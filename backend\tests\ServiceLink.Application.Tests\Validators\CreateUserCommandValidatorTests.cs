using FluentAssertions;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Validators;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Tests.Validators;

/// <summary>
/// Tests unitaires pour CreateUserCommandValidator
/// </summary>
public class CreateUserCommandValidatorTests
{
    private readonly CreateUserCommandValidator _validator;

    public CreateUserCommandValidatorTests()
    {
        _validator = new CreateUserCommandValidator();
    }

    [Fact]
    public void Validate_WithValidCommand_ShouldBeValid()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            PhoneNumber = "+33123456789",
            Password = "SecurePassword123!"
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Validate_WithInvalidEmail_ShouldBeInvalid(string invalidEmail)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = invalidEmail,
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.Email));
    }

    [Theory]
    [InlineData("invalid-email")]
    [InlineData("@example.com")]
    [InlineData("test@")]
    [InlineData("<EMAIL>")]
    public void Validate_WithInvalidEmailFormat_ShouldBeInvalid(string invalidEmail)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = invalidEmail,
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.Email));
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("A")]
    [InlineData(null)]
    public void Validate_WithInvalidFirstName_ShouldBeInvalid(string invalidFirstName)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = invalidFirstName,
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.FirstName));
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("A")]
    [InlineData(null)]
    public void Validate_WithInvalidLastName_ShouldBeInvalid(string invalidLastName)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = invalidLastName,
            Role = UserRole.Client
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.LastName));
    }

    [Fact]
    public void Validate_WithTooLongFirstName_ShouldBeInvalid()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = new string('A', 101), // 101 characters
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.FirstName));
    }

    [Fact]
    public void Validate_WithTooLongLastName_ShouldBeInvalid()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = new string('A', 101), // 101 characters
            Role = UserRole.Client
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.LastName));
    }

    [Theory]
    [InlineData("invalid-phone")]
    [InlineData("123")]
    [InlineData("+33")]
    [InlineData("0123456789")] // French format without country code
    public void Validate_WithInvalidPhoneNumber_ShouldBeInvalid(string invalidPhoneNumber)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            PhoneNumber = invalidPhoneNumber
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.PhoneNumber));
    }

    [Theory]
    [InlineData("+33123456789")]
    [InlineData("+1234567890")]
    [InlineData("+49123456789")]
    [InlineData("+86123456789")]
    public void Validate_WithValidPhoneNumber_ShouldBeValid(string validPhoneNumber)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            PhoneNumber = validPhoneNumber
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("weak")]
    [InlineData("12345")]
    [InlineData("password")]
    [InlineData("PASSWORD")]
    [InlineData("Password")]
    [InlineData("Password1")]
    public void Validate_WithWeakPassword_ShouldBeInvalid(string weakPassword)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            Password = weakPassword
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.Password));
    }

    [Theory]
    [InlineData("SecurePassword123!")]
    [InlineData("MyP@ssw0rd")]
    [InlineData("Str0ng!P@ssw0rd")]
    [InlineData("C0mpl3x!P@ssw0rd")]
    public void Validate_WithStrongPassword_ShouldBeValid(string strongPassword)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            Password = strongPassword
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithoutPassword_ShouldBeValid()
    {
        // Arrange - Password is optional for admin-created users
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client
            // No password provided
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithoutPhoneNumber_ShouldBeValid()
    {
        // Arrange - Phone number is optional
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client
            // No phone number provided
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithAllRoles_ShouldBeValid()
    {
        // Arrange & Act & Assert
        foreach (UserRole role in Enum.GetValues<UserRole>())
        {
            var command = new CreateUserCommand
            {
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe",
                Role = role
            };

            var result = _validator.Validate(command);
            result.IsValid.Should().BeTrue($"Role {role} should be valid");
        }
    }

    [Fact]
    public void Validate_WithMultipleErrors_ShouldReturnAllErrors()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "invalid-email",
            FirstName = "A", // Too short
            LastName = "", // Empty
            Role = UserRole.Client,
            PhoneNumber = "invalid-phone",
            Password = "weak"
        };

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().HaveCountGreaterThan(1);
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.Email));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.FirstName));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.LastName));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.PhoneNumber));
        result.Errors.Should().Contain(e => e.PropertyName == nameof(CreateUserCommand.Password));
    }
}
