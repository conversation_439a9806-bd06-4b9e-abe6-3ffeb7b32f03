# Script d'arrêt PowerShell pour ServiceLink
# Usage: .\scripts\stop.ps1 [-Environment development|staging|production] [-CleanVolumes]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("development", "staging", "production")]
    [string]$Environment = "development",
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanVolumes = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$RemoveImages = $false
)

# Fonctions utilitaires
function Write-Log {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ✓ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ⚠ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ✗ $Message" -ForegroundColor Red
}

# Arrêter les conteneurs
function Stop-Containers {
    Write-Log "Arrêt des conteneurs..."
    
    try {
        if ($Environment -eq "development") {
            docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
        }
        else {
            docker-compose down
        }
        Write-Success "Conteneurs arrêtés"
    }
    catch {
        Write-Error "Erreur lors de l'arrêt des conteneurs: $_"
    }
}

# Nettoyer les volumes
function Remove-Volumes {
    if ($CleanVolumes) {
        Write-Warning "Suppression des volumes (données perdues définitivement)..."
        
        $confirmation = Read-Host "Êtes-vous sûr de vouloir supprimer tous les volumes ? (y/N)"
        if ($confirmation -eq "y" -or $confirmation -eq "Y") {
            try {
                if ($Environment -eq "development") {
                    docker-compose -f docker-compose.yml -f docker-compose.dev.yml down -v
                }
                else {
                    docker-compose down -v
                }
                Write-Success "Volumes supprimés"
            }
            catch {
                Write-Error "Erreur lors de la suppression des volumes: $_"
            }
        }
        else {
            Write-Log "Suppression des volumes annulée"
        }
    }
}

# Supprimer les images
function Remove-Images {
    if ($RemoveImages) {
        Write-Warning "Suppression des images Docker..."
        
        try {
            # Supprimer les images du projet
            $images = docker images --filter "reference=servicelink*" -q
            if ($images) {
                docker rmi $images -f
                Write-Success "Images ServiceLink supprimées"
            }
            else {
                Write-Log "Aucune image ServiceLink trouvée"
            }
        }
        catch {
            Write-Error "Erreur lors de la suppression des images: $_"
        }
    }
}

# Nettoyer les ressources Docker inutilisées
function Clean-DockerResources {
    Write-Log "Nettoyage des ressources Docker inutilisées..."
    
    try {
        docker system prune -f
        Write-Success "Ressources Docker nettoyées"
    }
    catch {
        Write-Error "Erreur lors du nettoyage: $_"
    }
}

# Afficher le statut des conteneurs
function Show-Status {
    Write-Log "Statut des conteneurs ServiceLink:"
    
    try {
        $containers = docker ps -a --filter "name=servicelink" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        if ($containers) {
            Write-Host $containers
        }
        else {
            Write-Log "Aucun conteneur ServiceLink trouvé"
        }
    }
    catch {
        Write-Error "Erreur lors de la récupération du statut: $_"
    }
}

# Fonction principale
function Main {
    Write-Log "Arrêt de ServiceLink"
    Write-Log "Environnement: $Environment"
    
    if ($CleanVolumes) {
        Write-Warning "Mode de nettoyage des volumes activé"
    }
    
    if ($RemoveImages) {
        Write-Warning "Mode de suppression des images activé"
    }
    
    Stop-Containers
    Remove-Volumes
    Remove-Images
    Clean-DockerResources
    Show-Status
    
    Write-Success "Arrêt terminé"
}

# Exécuter le script principal
Main
