using ServiceLink.Domain.Events;

namespace ServiceLink.Domain.Entities;

/// <summary>
/// Entité de base pour toutes les entités du domaine ServiceLink
/// Fournit les propriétés communes et la gestion des événements de domaine
/// </summary>
public abstract class BaseEntity
{
    private readonly List<IDomainEvent> _domainEvents = new();

    /// <summary>
    /// Identifiant unique de l'entité
    /// </summary>
    public Guid Id { get; protected set; } = Guid.NewGuid();

    /// <summary>
    /// Date de création de l'entité
    /// </summary>
    public DateTime CreatedAt { get; protected set; } = DateTime.UtcNow;

    /// <summary>
    /// Date de dernière modification de l'entité
    /// </summary>
    public DateTime UpdatedAt { get; protected set; } = DateTime.UtcNow;

    /// <summary>
    /// Identifiant de l'utilisateur qui a créé l'entité
    /// </summary>
    public Guid? CreatedBy { get; protected set; }

    /// <summary>
    /// Identifiant de l'utilisateur qui a modifié l'entité en dernier
    /// </summary>
    public Guid? UpdatedBy { get; protected set; }

    /// <summary>
    /// Indique si l'entité est supprimée (soft delete)
    /// </summary>
    public bool IsDeleted { get; protected set; } = false;

    /// <summary>
    /// Date de suppression de l'entité
    /// </summary>
    public DateTime? DeletedAt { get; protected set; }

    /// <summary>
    /// Identifiant de l'utilisateur qui a supprimé l'entité
    /// </summary>
    public Guid? DeletedBy { get; protected set; }

    /// <summary>
    /// Version de l'entité pour la gestion de la concurrence optimiste
    /// </summary>
    public byte[] RowVersion { get; protected set; } = Array.Empty<byte>();

    /// <summary>
    /// Événements de domaine associés à cette entité
    /// </summary>
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Ajoute un événement de domaine à l'entité
    /// </summary>
    /// <param name="domainEvent">L'événement de domaine à ajouter</param>
    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Supprime un événement de domaine de l'entité
    /// </summary>
    /// <param name="domainEvent">L'événement de domaine à supprimer</param>
    protected void RemoveDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Remove(domainEvent);
    }

    /// <summary>
    /// Efface tous les événements de domaine de l'entité
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Met à jour les métadonnées de modification
    /// </summary>
    /// <param name="updatedBy">Identifiant de l'utilisateur qui modifie</param>
    public virtual void UpdateMetadata(Guid? updatedBy = null)
    {
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
    }

    /// <summary>
    /// Marque l'entité comme supprimée (soft delete)
    /// </summary>
    /// <param name="deletedBy">Identifiant de l'utilisateur qui supprime</param>
    public virtual void SoftDelete(Guid? deletedBy = null)
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        DeletedBy = deletedBy;
        UpdateMetadata(deletedBy);
    }

    /// <summary>
    /// Restaure une entité supprimée
    /// </summary>
    /// <param name="restoredBy">Identifiant de l'utilisateur qui restaure</param>
    public virtual void Restore(Guid? restoredBy = null)
    {
        IsDeleted = false;
        DeletedAt = null;
        DeletedBy = null;
        UpdateMetadata(restoredBy);
    }

    /// <summary>
    /// Définit l'utilisateur créateur lors de la création
    /// </summary>
    /// <param name="createdBy">Identifiant de l'utilisateur créateur</param>
    protected void SetCreatedBy(Guid? createdBy)
    {
        CreatedBy = createdBy;
        UpdatedBy = createdBy;
    }

    /// <summary>
    /// Égalité basée sur l'identifiant
    /// </summary>
    public override bool Equals(object? obj)
    {
        if (obj is not BaseEntity other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        if (GetType() != other.GetType())
            return false;

        return Id == other.Id;
    }

    /// <summary>
    /// Hash code basé sur l'identifiant
    /// </summary>
    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    /// <summary>
    /// Opérateur d'égalité
    /// </summary>
    public static bool operator ==(BaseEntity? left, BaseEntity? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Opérateur d'inégalité
    /// </summary>
    public static bool operator !=(BaseEntity? left, BaseEntity? right)
    {
        return !Equals(left, right);
    }
}
