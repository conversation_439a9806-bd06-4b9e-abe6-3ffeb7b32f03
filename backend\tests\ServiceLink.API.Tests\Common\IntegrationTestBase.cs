using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ServiceLink.Infrastructure.Data;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace ServiceLink.API.Tests.Common;

/// <summary>
/// Classe de base pour les tests d'intégration API
/// </summary>
public abstract class IntegrationTestBase : IClassFixture<WebApplicationFactory<Program>>, IDisposable
{
    protected readonly WebApplicationFactory<Program> Factory;
    protected readonly HttpClient Client;
    protected readonly ServiceLinkDbContext DbContext;

    protected IntegrationTestBase(WebApplicationFactory<Program> factory)
    {
        Factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<ServiceLinkDbContext>));
                if (descriptor != null)
                {
                    services.Remove(descriptor);
                }

                // Add InMemory database for testing
                services.AddDbContext<ServiceLinkDbContext>(options =>
                {
                    options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}");
                });

                // Disable logging for cleaner test output
                services.AddLogging(builder => builder.SetMinimumLevel(LogLevel.Warning));
            });

            builder.UseEnvironment("Testing");
        });

        Client = Factory.CreateClient();
        
        // Get DbContext from the test server
        var scope = Factory.Services.CreateScope();
        DbContext = scope.ServiceProvider.GetRequiredService<ServiceLinkDbContext>();
        
        // Ensure database is created
        DbContext.Database.EnsureCreated();
    }

    /// <summary>
    /// Sérialise un objet en JSON pour les requêtes HTTP
    /// </summary>
    protected static StringContent CreateJsonContent(object obj)
    {
        var json = JsonSerializer.Serialize(obj, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
        return new StringContent(json, Encoding.UTF8, "application/json");
    }

    /// <summary>
    /// Désérialise une réponse HTTP JSON
    /// </summary>
    protected static async Task<T?> DeserializeResponse<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(content, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
    }

    /// <summary>
    /// Ajoute un token JWT aux headers d'autorisation
    /// </summary>
    protected void SetAuthorizationHeader(string token)
    {
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    /// <summary>
    /// Supprime les headers d'autorisation
    /// </summary>
    protected void ClearAuthorizationHeader()
    {
        Client.DefaultRequestHeaders.Authorization = null;
    }

    /// <summary>
    /// Génère un token JWT pour les tests (simplifié)
    /// </summary>
    protected string GenerateTestJwtToken(Guid userId, string email, string role = "Client")
    {
        // Pour les tests, on peut utiliser un token simplifié ou mocker le service JWT
        // Ici on retourne un token factice - dans un vrai test, on utiliserait le JwtService
        return "test-jwt-token";
    }

    /// <summary>
    /// Nettoie la base de données entre les tests
    /// </summary>
    protected async Task CleanupDatabase()
    {
        // Remove all entities
        DbContext.Users.RemoveRange(DbContext.Users);
        await DbContext.SaveChangesAsync();
    }

    /// <summary>
    /// Vérifie qu'une réponse HTTP est réussie
    /// </summary>
    protected static void AssertSuccessResponse(HttpResponseMessage response)
    {
        if (!response.IsSuccessStatusCode)
        {
            var content = response.Content.ReadAsStringAsync().Result;
            throw new Exception($"Expected success status code, but got {response.StatusCode}. Content: {content}");
        }
    }

    /// <summary>
    /// Vérifie qu'une réponse HTTP a un code d'erreur spécifique
    /// </summary>
    protected static void AssertErrorResponse(HttpResponseMessage response, System.Net.HttpStatusCode expectedStatusCode)
    {
        if (response.StatusCode != expectedStatusCode)
        {
            var content = response.Content.ReadAsStringAsync().Result;
            throw new Exception($"Expected status code {expectedStatusCode}, but got {response.StatusCode}. Content: {content}");
        }
    }

    public virtual void Dispose()
    {
        DbContext?.Dispose();
        Client?.Dispose();
        Factory?.Dispose();
    }
}

/// <summary>
/// Factory personnalisée pour les tests d'intégration
/// </summary>
public class CustomWebApplicationFactory : WebApplicationFactory<Program>
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<ServiceLinkDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add InMemory database for testing
            services.AddDbContext<ServiceLinkDbContext>(options =>
            {
                options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}");
            });

            // Override other services for testing if needed
            // For example, mock email service, etc.
        });

        builder.UseEnvironment("Testing");
    }
}

/// <summary>
/// Modèles pour les réponses d'erreur API
/// </summary>
public class ApiErrorResponse
{
    public int Status { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Detail { get; set; } = string.Empty;
    public string TraceId { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, string[]>? Errors { get; set; }
}

/// <summary>
/// Modèles pour les réponses de validation
/// </summary>
public class ValidationErrorResponse : ApiErrorResponse
{
    public new Dictionary<string, string[]> Errors { get; set; } = new();
}

/// <summary>
/// Modèle pour les réponses paginées
/// </summary>
public class PagedResponse<T>
{
    public IEnumerable<T> Items { get; set; } = new List<T>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
}

/// <summary>
/// Extensions utilitaires pour les tests
/// </summary>
public static class TestExtensions
{
    /// <summary>
    /// Vérifie qu'une réponse contient des erreurs de validation spécifiques
    /// </summary>
    public static async Task ShouldHaveValidationError(this HttpResponseMessage response, string propertyName)
    {
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);
        
        var errorResponse = await IntegrationTestBase.DeserializeResponse<ValidationErrorResponse>(response);
        errorResponse.Should().NotBeNull();
        errorResponse!.Errors.Should().ContainKey(propertyName);
    }

    /// <summary>
    /// Vérifie qu'une réponse est une erreur 401 Unauthorized
    /// </summary>
    public static void ShouldBeUnauthorized(this HttpResponseMessage response)
    {
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.Unauthorized);
    }

    /// <summary>
    /// Vérifie qu'une réponse est une erreur 403 Forbidden
    /// </summary>
    public static void ShouldBeForbidden(this HttpResponseMessage response)
    {
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.Forbidden);
    }

    /// <summary>
    /// Vérifie qu'une réponse est une erreur 404 Not Found
    /// </summary>
    public static void ShouldBeNotFound(this HttpResponseMessage response)
    {
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.NotFound);
    }

    /// <summary>
    /// Vérifie qu'une réponse est une erreur 409 Conflict
    /// </summary>
    public static void ShouldBeConflict(this HttpResponseMessage response)
    {
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.Conflict);
    }
}
