using FluentAssertions;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Domain.Tests.Enums;

/// <summary>
/// Tests unitaires pour l'énumération UserRole et ses extensions
/// </summary>
public class UserRoleTests
{
    [Theory]
    [InlineData(UserRole.Admin, true)]
    [InlineData(UserRole.Support, true)]
    [InlineData(UserRole.Manager, true)]
    [InlineData(UserRole.Supervisor, true)]
    [InlineData(UserRole.Client, false)]
    [InlineData(UserRole.Provider, false)]
    public void IsAdministrative_ShouldReturnCorrectResult(UserRole role, bool expected)
    {
        // Act
        var result = role.IsAdministrative();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(UserRole.Client, true)]
    [InlineData(UserRole.Provider, true)]
    [InlineData(UserRole.Admin, false)]
    [InlineData(UserRole.Support, false)]
    [InlineData(UserRole.Manager, false)]
    [InlineData(UserRole.Supervisor, false)]
    public void IsEndUser_ShouldReturnCorrectResult(UserRole role, bool expected)
    {
        // Act
        var result = role.IsEndUser();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(UserRole.Client, "Client - Recherche et réserve des services")]
    [InlineData(UserRole.Provider, "Prestataire - Fournit des services à domicile")]
    [InlineData(UserRole.Admin, "Administrateur - Accès complet au système")]
    [InlineData(UserRole.Support, "Support - Assistance aux utilisateurs")]
    [InlineData(UserRole.Manager, "Manager - Gestion des réclamations et litiges")]
    [InlineData(UserRole.Supervisor, "Superviseur - Validation des prestataires")]
    public void GetDescription_ShouldReturnCorrectDescription(UserRole role, string expectedDescription)
    {
        // Act
        var description = role.GetDescription();

        // Assert
        description.Should().Be(expectedDescription);
    }

    [Fact]
    public void GetPermissions_Client_ShouldReturnClientPermissions()
    {
        // Act
        var permissions = UserRole.Client.GetPermissions().ToList();

        // Assert
        permissions.Should().Contain("search_providers");
        permissions.Should().Contain("create_booking");
        permissions.Should().Contain("make_payment");
        permissions.Should().Contain("leave_review");
        permissions.Should().Contain("view_own_bookings");
        permissions.Should().Contain("cancel_booking");
        permissions.Should().HaveCount(6);
    }

    [Fact]
    public void GetPermissions_Provider_ShouldReturnProviderPermissions()
    {
        // Act
        var permissions = UserRole.Provider.GetPermissions().ToList();

        // Assert
        permissions.Should().Contain("manage_profile");
        permissions.Should().Contain("manage_services");
        permissions.Should().Contain("accept_booking");
        permissions.Should().Contain("receive_payment");
        permissions.Should().Contain("view_own_bookings");
        permissions.Should().Contain("respond_to_reviews");
        permissions.Should().HaveCount(6);
    }

    [Fact]
    public void GetPermissions_Admin_ShouldReturnAdminPermissions()
    {
        // Act
        var permissions = UserRole.Admin.GetPermissions().ToList();

        // Assert
        permissions.Should().Contain("manage_all_users");
        permissions.Should().Contain("manage_all_bookings");
        permissions.Should().Contain("manage_all_payments");
        permissions.Should().Contain("system_configuration");
        permissions.Should().Contain("view_analytics");
        permissions.Should().Contain("manage_categories");
        permissions.Should().Contain("manage_complaints");
        permissions.Should().Contain("moderate_content");
        permissions.Should().HaveCount(8);
    }

    [Fact]
    public void GetPermissions_Support_ShouldReturnSupportPermissions()
    {
        // Act
        var permissions = UserRole.Support.GetPermissions().ToList();

        // Assert
        permissions.Should().Contain("view_user_data");
        permissions.Should().Contain("manage_support_tickets");
        permissions.Should().Contain("chat_with_users");
        permissions.Should().Contain("view_basic_analytics");
        permissions.Should().Contain("escalate_issues");
        permissions.Should().HaveCount(5);
    }

    [Fact]
    public void GetPermissions_Manager_ShouldReturnManagerPermissions()
    {
        // Act
        var permissions = UserRole.Manager.GetPermissions().ToList();

        // Assert
        permissions.Should().Contain("manage_complaints");
        permissions.Should().Contain("arbitrate_disputes");
        permissions.Should().Contain("apply_sanctions");
        permissions.Should().Contain("view_complaint_analytics");
        permissions.Should().Contain("manage_refunds");
        permissions.Should().HaveCount(5);
    }

    [Fact]
    public void GetPermissions_Supervisor_ShouldReturnSupervisorPermissions()
    {
        // Act
        var permissions = UserRole.Supervisor.GetPermissions().ToList();

        // Assert
        permissions.Should().Contain("validate_provider_documents");
        permissions.Should().Contain("approve_provider_profiles");
        permissions.Should().Contain("moderate_services");
        permissions.Should().Contain("suspend_providers");
        permissions.Should().Contain("view_provider_analytics");
        permissions.Should().HaveCount(5);
    }

    [Theory]
    [InlineData(UserRole.Client, "search_providers", true)]
    [InlineData(UserRole.Client, "manage_all_users", false)]
    [InlineData(UserRole.Provider, "manage_profile", true)]
    [InlineData(UserRole.Provider, "search_providers", false)]
    [InlineData(UserRole.Admin, "manage_all_users", true)]
    [InlineData(UserRole.Admin, "search_providers", true)] // Admin has all permissions
    [InlineData(UserRole.Support, "view_user_data", true)]
    [InlineData(UserRole.Support, "manage_all_users", false)]
    public void HasPermission_ShouldReturnCorrectResult(UserRole role, string permission, bool expected)
    {
        // Act
        var result = role.HasPermission(permission);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void HasPermission_Admin_ShouldHaveAllPermissions()
    {
        // Arrange
        var allPermissions = new[]
        {
            "search_providers", "create_booking", "manage_profile", "manage_services",
            "manage_all_users", "view_user_data", "manage_complaints", "validate_provider_documents"
        };

        // Act & Assert
        foreach (var permission in allPermissions)
        {
            UserRole.Admin.HasPermission(permission).Should().BeTrue($"Admin should have permission: {permission}");
        }
    }

    [Theory]
    [InlineData(UserRole.Admin, 100)]
    [InlineData(UserRole.Manager, 80)]
    [InlineData(UserRole.Supervisor, 70)]
    [InlineData(UserRole.Support, 60)]
    [InlineData(UserRole.Provider, 20)]
    [InlineData(UserRole.Client, 10)]
    public void GetPriorityLevel_ShouldReturnCorrectPriority(UserRole role, int expectedPriority)
    {
        // Act
        var priority = role.GetPriorityLevel();

        // Assert
        priority.Should().Be(expectedPriority);
    }

    [Fact]
    public void GetPriorityLevel_ShouldHaveCorrectHierarchy()
    {
        // Act & Assert
        UserRole.Admin.GetPriorityLevel().Should().BeGreaterThan(UserRole.Manager.GetPriorityLevel());
        UserRole.Manager.GetPriorityLevel().Should().BeGreaterThan(UserRole.Supervisor.GetPriorityLevel());
        UserRole.Supervisor.GetPriorityLevel().Should().BeGreaterThan(UserRole.Support.GetPriorityLevel());
        UserRole.Support.GetPriorityLevel().Should().BeGreaterThan(UserRole.Provider.GetPriorityLevel());
        UserRole.Provider.GetPriorityLevel().Should().BeGreaterThan(UserRole.Client.GetPriorityLevel());
    }

    [Theory]
    [InlineData("SEARCH_PROVIDERS")]
    [InlineData("Search_Providers")]
    [InlineData("search_providers")]
    public void HasPermission_ShouldBeCaseInsensitive(string permission)
    {
        // Act
        var result = UserRole.Client.HasPermission(permission);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void HasPermission_WithNonExistentPermission_ShouldReturnFalse()
    {
        // Act
        var result = UserRole.Client.HasPermission("non_existent_permission");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void HasPermission_WithEmptyPermission_ShouldReturnFalse()
    {
        // Act
        var result = UserRole.Client.HasPermission("");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void AllRoles_ShouldHaveUniqueValues()
    {
        // Arrange
        var roles = Enum.GetValues<UserRole>().Cast<int>().ToList();

        // Act & Assert
        roles.Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public void AllRoles_ShouldHaveDescriptions()
    {
        // Arrange
        var roles = Enum.GetValues<UserRole>();

        // Act & Assert
        foreach (var role in roles)
        {
            var description = role.GetDescription();
            description.Should().NotBeNullOrEmpty();
            description.Should().NotBe("Rôle inconnu");
        }
    }

    [Fact]
    public void AllRoles_ShouldHavePermissions()
    {
        // Arrange
        var roles = Enum.GetValues<UserRole>();

        // Act & Assert
        foreach (var role in roles)
        {
            var permissions = role.GetPermissions();
            permissions.Should().NotBeNull();
            permissions.Should().NotBeEmpty();
        }
    }

    [Fact]
    public void AllRoles_ShouldHaveValidPriorityLevels()
    {
        // Arrange
        var roles = Enum.GetValues<UserRole>();

        // Act & Assert
        foreach (var role in roles)
        {
            var priority = role.GetPriorityLevel();
            priority.Should().BeGreaterThan(0);
        }
    }
}
