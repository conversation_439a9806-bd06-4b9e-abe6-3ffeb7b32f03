using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Events;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Configuration;
using System.Reflection;

namespace ServiceLink.Infrastructure.Data;

/// <summary>
/// Contexte de base de données principal pour ServiceLink
/// Implémente les patterns Repository et Unit of Work
/// </summary>
public class ServiceLinkDbContext : DbContext
{
    /// <summary>
    /// Constructeur avec options de configuration
    /// </summary>
    /// <param name="options">Options de configuration du contexte</param>
    public ServiceLinkDbContext(DbContextOptions<ServiceLinkDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Table des utilisateurs
    /// </summary>
    public DbSet<User> Users { get; set; } = null!;

    /// <summary>
    /// Configuration du modèle de données
    /// </summary>
    /// <param name="modelBuilder">Builder pour configurer le modèle</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Application automatique de toutes les configurations
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Configuration globale pour les entités supprimées (soft delete)
        ConfigureSoftDelete(modelBuilder);

        // Configuration des index globaux
        ConfigureGlobalIndexes(modelBuilder);

        // Configuration des contraintes globales
        ConfigureGlobalConstraints(modelBuilder);
    }

    /// <summary>
    /// Sauvegarde les changements avec gestion des événements de domaine
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre d'entités affectées</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Mise à jour des propriétés d'audit avant sauvegarde
        UpdateAuditProperties();

        // Collecte des événements de domaine avant sauvegarde
        var domainEvents = CollectDomainEvents();

        // Sauvegarde des changements
        var result = await base.SaveChangesAsync(cancellationToken);

        // Publication des événements de domaine après sauvegarde réussie
        await PublishDomainEventsAsync(domainEvents, cancellationToken);

        return result;
    }

    /// <summary>
    /// Sauvegarde synchrone avec gestion des événements
    /// </summary>
    /// <returns>Nombre d'entités affectées</returns>
    public override int SaveChanges()
    {
        // Mise à jour des propriétés d'audit
        UpdateAuditProperties();

        // Pour la version synchrone, on ne gère pas les événements de domaine
        // car ils nécessitent des appels asynchrones
        return base.SaveChanges();
    }

    /// <summary>
    /// Met à jour les propriétés d'audit des entités
    /// </summary>
    private void UpdateAuditProperties()
    {
        var entries = ChangeTracker.Entries<BaseEntity>()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            var entity = entry.Entity;
            var now = DateTime.UtcNow;

            if (entry.State == EntityState.Added)
            {
                entity.SetCreatedAt(now);
            }
            else if (entry.State == EntityState.Modified)
            {
                entity.SetUpdatedAt(now);
            }
        }
    }

    /// <summary>
    /// Collecte tous les événements de domaine des entités trackées
    /// </summary>
    /// <returns>Liste des événements de domaine</returns>
    private List<IDomainEvent> CollectDomainEvents()
    {
        var domainEvents = new List<IDomainEvent>();

        var entitiesWithEvents = ChangeTracker.Entries<BaseEntity>()
            .Where(e => e.Entity.DomainEvents.Any())
            .Select(e => e.Entity)
            .ToList();

        foreach (var entity in entitiesWithEvents)
        {
            domainEvents.AddRange(entity.DomainEvents);
            entity.ClearDomainEvents();
        }

        return domainEvents;
    }

    /// <summary>
    /// Publie les événements de domaine via MediatR
    /// </summary>
    /// <param name="domainEvents">Événements à publier</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    private async Task PublishDomainEventsAsync(List<IDomainEvent> domainEvents, CancellationToken cancellationToken)
    {
        // Note: L'injection de MediatR sera configurée dans le DI container
        // Pour l'instant, on log les événements
        foreach (var domainEvent in domainEvents)
        {
            // TODO: Injecter IMediator et publier les événements
            // await _mediator.Publish(domainEvent, cancellationToken);
            
            // Log temporaire
            Console.WriteLine($"Domain Event: {domainEvent.GetType().Name} - {domainEvent.EventId}");
        }
    }

    /// <summary>
    /// Configure le soft delete pour toutes les entités BaseEntity
    /// </summary>
    /// <param name="modelBuilder">Builder du modèle</param>
    private static void ConfigureSoftDelete(ModelBuilder modelBuilder)
    {
        // Filtre global pour exclure les entités supprimées
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                var method = typeof(ServiceLinkDbContext)
                    .GetMethod(nameof(SetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)!
                    .MakeGenericMethod(entityType.ClrType);

                method.Invoke(null, new object[] { modelBuilder });
            }
        }
    }

    /// <summary>
    /// Configure le filtre de soft delete pour un type d'entité spécifique
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    /// <param name="modelBuilder">Builder du modèle</param>
    private static void SetSoftDeleteFilter<T>(ModelBuilder modelBuilder) where T : BaseEntity
    {
        modelBuilder.Entity<T>().HasQueryFilter(e => !e.IsDeleted);
    }

    /// <summary>
    /// Configure les index globaux pour les performances
    /// </summary>
    /// <param name="modelBuilder">Builder du modèle</param>
    private static void ConfigureGlobalIndexes(ModelBuilder modelBuilder)
    {
        // Index sur les propriétés d'audit communes
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                var entity = modelBuilder.Entity(entityType.ClrType);
                
                // Index sur CreatedAt pour les requêtes temporelles
                entity.HasIndex(nameof(BaseEntity.CreatedAt))
                      .HasDatabaseName($"IX_{entityType.GetTableName()}_CreatedAt");

                // Index sur IsDeleted pour le soft delete
                entity.HasIndex(nameof(BaseEntity.IsDeleted))
                      .HasDatabaseName($"IX_{entityType.GetTableName()}_IsDeleted");
            }
        }
    }

    /// <summary>
    /// Configure les contraintes globales
    /// </summary>
    /// <param name="modelBuilder">Builder du modèle</param>
    private static void ConfigureGlobalConstraints(ModelBuilder modelBuilder)
    {
        // Configuration des contraintes de longueur par défaut
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(string))
                {
                    // Longueur par défaut pour les chaînes si non spécifiée
                    if (property.GetMaxLength() == null)
                    {
                        property.SetMaxLength(500);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Configure la base de données pour le développement
    /// </summary>
    /// <param name="optionsBuilder">Builder des options</param>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // Configuration par défaut pour le développement
            optionsBuilder.UseNpgsql("Host=localhost;Database=ServiceLink;Username=********;Password=********");
            
            // Configuration pour le développement
            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.EnableDetailedErrors();
        }
    }
}
