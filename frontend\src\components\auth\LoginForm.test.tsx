import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, createTestUser } from '../../test/utils'
import { LoginForm } from './LoginForm'

// Mock du contexte d'authentification
const mockLogin = vi.fn()
const mockAuthContext = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  login: mockLogin,
  register: vi.fn(),
  logout: vi.fn(),
  refreshToken: vi.fn(),
  updateProfile: vi.fn(),
}

vi.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}))

describe('LoginForm', () => {
  const user = userEvent.setup()
  const mockOnSuccess = vi.fn()
  const mockOnRegisterClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockAuthContext.isLoading = false
  })

  it('renders login form correctly', () => {
    render(<LoginForm />)

    expect(screen.getByText('Connexion')).toBeInTheDocument()
    expect(screen.getByText('Connectez-vous à votre compte ServiceLink')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Mot de passe')).toBeInTheDocument()
    expect(screen.getByRole('checkbox', { name: /se souvenir de moi/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /se connecter/i })).toBeInTheDocument()
  })

  it('shows validation errors for empty fields', async () => {
    render(<LoginForm />)

    const submitButton = screen.getByRole('button', { name: /se connecter/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('L\'email est requis')).toBeInTheDocument()
      expect(screen.getByText('Le mot de passe est requis')).toBeInTheDocument()
    })
  })

  it('shows validation error for invalid email format', async () => {
    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    await user.type(emailInput, 'invalid-email')

    const submitButton = screen.getByRole('button', { name: /se connecter/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Format d\'email invalide')).toBeInTheDocument()
    })
  })

  it('shows validation error for short password', async () => {
    render(<LoginForm />)

    const passwordInput = screen.getByLabelText('Mot de passe')
    await user.type(passwordInput, '123')

    const submitButton = screen.getByRole('button', { name: /se connecter/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Le mot de passe doit contenir au moins 6 caractères')).toBeInTheDocument()
    })
  })

  it('toggles password visibility', async () => {
    render(<LoginForm />)

    const passwordInput = screen.getByLabelText('Mot de passe') as HTMLInputElement
    const toggleButton = screen.getByRole('button', { name: '' }) // Le bouton eye/eyeoff

    expect(passwordInput.type).toBe('password')

    await user.click(toggleButton)
    expect(passwordInput.type).toBe('text')

    await user.click(toggleButton)
    expect(passwordInput.type).toBe('password')
  })

  it('submits form with valid data', async () => {
    mockLogin.mockResolvedValueOnce(undefined)
    render(<LoginForm onSuccess={mockOnSuccess} />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Mot de passe')
    const rememberMeCheckbox = screen.getByRole('checkbox', { name: /se souvenir de moi/i })
    const submitButton = screen.getByRole('button', { name: /se connecter/i })

    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.click(rememberMeCheckbox)
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: true,
      })
      expect(mockOnSuccess).toHaveBeenCalled()
    })
  })

  it('shows loading state during submission', async () => {
    mockAuthContext.isLoading = true
    render(<LoginForm />)

    const submitButton = screen.getByRole('button', { name: /se connecter/i })
    expect(submitButton).toBeDisabled()
    expect(screen.getByText('Se connecter')).toBeInTheDocument()
  })

  it('handles login error', async () => {
    const errorMessage = 'Email ou mot de passe incorrect'
    mockLogin.mockRejectedValueOnce({
      statusCode: 401,
      message: errorMessage,
    })

    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Mot de passe')
    const submitButton = screen.getByRole('button', { name: /se connecter/i })

    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'wrongpassword')
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Email ou mot de passe incorrect')).toBeInTheDocument()
    })
  })

  it('handles validation errors from server', async () => {
    mockLogin.mockRejectedValueOnce({
      statusCode: 400,
      errors: {
        email: ['Email format is invalid'],
        password: ['Password is too weak'],
      },
    })

    render(<LoginForm />)

    const emailInput = screen.getByLabelText('Email')
    const passwordInput = screen.getByLabelText('Mot de passe')
    const submitButton = screen.getByRole('button', { name: /se connecter/i })

    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Email format is invalid')).toBeInTheDocument()
      expect(screen.getByText('Password is too weak')).toBeInTheDocument()
    })
  })

  it('calls onRegisterClick when register link is clicked', async () => {
    render(<LoginForm onRegisterClick={mockOnRegisterClick} />)

    const registerLink = screen.getByRole('button', { name: /créer un compte/i })
    await user.click(registerLink)

    expect(mockOnRegisterClick).toHaveBeenCalled()
  })

  it('navigates to forgot password', async () => {
    render(<LoginForm />)

    const forgotPasswordLink = screen.getByRole('button', { name: /mot de passe oublié/i })
    expect(forgotPasswordLink).toBeInTheDocument()
    
    // Pour l'instant, on vérifie juste que le lien existe
    // La navigation sera implémentée plus tard
  })
})
