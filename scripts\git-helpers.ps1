﻿# ServiceLink Git Helper Scripts
# PowerShell scripts pour faciliter le workflow Git

# Fonction pour créer une nouvelle feature branch
function New-FeatureBranch {
    param(
        [Parameter(Mandatory=$true)]
        [string]$FeatureName,
        
        [Parameter(Mandatory=$false)]
        [string]$Scope = ""
    )
    
    $branchName = if ($Scope) { "feature/$Scope/$FeatureName" } else { "feature/$FeatureName" }
    
    Write-Host "🌿 Création de la branche: $branchName" -ForegroundColor Green
    
    # S'assurer qu'on est sur develop et à jour
    git checkout develop
    git pull origin develop
    
    # Créer la nouvelle branche
    git checkout -b $branchName
    
    Write-Host "✅ Branche $branchName créée avec succès!" -ForegroundColor Green
    Write-Host "💡 N'oubliez pas de faire des commits réguliers avec les conventions:" -ForegroundColor Yellow
    Write-Host "   feat($Scope): description de la fonctionnalité" -ForegroundColor Cyan
}

# Fonction pour créer une hotfix branch
function New-HotfixBranch {
    param(
        [Parameter(Mandatory=$true)]
        [string]$HotfixName,
        
        [Parameter(Mandatory=$false)]
        [string]$Scope = ""
    )
    
    $branchName = if ($Scope) { "hotfix/$Scope/$HotfixName" } else { "hotfix/$HotfixName" }
    
    Write-Host "🔥 Création de la branche hotfix: $branchName" -ForegroundColor Red
    
    # S'assurer qu'on est sur main et à jour
    git checkout main
    git pull origin main
    
    # Créer la nouvelle branche
    git checkout -b $branchName
    
    Write-Host "✅ Branche hotfix $branchName créée avec succès!" -ForegroundColor Green
    Write-Host "⚠️  ATTENTION: Cette branche sera mergée vers main ET develop" -ForegroundColor Yellow
}

# Fonction pour créer une release branch
function New-ReleaseBranch {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Version
    )
    
    $branchName = "release/v$Version"
    
    Write-Host "🚀 Création de la branche release: $branchName" -ForegroundColor Blue
    
    # S'assurer qu'on est sur develop et à jour
    git checkout develop
    git pull origin develop
    
    # Créer la nouvelle branche
    git checkout -b $branchName
    
    Write-Host "✅ Branche release $branchName créée avec succès!" -ForegroundColor Green
    Write-Host "📝 Prochaines étapes:" -ForegroundColor Yellow
    Write-Host "   1. Mettre à jour les numéros de version" -ForegroundColor Cyan
    Write-Host "   2. Finaliser la documentation" -ForegroundColor Cyan
    Write-Host "   3. Effectuer les tests finaux" -ForegroundColor Cyan
    Write-Host "   4. Créer PR vers main" -ForegroundColor Cyan
}

# Fonction pour faire un commit avec les conventions
function Invoke-ConventionalCommit {
    param(
        [Parameter(Mandatory=$true)]
        [ValidateSet("feat", "fix", "docs", "style", "refactor", "perf", "test", "build", "ci", "chore", "revert")]
        [string]$Type,
        
        [Parameter(Mandatory=$false)]
        [string]$Scope = "",
        
        [Parameter(Mandatory=$true)]
        [string]$Description,
        
        [Parameter(Mandatory=$false)]
        [string]$Body = "",
        
        [Parameter(Mandatory=$false)]
        [switch]$BreakingChange
    )
    
    $commitMessage = if ($Scope) { "$Type($Scope): $Description" } else { "$Type: $Description" }
    
    if ($Body) {
        $commitMessage += "`n`n$Body"
    }
    
    if ($BreakingChange) {
        $commitMessage += "`n`nBREAKING CHANGE: $Description"
    }
    
    Write-Host "📝 Commit message:" -ForegroundColor Green
    Write-Host $commitMessage -ForegroundColor Cyan
    
    $confirm = Read-Host "Confirmer ce commit? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        git add .
        git commit -m $commitMessage
        Write-Host "✅ Commit effectué avec succès!" -ForegroundColor Green
    } else {
        Write-Host "❌ Commit annulé" -ForegroundColor Red
    }
}

# Fonction pour synchroniser avec develop
function Sync-WithDevelop {
    $currentBranch = git branch --show-current
    
    Write-Host "🔄 Synchronisation de $currentBranch avec develop..." -ForegroundColor Blue
    
    # Sauvegarder les changements en cours
    git stash push -m "Auto-stash before sync with develop"
    
    # Aller sur develop et récupérer les derniers changements
    git checkout develop
    git pull origin develop
    
    # Retourner sur la branche et rebaser
    git checkout $currentBranch
    git rebase develop
    
    # Restaurer les changements
    $stashList = git stash list
    if ($stashList -match "Auto-stash before sync with develop") {
        git stash pop
    }
    
    Write-Host "✅ Synchronisation terminée!" -ForegroundColor Green
}

# Fonction pour nettoyer les branches locales
function Remove-MergedBranches {
    Write-Host "🧹 Nettoyage des branches mergées..." -ForegroundColor Yellow
    
    # Lister les branches mergées (sauf main et develop)
    $mergedBranches = git branch --merged | Where-Object { $_ -notmatch "(main|develop|\*)" } | ForEach-Object { $_.Trim() }
    
    if ($mergedBranches.Count -eq 0) {
        Write-Host "✅ Aucune branche à nettoyer" -ForegroundColor Green
        return
    }
    
    Write-Host "Branches à supprimer:" -ForegroundColor Red
    $mergedBranches | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
    
    $confirm = Read-Host "Supprimer ces branches? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        $mergedBranches | ForEach-Object { git branch -d $_ }
        Write-Host "✅ Branches supprimées!" -ForegroundColor Green
    } else {
        Write-Host "❌ Nettoyage annulé" -ForegroundColor Red
    }
}

# Fonction pour afficher le statut du projet
function Show-ProjectStatus {
    Write-Host "📊 ServiceLink - Statut du Projet" -ForegroundColor Blue
    Write-Host "=================================" -ForegroundColor Blue
    
    # Branche actuelle
    $currentBranch = git branch --show-current
    Write-Host "🌿 Branche actuelle: $currentBranch" -ForegroundColor Green
    
    # Statut Git
    Write-Host "`n📋 Statut Git:" -ForegroundColor Yellow
    git status --short
    
    # Derniers commits
    Write-Host "`n📝 Derniers commits:" -ForegroundColor Yellow
    git log --oneline -5
    
    # Branches locales
    Write-Host "`n🌿 Branches locales:" -ForegroundColor Yellow
    git branch
    
    # Vérifier si les services sont en cours d'exécution
    Write-Host "`n🚀 Services:" -ForegroundColor Yellow
    
    $frontendProcess = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*vite*" }
    $backendProcess = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*ServiceLink.API*" }
    
    if ($frontendProcess) {
        Write-Host "  ✅ Frontend (React) - Running on http://localhost:5173" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Frontend (React) - Not running" -ForegroundColor Red
    }
    
    if ($backendProcess) {
        Write-Host "  ✅ Backend (.NET) - Running" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Backend (.NET) - Not running" -ForegroundColor Red
    }
    
    # Docker services
    try {
        $dockerServices = docker-compose ps --services 2>$null
        if ($dockerServices) {
            Write-Host "  ✅ Docker services available" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ❌ Docker not available" -ForegroundColor Red
    }
}

# Fonction d'aide
function Show-GitHelp {
    Write-Host "🛠️  ServiceLink Git Helper Commands" -ForegroundColor Blue
    Write-Host "====================================" -ForegroundColor Blue
    Write-Host ""
    Write-Host "📝 Création de branches:" -ForegroundColor Yellow
    Write-Host "  New-FeatureBranch -FeatureName 'auth-system' -Scope 'auth'" -ForegroundColor Cyan
    Write-Host "  New-HotfixBranch -HotfixName 'security-fix' -Scope 'auth'" -ForegroundColor Cyan
    Write-Host "  New-ReleaseBranch -Version '1.0.0'" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📝 Commits conventionnels:" -ForegroundColor Yellow
    Write-Host "  Invoke-ConventionalCommit -Type 'feat' -Scope 'auth' -Description 'add JWT authentication'" -ForegroundColor Cyan
    Write-Host "  Invoke-ConventionalCommit -Type 'fix' -Description 'resolve login bug' -BreakingChange" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🔄 Synchronisation:" -ForegroundColor Yellow
    Write-Host "  Sync-WithDevelop" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🧹 Maintenance:" -ForegroundColor Yellow
    Write-Host "  Remove-MergedBranches" -ForegroundColor Cyan
    Write-Host "  Show-ProjectStatus" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 Exemples de workflow:" -ForegroundColor Yellow
    Write-Host "  1. New-FeatureBranch -FeatureName 'user-dashboard' -Scope 'frontend'" -ForegroundColor Cyan
    Write-Host "  2. # Développer la fonctionnalité" -ForegroundColor Gray
    Write-Host "  3. Invoke-ConventionalCommit -Type 'feat' -Scope 'frontend' -Description 'add user dashboard'" -ForegroundColor Cyan
    Write-Host "  4. git push origin feature/frontend/user-dashboard" -ForegroundColor Cyan
    Write-Host "  5. # Créer PR via GitHub" -ForegroundColor Gray
}

# Exporter les fonctions
Export-ModuleMember -Function New-FeatureBranch, New-HotfixBranch, New-ReleaseBranch, Invoke-ConventionalCommit, Sync-WithDevelop, Remove-MergedBranches, Show-ProjectStatus, Show-GitHelp

# Afficher l'aide au chargement
Write-Host "🛠️  ServiceLink Git Helpers chargés!" -ForegroundColor Green
Write-Host "Tapez 'Show-GitHelp' pour voir les commandes disponibles" -ForegroundColor Yellow
