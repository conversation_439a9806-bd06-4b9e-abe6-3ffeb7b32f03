# Fichier d'exemple des variables d'environnement pour ServiceLink
# Copiez ce fichier vers .env et modifiez les valeurs selon vos besoins

# =============================================================================
# CONFIGURATION DE BASE
# =============================================================================

# Environnement de l'application (Development, Staging, Production)
ASPNETCORE_ENVIRONMENT=Development

# =============================================================================
# BASE DE DONNÉES
# =============================================================================

# Configuration PostgreSQL
POSTGRES_DB=servicelink
POSTGRES_USER=servicelink
POSTGRES_PASSWORD=ServiceLink2024!

# Chaîne de connexion complète (optionnel, sera construite automatiquement si non définie)
# CONNECTION_STRING=Host=localhost;Port=5432;Database=servicelink;Username=servicelink;Password=ServiceLink2024!

# =============================================================================
# CACHE REDIS
# =============================================================================

# Configuration Redis
REDIS_PASSWORD=ServiceLink2024!
REDIS_CONNECTION_STRING=localhost:6379,password=ServiceLink2024!

# =============================================================================
# AUTHENTIFICATION JWT
# =============================================================================

# Clé secrète JWT (IMPORTANT: Changez cette valeur en production!)
# Doit faire au moins 32 caractères
JWT_SECRET_KEY=your-super-secret-jwt-key-that-should-be-at-least-32-characters-long

# Émetteur et audience JWT
JWT_ISSUER=ServiceLink
JWT_AUDIENCE=ServiceLink

# Durée d'expiration des tokens en minutes
JWT_EXPIRATION_MINUTES=60

# =============================================================================
# CORS (Cross-Origin Resource Sharing)
# =============================================================================

# Origines autorisées (séparées par des virgules)
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,https://servicelink.com

# =============================================================================
# EMAIL
# =============================================================================

# Configuration SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Expéditeur par défaut
FROM_EMAIL=<EMAIL>
FROM_NAME=ServiceLink

# =============================================================================
# LOGGING
# =============================================================================

# Niveau de log (Trace, Debug, Information, Warning, Error, Critical)
LOG_LEVEL=Information

# Chemin des fichiers de log
LOG_PATH=./logs

# =============================================================================
# SÉCURITÉ
# =============================================================================

# Clé de chiffrement pour les données sensibles
ENCRYPTION_KEY=your-32-character-encryption-key

# Salt pour le hachage des mots de passe (généré automatiquement si non défini)
# PASSWORD_SALT=your-password-salt

# =============================================================================
# SERVICES EXTERNES
# =============================================================================

# Configuration pour les services de paiement (exemple Stripe)
# STRIPE_PUBLIC_KEY=pk_test_...
# STRIPE_SECRET_KEY=sk_test_...
# STRIPE_WEBHOOK_SECRET=whsec_...

# Configuration pour les services de stockage (exemple AWS S3)
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
# AWS_REGION=eu-west-1
# AWS_S3_BUCKET=servicelink-files

# =============================================================================
# MONITORING ET OBSERVABILITÉ
# =============================================================================

# Application Insights (Azure)
# APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=...

# Sentry pour le monitoring des erreurs
# SENTRY_DSN=https://...@sentry.io/...

# =============================================================================
# DÉVELOPPEMENT
# =============================================================================

# URL de l'API pour le frontend
VITE_API_URL=http://localhost:8080

# Activer le mode développement pour Entity Framework
EF_DEVELOPMENT_MODE=true

# Activer les migrations automatiques
AUTO_MIGRATE=true

# =============================================================================
# PRODUCTION
# =============================================================================

# URL publique de l'application
PUBLIC_URL=https://servicelink.com

# Configuration SSL/TLS
# SSL_CERTIFICATE_PATH=/path/to/certificate.crt
# SSL_CERTIFICATE_KEY_PATH=/path/to/private.key

# Configuration du reverse proxy
# PROXY_TRUSTED_NETWORKS=10.0.0.0/8,**********/12,***********/16

# =============================================================================
# DOCKER
# =============================================================================

# Tag de version pour les images Docker
DOCKER_TAG=latest

# Registry Docker (pour le déploiement)
# DOCKER_REGISTRY=your-registry.com/servicelink
