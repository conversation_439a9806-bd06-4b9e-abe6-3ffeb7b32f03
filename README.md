# 🏠 ServiceLink - Plateforme de Services à Domicile

[![.NET](https://img.shields.io/badge/.NET-9.0-purple.svg)](https://dotnet.microsoft.com/)
[![React](https://img.shields.io/badge/React-18.0-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-16-blue.svg)](https://www.postgresql.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

ServiceLink est une plateforme complète de services à domicile développée avec **Clean Architecture**, **CQRS**, **JWT Authentication**, et une interface React moderne. Elle connecte les clients avec des prestataires de services qualifiés pour tous types de services à domicile.

## ✅ État Actuel du Projet

### 🎯 Phase 6 Terminée - Backend API Complet
- ✅ **Clean Architecture** avec 4 couches (Domain, Application, Infrastructure, API)
- ✅ **CQRS** avec MediatR (15+ Commands, 12+ Queries)
- ✅ **Authentification JWT** complète avec refresh tokens
- ✅ **Authentification 2FA** (TOTP) avec QR codes et codes de récupération
- ✅ **Gestion des utilisateurs** avec 6 rôles et permissions granulaires
- ✅ **Services sécurisés** (Password, Email, TwoFactor)
- ✅ **Middleware pipeline** (Error handling, Logging, Rate limiting, CORS)
- ✅ **Documentation OpenAPI/Swagger** interactive avec JWT
- ✅ **Configuration complète** par environnement
- ✅ **Health checks** et monitoring

### 🚀 Fonctionnalités Implémentées

#### 👥 Gestion des Utilisateurs
- **Authentification JWT** avec refresh tokens sécurisés
- **Authentification 2FA** (TOTP) compatible Google Authenticator
- **6 rôles** : Admin, Manager, Supervisor, Support, Client, Provider
- **Permissions granulaires** par rôle avec claims JWT
- **Profils complets** avec validation FluentValidation
#### 🔐 Sécurité Avancée
- **Hachage BCrypt** des mots de passe (work factor 12 = 4096 itérations)
- **Validation force** des mots de passe (5 niveaux : VeryWeak → VeryStrong)
- **Rate limiting** (100 req/min, 1000 req/heure) avec AspNetCoreRateLimit
- **CORS** configuré pour frontend avec origines spécifiques
- **Tokens sécurisés** pour reset password et confirmation email

#### 📧 Communication
- **Service email** avec templates HTML/texte pour tous les emails transactionnels
- **SMTP configuré** avec support SSL/TLS et validation domaines
- **Notifications automatiques** : bienvenue, reset password, 2FA, etc.
- **Mode développement** avec logging console

#### 📊 Monitoring & Documentation
- **Health checks** pour base de données et services (/health)
- **Logging structuré** avec trace IDs et détection requêtes lentes
- **Documentation OpenAPI/Swagger** interactive avec authentification JWT
- **Métriques de performance** intégrées dans tous les endpoints

## 🏗️ Architecture Backend

### Structure Clean Architecture
```
backend/src/
├── ServiceLink.API/           # 🎮 API Layer
│   ├── Controllers/           # Controllers REST avec autorisation
│   ├── Middleware/           # Error handling, logging, rate limiting
│   ├── Configuration/        # JWT, CORS, Swagger, Security settings
│   └── Services/             # JwtService pour génération/validation tokens
├── ServiceLink.Application/   # ⚡ Application Layer
│   ├── Commands/             # 15+ Commands CQRS (Create, Update, Delete...)
│   ├── Queries/              # 12+ Queries CQRS (Get, Search, Statistics...)
│   ├── Handlers/             # MediatR handlers avec logging
│   ├── DTOs/                 # Request/Response DTOs avec validation
│   ├── Validators/           # FluentValidation avec règles métier
│   ├── Behaviors/            # Pipeline MediatR (Validation, Performance, Logging)
│   └── Interfaces/           # Abstractions services (IPasswordService, IEmailService...)
├── ServiceLink.Domain/        # 🏛️ Domain Layer
│   ├── Entities/             # User avec méthodes métier
│   ├── ValueObjects/         # Email, PhoneNumber avec validation
│   ├── Enums/                # UserRole (6 rôles), autres enums
│   ├── Interfaces/           # IRepository, IUnitOfWork
│   └── Specifications/       # Requêtes complexes réutilisables
└── ServiceLink.Infrastructure/ # 🔧 Infrastructure Layer
    ├── Data/                 # DbContext EF Core avec configurations
    ├── Repositories/         # Implémentations Repository + UnitOfWork
    ├── Services/             # Services concrets (Password, Email, TwoFactor)
    └── Migrations/           # Migrations EF Core

### Technologies Utilisées
- **.NET 9** : Framework principal avec C# 13
- **Entity Framework Core** : ORM avec PostgreSQL et migrations
- **MediatR** : CQRS et pipeline behaviors
- **FluentValidation** : Validation robuste avec règles métier
- **BCrypt.Net** : Hachage sécurisé des mots de passe
- **OTP.NET** : Authentification 2FA TOTP
- **QRCoder** : Génération QR codes pour 2FA
- **AspNetCoreRateLimit** : Protection rate limiting
- **Swashbuckle** : Documentation OpenAPI/Swagger

## 🚦 Démarrage Rapide

### Prérequis
- [.NET 9 SDK](https://dotnet.microsoft.com/download/dotnet/9.0)
- [PostgreSQL 16+](https://www.postgresql.org/download/)
- [Git](https://git-scm.com/)

### Installation Backend

1. **Cloner le repository**
```bash
git clone https://github.com/wilfried11/ServiceLink.git
cd ServiceLink
```

2. **Configurer la base de données**
```bash
# Créer la base de données PostgreSQL
createdb ServiceLink_Dev

# Configurer la chaîne de connexion dans appsettings.Development.json
```

3. **Restaurer les packages et compiler**
```bash
cd backend
dotnet restore
dotnet build
```

4. **Appliquer les migrations**
```bash
cd src/ServiceLink.API
dotnet ef database update
```

5. **Lancer l'API**
```bash
dotnet run
```

🎉 **L'API sera disponible sur `https://localhost:5001`**
📚 **Documentation Swagger : `https://localhost:5001/api/docs`**

### Configuration Rapide

#### appsettings.Development.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ServiceLink_Dev;Username=postgres;Password=your_password"
  },
  "JWT": {
    "SecretKey": "your-super-secret-key-minimum-32-characters-for-development",
    "ExpirationMinutes": 120
  },
  "Email": {
    "DevelopmentMode": true
  }
}
```

## 📚 Documentation API

### Endpoints Principaux

#### 🔐 Authentification (À venir - Phase 7)
- `POST /api/auth/login` - Connexion utilisateur avec JWT
- `POST /api/auth/register` - Inscription utilisateur
- `POST /api/auth/refresh` - Renouvellement token
- `POST /api/auth/logout` - Déconnexion

#### 👥 Gestion Utilisateurs
- `GET /api/users` - Liste des utilisateurs avec pagination (Admin/Manager/Support)
- `GET /api/users/me` - Profil utilisateur connecté
- `GET /api/users/{id}` - Utilisateur par ID (self ou admin)
- `POST /api/users` - Créer utilisateur (Admin/Manager/Supervisor)
- `PUT /api/users/{id}` - Mettre à jour utilisateur (self ou admin)
- `POST /api/users/{id}/change-password` - Changer mot de passe (self only)

#### 🔒 Authentification 2FA
- `POST /api/users/{id}/enable-2fa` - Activer 2FA avec QR code (self only)
- `POST /api/users/{id}/disable-2fa` - Désactiver 2FA (self only)

#### 🛡️ Administration
- `POST /api/users/{id}/activate` - Activer utilisateur (Admin/Manager)
- `POST /api/users/{id}/deactivate` - Désactiver utilisateur (Admin/Manager)
- `GET /api/users/statistics` - Statistiques utilisateurs (Admin/Manager/Support)

### Documentation Interactive
📖 **Swagger UI complet** : `https://localhost:5001/api/docs`
- Authentification JWT intégrée
- Test direct des endpoints
- Schémas de données détaillés
- Exemples de requêtes/réponses

## 🧪 Tests

### Lancer tous les tests
```bash
cd backend
dotnet test
```

### Tests par projet
```bash
# Tests unitaires Domain
dotnet test tests/ServiceLink.Domain.Tests/

# Tests unitaires Application
dotnet test tests/ServiceLink.Application.Tests/

# Tests d'intégration Infrastructure
dotnet test tests/ServiceLink.Infrastructure.Tests/

# Tests d'intégration API
dotnet test tests/ServiceLink.API.Tests/
```

## � Déploiement

### Déploiement avec Docker

#### Prérequis
- Docker et Docker Compose installés
- Variables d'environnement configurées (voir `.env.example`)

#### Déploiement rapide
```bash
# Copier et configurer les variables d'environnement
cp .env.example .env
# Éditer .env avec vos valeurs

# Déploiement en développement
.\scripts\deploy.ps1 -Environment development

# Déploiement en production
.\scripts\deploy.ps1 -Environment production -Version v1.0.0
```

#### Services disponibles
- **API** : http://localhost:8080
- **Base de données** : PostgreSQL sur le port 5432
- **Cache** : Redis sur le port 6379
- **Interface DB** (dev) : pgAdmin sur http://localhost:5050
- **Interface Redis** (dev) : Redis Commander sur http://localhost:8081
- **Email testing** (dev) : MailHog sur http://localhost:8025

#### Arrêt des services
```bash
# Arrêt simple
.\scripts\stop.ps1

# Arrêt avec nettoyage des volumes
.\scripts\stop.ps1 -CleanVolumes

# Arrêt avec suppression des images
.\scripts\stop.ps1 -RemoveImages
```

### Variables d'environnement importantes

| Variable | Description | Défaut |
|----------|-------------|---------|
| `POSTGRES_PASSWORD` | Mot de passe PostgreSQL | `ServiceLink2024!` |
| `REDIS_PASSWORD` | Mot de passe Redis | `ServiceLink2024!` |
| `JWT_SECRET_KEY` | Clé secrète JWT | ⚠️ À changer en production |
| `SMTP_HOST` | Serveur SMTP | `smtp.gmail.com` |
| `CORS_ORIGINS` | Origines CORS autorisées | `http://localhost:3000` |

## �📋 Roadmap

### ✅ Phase 7 - Tests & Validation (Terminé)
- [x] Infrastructure de tests unitaires
- [x] Tests de base pour tous les projets
- [x] Configuration des outils de test
- [ ] Tests d'intégration API complets (À venir)
- [ ] Tests de sécurité et autorisation (À venir)
- [ ] Tests de performance et charge (À venir)

### ✅ Phase 8 - Configuration & Déploiement (Terminé)
- [x] Configuration Docker et Docker Compose
- [x] Scripts de déploiement PowerShell
- [x] Configuration des variables d'environnement
- [x] Dockerfile optimisé pour production et développement
- [x] Configuration des services (PostgreSQL, Redis, Nginx)

### 📅 Phase 9 - Frontend React
- [ ] Interface utilisateur moderne avec React + TypeScript
- [ ] Authentification JWT avec refresh automatique
- [ ] Gestion des profils utilisateurs
- [ ] Interface d'administration

### 📅 Phase 10 - Services Métier
- [ ] Gestion des prestataires et services
- [ ] Système de réservations
- [ ] Gestion des paiements
- [ ] Système d'avis et notations

### 📅 Phase 11 - CI/CD & Production
- [ ] Pipeline CI/CD avec GitHub Actions
- [ ] Déploiement cloud (Azure/AWS)
- [ ] Monitoring et alertes
- [ ] Tests automatisés en pipeline

## 🤝 Contribution

### Workflow Git
1. **Fork** le repository
2. **Créer** une branche feature : `git checkout -b feature/ma-fonctionnalite`
3. **Commiter** les changements : `git commit -m 'feat: ajouter ma fonctionnalité'`
4. **Pousser** la branche : `git push origin feature/ma-fonctionnalite`
5. **Créer** une Pull Request

### Standards de Code
- **Clean Code** : Noms explicites, fonctions courtes, commentaires utiles
- **SOLID Principles** : Respect des principes de conception
- **Tests** : Couverture minimale 80%
- **Documentation** : Commentaires XML pour l'API

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 👨‍💻 Auteur

**Kevin Wilfried** - [GitHub](https://github.com/wilfried11)

---

⭐ **N'hésitez pas à donner une étoile si ce projet vous plaît !**
