# 🛠️ ServiceLink - Plateforme de Services à Domicile

## 🎯 Description
ServiceLink est une plateforme web fullstack de mise en relation entre particuliers et prestataires de services à domicile. Une alternative moderne et améliorée à Yoojo.

## 🏗️ Architecture

### Frontend
- **React.js 18** avec Vite et TypeScript
- **TailwindCSS** + **Shadcn/UI** pour l'interface
- **Responsive Design** et UX moderne

### Backend
- **.NET 9 API** avec Clean Architecture
- **CQRS** + Repository Pattern
- **Entity Framework Core** avec PostgreSQL
- **JWT Authentication** + MFA + RBAC

### Base de Données
- **PostgreSQL** pour la production
- **Docker** pour le développement local

## 🚀 Fonctionnalités Principales

### 👥 Pour les Clients
- Recherche et comparaison de prestataires
- Réservation immédiate ou sur devis
- Paiement sécurisé (Stripe, PayPal, solutions mobiles)
- Suivi en temps réel des prestations
- Système d'évaluation et avis

### 🔧 Pour les Prestataires
- Profil professionnel avec certifications
- Gestion d'agenda et disponibilités
- Messagerie intégrée avec clients
- Suivi des paiements et commissions
- Tableau de bord analytique

### 🛡️ Back-Office Admin
- Gestion des utilisateurs et validation
- Modération des annonces
- Gestion des paiements et commissions
- Système de réclamations et litiges
- Statistiques et rapports

## 💳 Systèmes de Paiement
- **International** : Stripe, PayPal, Visa/MasterCard
- **Mobile Afrique** : MTN MoMo, Orange Money, Flutterwave
- **Fonctionnalités** : Paiement différé, cashback, abonnements

## 🔐 Sécurité
- Authentification JWT avec MFA
- Système de rôles granulaire (RBAC)
- Chiffrement des données sensibles
- Logs d'audit complets

## 🧪 Tests et Qualité
- Tests unitaires et d'intégration
- Tests end-to-end
- Couverture de code > 80%
- CI/CD avec environnements Dev/Test/Prod

## 📁 Structure du Projet

```
ServiceLink/
├── backend/                 # API .NET 9
│   ├── src/
│   │   ├── ServiceLink.API/
│   │   ├── ServiceLink.Application/
│   │   ├── ServiceLink.Domain/
│   │   └── ServiceLink.Infrastructure/
│   └── tests/
├── frontend/               # React + Vite
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   └── services/
│   └── public/
├── docker/                 # Configuration Docker
└── docs/                   # Documentation
```

## 🚀 Démarrage Rapide

### Prérequis
- .NET 9 SDK
- Node.js 18+
- Docker et Docker Compose
- PostgreSQL (ou via Docker)

### Installation

1. **Cloner le projet**
```bash
git clone <repository-url>
cd ServiceLink
```

2. **Backend**
```bash
cd backend
dotnet restore
dotnet run --project src/ServiceLink.API
```

3. **Frontend**
```bash
cd frontend
npm install
npm run dev
```

4. **Base de données**
```bash
docker-compose up -d postgres
cd backend
dotnet ef database update
```

## 🌍 Environnements

- **Development** : `http://localhost:5173` (Frontend) + `http://localhost:5000` (API)
- **Testing** : Configuration dédiée avec base de test
- **Production** : Configuration sécurisée avec HTTPS et domaines personnalisés

## 📚 Documentation

- [Guide d'installation](docs/installation.md)
- [Documentation API](docs/api.md)
- [Guide utilisateur](docs/user-guide.md)
- [Guide de déploiement](docs/deployment.md)

## 🤝 Contribution

Ce projet suit les meilleures pratiques de développement :
- Code documenté et testé
- Commits conventionnels
- Pull requests avec review
- Standards de qualité stricts

## 📄 Licence

Propriétaire - Tous droits réservés

---

**ServiceLink** - "Des services de qualité à portée de main" 🔗
