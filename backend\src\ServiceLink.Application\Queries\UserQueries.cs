using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Queries;

/// <summary>
/// Requête pour obtenir un utilisateur par son ID
/// </summary>
public class GetUserByIdQuery : IRequest<UserResponse?>
{
    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="userId">Identifiant de l'utilisateur</param>
    public GetUserByIdQuery(Guid userId)
    {
        UserId = userId;
    }
}

/// <summary>
/// Requête pour obtenir un utilisateur par son email
/// </summary>
public class GetUserByEmailQuery : IRequest<UserResponse?>
{
    /// <summary>
    /// Adresse email de l'utilisateur
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="email">Adresse email</param>
    public GetUserByEmailQuery(string email)
    {
        Email = email;
    }
}

/// <summary>
/// Requête pour obtenir un utilisateur par son numéro de téléphone
/// </summary>
public class GetUserByPhoneQuery : IRequest<UserResponse?>
{
    /// <summary>
    /// Numéro de téléphone
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone</param>
    public GetUserByPhoneQuery(string phoneNumber)
    {
        PhoneNumber = phoneNumber;
    }
}

/// <summary>
/// Requête pour obtenir la liste des utilisateurs avec pagination et filtres
/// </summary>
public class GetUsersQuery : IRequest<UserListResponse>
{
    /// <summary>
    /// Numéro de page (commence à 1)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Taille de la page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Filtre par rôle (optionnel)
    /// </summary>
    public UserRole? Role { get; set; }

    /// <summary>
    /// Filtre par statut actif (optionnel)
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filtre par email confirmé (optionnel)
    /// </summary>
    public bool? IsEmailConfirmed { get; set; }

    /// <summary>
    /// Terme de recherche (nom, prénom, email)
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Tri par champ
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// Ordre de tri (asc/desc)
    /// </summary>
    public string? SortOrder { get; set; } = "asc";
}

/// <summary>
/// Requête pour obtenir les utilisateurs par rôle
/// </summary>
public class GetUsersByRoleQuery : IRequest<List<UserResponse>>
{
    /// <summary>
    /// Rôle recherché
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="role">Rôle recherché</param>
    public GetUsersByRoleQuery(UserRole role)
    {
        Role = role;
    }
}

/// <summary>
/// Requête pour obtenir les utilisateurs actifs
/// </summary>
public class GetActiveUsersQuery : IRequest<List<UserResponse>>
{
}

/// <summary>
/// Requête pour obtenir les utilisateurs inactifs
/// </summary>
public class GetInactiveUsersQuery : IRequest<List<UserResponse>>
{
}

/// <summary>
/// Requête pour obtenir les utilisateurs avec email non confirmé
/// </summary>
public class GetUsersWithUnconfirmedEmailQuery : IRequest<List<UserResponse>>
{
}

/// <summary>
/// Requête pour obtenir les utilisateurs verrouillés
/// </summary>
public class GetLockedUsersQuery : IRequest<List<UserResponse>>
{
}

/// <summary>
/// Requête pour rechercher des utilisateurs
/// </summary>
public class SearchUsersQuery : IRequest<List<UserResponse>>
{
    /// <summary>
    /// Terme de recherche
    /// </summary>
    public string SearchTerm { get; set; }

    /// <summary>
    /// Nombre maximum de résultats
    /// </summary>
    public int MaxResults { get; set; } = 50;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="searchTerm">Terme de recherche</param>
    public SearchUsersQuery(string searchTerm)
    {
        SearchTerm = searchTerm;
    }
}

/// <summary>
/// Requête pour obtenir les utilisateurs créés dans une période
/// </summary>
public class GetUsersCreatedBetweenQuery : IRequest<List<UserResponse>>
{
    /// <summary>
    /// Date de début
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Date de fin
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="startDate">Date de début</param>
    /// <param name="endDate">Date de fin</param>
    public GetUsersCreatedBetweenQuery(DateTime startDate, DateTime endDate)
    {
        StartDate = startDate;
        EndDate = endDate;
    }
}

/// <summary>
/// Requête pour obtenir les utilisateurs connectés récemment
/// </summary>
public class GetRecentlyLoggedInUsersQuery : IRequest<List<UserResponse>>
{
    /// <summary>
    /// Date depuis laquelle chercher
    /// </summary>
    public DateTime Since { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="since">Date depuis laquelle chercher</param>
    public GetRecentlyLoggedInUsersQuery(DateTime since)
    {
        Since = since;
    }
}

/// <summary>
/// Requête pour obtenir les statistiques des utilisateurs
/// </summary>
public class GetUserStatisticsQuery : IRequest<UserStatisticsResponse>
{
}

/// <summary>
/// Requête pour vérifier si un email existe
/// </summary>
public class CheckEmailExistsQuery : IRequest<bool>
{
    /// <summary>
    /// Adresse email à vérifier
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// ID utilisateur à exclure de la vérification (pour les mises à jour)
    /// </summary>
    public Guid? ExcludeUserId { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="email">Adresse email</param>
    public CheckEmailExistsQuery(string email)
    {
        Email = email;
    }
}

/// <summary>
/// Requête pour vérifier si un numéro de téléphone existe
/// </summary>
public class CheckPhoneExistsQuery : IRequest<bool>
{
    /// <summary>
    /// Numéro de téléphone à vérifier
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// ID utilisateur à exclure de la vérification (pour les mises à jour)
    /// </summary>
    public Guid? ExcludeUserId { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone</param>
    public CheckPhoneExistsQuery(string phoneNumber)
    {
        PhoneNumber = phoneNumber;
    }
}

/// <summary>
/// Requête pour valider les informations de connexion
/// </summary>
public class ValidateLoginQuery : IRequest<UserResponse?>
{
    /// <summary>
    /// Adresse email
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// Mot de passe
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="password">Mot de passe</param>
    public ValidateLoginQuery(string email, string password)
    {
        Email = email;
        Password = password;
    }
}

/// <summary>
/// Requête pour obtenir un utilisateur par token de confirmation d'email
/// </summary>
public class GetUserByEmailTokenQuery : IRequest<UserResponse?>
{
    /// <summary>
    /// Token de confirmation d'email
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="token">Token de confirmation</param>
    public GetUserByEmailTokenQuery(string token)
    {
        Token = token;
    }
}

/// <summary>
/// Requête pour obtenir un utilisateur par token de réinitialisation de mot de passe
/// </summary>
public class GetUserByPasswordResetTokenQuery : IRequest<UserResponse?>
{
    /// <summary>
    /// Token de réinitialisation
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="token">Token de réinitialisation</param>
    public GetUserByPasswordResetTokenQuery(string token)
    {
        Token = token;
    }
}
