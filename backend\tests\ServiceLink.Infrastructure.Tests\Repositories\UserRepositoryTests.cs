using Bogus;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.ValueObjects;
using ServiceLink.Infrastructure.Data;
using ServiceLink.Infrastructure.Repositories;

namespace ServiceLink.Infrastructure.Tests.Repositories;

/// <summary>
/// Tests d'intégration pour UserRepository avec base de données en mémoire
/// </summary>
public class UserRepositoryTests : IDisposable
{
    private readonly ServiceLinkDbContext _context;
    private readonly UserRepository _repository;
    private readonly Faker _faker;

    public UserRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<ServiceLinkDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ServiceLinkDbContext(options);
        _repository = new UserRepository(_context);
        _faker = new Faker("fr");

        // Ensure database is created
        _context.Database.EnsureCreated();
    }

    [Fact]
    public async Task AddAsync_WithValidUser_ShouldAddUserToDatabase()
    {
        // Arrange
        var user = CreateValidUser();

        // Act
        await _repository.AddAsync(user);
        await _context.SaveChangesAsync();

        // Assert
        var savedUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == user.Id);
        savedUser.Should().NotBeNull();
        savedUser!.Email.Should().Be(user.Email);
        savedUser.FirstName.Should().Be(user.FirstName);
        savedUser.LastName.Should().Be(user.LastName);
        savedUser.Role.Should().Be(user.Role);
    }

    [Fact]
    public async Task GetByIdAsync_WithExistingUser_ShouldReturnUser()
    {
        // Arrange
        var user = CreateValidUser();
        await _repository.AddAsync(user);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(user.Id);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(user.Id);
        result.Email.Should().Be(user.Email);
        result.FirstName.Should().Be(user.FirstName);
        result.LastName.Should().Be(user.LastName);
    }

    [Fact]
    public async Task GetByIdAsync_WithNonExistingUser_ShouldReturnNull()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();

        // Act
        var result = await _repository.GetByIdAsync(nonExistingId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByEmailAsync_WithExistingEmail_ShouldReturnUser()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var user = CreateValidUser(email);
        await _repository.AddAsync(user);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByEmailAsync(email);

        // Assert
        result.Should().NotBeNull();
        result!.Email.Should().Be(email);
        result.Id.Should().Be(user.Id);
    }

    [Fact]
    public async Task GetByEmailAsync_WithNonExistingEmail_ShouldReturnNull()
    {
        // Arrange
        var nonExistingEmail = Email.Create("<EMAIL>");

        // Act
        var result = await _repository.GetByEmailAsync(nonExistingEmail);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task ExistsByEmailAsync_WithExistingEmail_ShouldReturnTrue()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");
        var user = CreateValidUser(email);
        await _repository.AddAsync(user);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ExistsByEmailAsync(email);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ExistsByEmailAsync_WithNonExistingEmail_ShouldReturnFalse()
    {
        // Arrange
        var nonExistingEmail = Email.Create("<EMAIL>");

        // Act
        var result = await _repository.ExistsByEmailAsync(nonExistingEmail);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetAllAsync_WithMultipleUsers_ShouldReturnAllUsers()
    {
        // Arrange
        var users = new List<User>
        {
            CreateValidUser(Email.Create("<EMAIL>")),
            CreateValidUser(Email.Create("<EMAIL>")),
            CreateValidUser(Email.Create("<EMAIL>"))
        };

        foreach (var user in users)
        {
            await _repository.AddAsync(user);
        }
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllAsync();

        // Assert
        result.Should().HaveCount(3);
        result.Should().Contain(u => u.Email.Value == "<EMAIL>");
        result.Should().Contain(u => u.Email.Value == "<EMAIL>");
        result.Should().Contain(u => u.Email.Value == "<EMAIL>");
    }

    [Fact]
    public async Task GetPagedAsync_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        var users = new List<User>();
        for (int i = 1; i <= 10; i++)
        {
            users.Add(CreateValidUser(Email.Create($"user{i}@example.com")));
        }

        foreach (var user in users)
        {
            await _repository.AddAsync(user);
        }
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetPagedAsync(2, 3); // Page 2, 3 items per page

        // Assert
        result.Should().HaveCount(3);
        // Should return items 4, 5, 6 (0-indexed: skip 3, take 3)
    }

    [Fact]
    public async Task GetByRoleAsync_WithSpecificRole_ShouldReturnUsersWithThatRole()
    {
        // Arrange
        var clientUsers = new List<User>
        {
            CreateValidUser(Email.Create("<EMAIL>"), UserRole.Client),
            CreateValidUser(Email.Create("<EMAIL>"), UserRole.Client)
        };

        var adminUser = CreateValidUser(Email.Create("<EMAIL>"), UserRole.Admin);

        foreach (var user in clientUsers)
        {
            await _repository.AddAsync(user);
        }
        await _repository.AddAsync(adminUser);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByRoleAsync(UserRole.Client);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(u => u.Role == UserRole.Client);
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnOnlyActiveUsers()
    {
        // Arrange
        var activeUser = CreateValidUser(Email.Create("<EMAIL>"));
        var inactiveUser = CreateValidUser(Email.Create("<EMAIL>"));
        inactiveUser.Deactivate();

        await _repository.AddAsync(activeUser);
        await _repository.AddAsync(inactiveUser);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveUsersAsync();

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(u => u.Email.Value == "<EMAIL>");
        result.Should().NotContain(u => u.Email.Value == "<EMAIL>");
    }

    [Fact]
    public async Task UpdateAsync_WithModifiedUser_ShouldUpdateUser()
    {
        // Arrange
        var user = CreateValidUser();
        await _repository.AddAsync(user);
        await _context.SaveChangesAsync();

        // Modify user
        var newPhoneNumber = PhoneNumber.Create("+33987654321");
        user.UpdateBasicInfo("Jane", "Smith", newPhoneNumber);

        // Act
        await _repository.UpdateAsync(user);
        await _context.SaveChangesAsync();

        // Assert
        var updatedUser = await _repository.GetByIdAsync(user.Id);
        updatedUser.Should().NotBeNull();
        updatedUser!.FirstName.Should().Be("Jane");
        updatedUser.LastName.Should().Be("Smith");
        updatedUser.PhoneNumber!.Value.Should().Be("+33987654321");
    }

    [Fact]
    public async Task DeleteAsync_WithExistingUser_ShouldRemoveUser()
    {
        // Arrange
        var user = CreateValidUser();
        await _repository.AddAsync(user);
        await _context.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(user);
        await _context.SaveChangesAsync();

        // Assert
        var deletedUser = await _repository.GetByIdAsync(user.Id);
        deletedUser.Should().BeNull();
    }

    [Fact]
    public async Task CountAsync_WithMultipleUsers_ShouldReturnCorrectCount()
    {
        // Arrange
        var users = new List<User>
        {
            CreateValidUser(Email.Create("<EMAIL>")),
            CreateValidUser(Email.Create("<EMAIL>")),
            CreateValidUser(Email.Create("<EMAIL>"))
        };

        foreach (var user in users)
        {
            await _repository.AddAsync(user);
        }
        await _context.SaveChangesAsync();

        // Act
        var count = await _repository.CountAsync();

        // Assert
        count.Should().Be(3);
    }

    [Fact]
    public async Task GetUsersCreatedAfterAsync_ShouldReturnUsersCreatedAfterDate()
    {
        // Arrange
        var cutoffDate = DateTime.UtcNow.AddDays(-1);
        
        var oldUser = CreateValidUser(Email.Create("<EMAIL>"));
        var newUser = CreateValidUser(Email.Create("<EMAIL>"));

        // Simulate old user created before cutoff
        await _repository.AddAsync(oldUser);
        await _context.SaveChangesAsync();
        
        // Update created date manually for testing
        _context.Entry(oldUser).Property(u => u.CreatedAt).CurrentValue = cutoffDate.AddHours(-1);
        await _context.SaveChangesAsync();

        await _repository.AddAsync(newUser);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetUsersCreatedAfterAsync(cutoffDate);

        // Assert
        result.Should().HaveCount(1);
        result.Should().Contain(u => u.Email.Value == "<EMAIL>");
        result.Should().NotContain(u => u.Email.Value == "<EMAIL>");
    }

    private User CreateValidUser(Email? email = null, UserRole role = UserRole.Client)
    {
        email ??= Email.Create(_faker.Internet.Email());
        var firstName = _faker.Name.FirstName();
        var lastName = _faker.Name.LastName();
        var phoneNumber = PhoneNumber.Create("+33123456789");
        
        return new User(email, firstName, lastName, role, phoneNumber);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
