{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "ServiceLink": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=servicelink;Username=servicelink_user;Password=*********************;Port=5437"}, "JWT": {"SecretKey": "ServiceLink-Development-Secret-Key-Not-For-Production-Use-2024-Very-Long-Key", "ExpirationMinutes": 120, "RefreshTokenExpirationDays": 30}, "Email": {"DevelopmentMode": true, "SmtpServer": "localhost", "SmtpPort": 1025, "EnableSsl": false, "Username": "", "Password": "", "BaseUrl": "https://localhost:7276"}, "CORS": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:3000", "https://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:5173"]}, "RateLimit": {"GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 1000}, {"Endpoint": "*", "Period": "1h", "Limit": 10000}]}, "Development": {"EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "SeedData": true}, "Security": {"RequireHttps": false, "RequireEmailConfirmation": false, "RequirePhoneConfirmation": false, "MaxFailedLoginAttempts": 10, "LockoutDurationMinutes": 5, "TwoFactorRequired": false}, "Swagger": {"EnableXmlComments": true, "EnableJwtAuthentication": true, "IncludeXmlComments": true}}