{"version": 2, "dgSpecHash": "35p37F349ic=", "success": true, "projectFilePath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\12.5.0\\mediatr.12.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.contracts\\2.0.1\\mediatr.contracts.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"], "logs": []}