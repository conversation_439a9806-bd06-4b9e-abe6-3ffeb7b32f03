using FluentAssertions;

namespace ServiceLink.API.Tests;

/// <summary>
/// Tests de base pour vérifier que l'infrastructure de test fonctionne
/// </summary>
public class BasicTests
{
    [Fact]
    public void BasicTest_ShouldPass()
    {
        // Arrange
        var expected = 42;

        // Act
        var actual = 42;

        // Assert
        actual.Should().Be(expected);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void Boolean_ShouldReturnSameValue(bool input)
    {
        // Act
        var result = input;

        // Assert
        result.Should().Be(input);
    }
}
