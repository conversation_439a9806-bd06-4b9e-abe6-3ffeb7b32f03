using MediatR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Commands;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Application.Handlers;

/// <summary>
/// Handler pour la commande de création d'utilisateur
/// </summary>
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, UserResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordService _passwordService;
    private readonly IEmailService _emailService;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="unitOfWork">Unit of Work</param>
    /// <param name="passwordService">Service de gestion des mots de passe</param>
    /// <param name="emailService">Service d'envoi d'emails</param>
    /// <param name="logger">Logger</param>
    public CreateUserCommandHandler(
        IUnitOfWork unitOfWork,
        IPasswordService passwordService,
        IEmailService emailService,
        ILogger<CreateUserCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _passwordService = passwordService;
        _emailService = emailService;
        _logger = logger;
    }

    /// <summary>
    /// Traite la commande de création d'utilisateur
    /// </summary>
    /// <param name="request">Commande de création</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Réponse utilisateur</returns>
    public async Task<UserResponse> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Création d'un nouvel utilisateur avec l'email {Email}", request.Email);

        try
        {
            // Vérification de l'unicité de l'email
            var email = Email.Create(request.Email);
            var existingUser = await _unitOfWork.Users.GetByEmailAsync(email, cancellationToken);
            if (existingUser != null)
            {
                _logger.LogWarning("Tentative de création d'un utilisateur avec un email déjà existant: {Email}", request.Email);
                throw new InvalidOperationException("Un utilisateur avec cette adresse email existe déjà.");
            }

            // Vérification de l'unicité du numéro de téléphone si fourni
            PhoneNumber? phoneNumber = null;
            if (!string.IsNullOrEmpty(request.PhoneNumber))
            {
                phoneNumber = PhoneNumber.Create(request.PhoneNumber);
                var existingUserWithPhone = await _unitOfWork.Users.GetByPhoneNumberAsync(phoneNumber, cancellationToken);
                if (existingUserWithPhone != null)
                {
                    _logger.LogWarning("Tentative de création d'un utilisateur avec un numéro de téléphone déjà existant: {PhoneNumber}", request.PhoneNumber);
                    throw new InvalidOperationException("Un utilisateur avec ce numéro de téléphone existe déjà.");
                }
            }

            // Hachage du mot de passe
            var (passwordHash, passwordSalt) = _passwordService.HashPassword(request.Password);

            // Création de l'entité utilisateur
            var user = new User(
                email,
                request.FirstName,
                request.LastName,
                request.Role,
                phoneNumber,
                request.CreatedBy);

            // Définition du mot de passe
            user.SetPassword(passwordHash, passwordSalt, request.CreatedBy);

            // Mise à jour des propriétés optionnelles
            if (request.Language != "fr-FR")
            {
                user.UpdateBasicInfo(request.FirstName, request.LastName, phoneNumber, request.CreatedBy);
            }

            // Ajout à la base de données
            await _unitOfWork.Users.AddAsync(user, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Utilisateur créé avec succès. ID: {UserId}, Email: {Email}", user.Id, request.Email);

            // Envoi de l'email de bienvenue avec token de confirmation
            try
            {
                var confirmationToken = _passwordService.GenerateEmailConfirmationToken();
                // TODO: Stocker le token de confirmation dans l'utilisateur
                
                await _emailService.SendWelcomeEmailAsync(
                    request.Email,
                    request.FirstName,
                    confirmationToken,
                    cancellationToken);

                _logger.LogInformation("Email de bienvenue envoyé à {Email}", request.Email);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Échec de l'envoi de l'email de bienvenue à {Email}", request.Email);
                // Ne pas faire échouer la création de l'utilisateur si l'email échoue
            }

            // Conversion en DTO de réponse
            return MapToUserResponse(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de l'utilisateur avec l'email {Email}", request.Email);
            throw;
        }
    }

    /// <summary>
    /// Convertit une entité User en UserResponse
    /// </summary>
    /// <param name="user">Entité utilisateur</param>
    /// <returns>DTO de réponse</returns>
    private static UserResponse MapToUserResponse(User user)
    {
        return new UserResponse
        {
            Id = user.Id,
            Email = user.Email.Value,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            RoleDescription = user.Role.GetDescription(),
            IsEmailConfirmed = user.IsEmailConfirmed,
            IsPhoneConfirmed = user.IsPhoneConfirmed,
            IsActive = user.IsActive,
            IsTwoFactorEnabled = user.IsTwoFactorEnabled,
            AvatarUrl = user.AvatarUrl,
            ProfileCompletionPercentage = user.ProfileCompletionPercentage,
            Language = user.Language,
            TimeZone = user.TimeZone,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            LastLoginAt = user.LastLoginAt
        };
    }
}
