-- ServiceLink Database Initialization Script
-- This script sets up the initial database structure and configurations

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create additional schemas if needed
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS logs;

-- Set timezone
SET timezone = 'UTC';

-- Create custom types for enums (will be used by Entity Framework)
-- These will be recreated by EF migrations, but having them here ensures consistency

-- User roles enum
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('Client', 'Provider', 'Admin', 'Support', 'Manager', 'Supervisor');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Service status enum
DO $$ BEGIN
    CREATE TYPE service_status AS ENUM ('Draft', 'Active', 'Inactive', 'Suspended');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Booking status enum
DO $$ BEGIN
    CREATE TYPE booking_status AS ENUM ('Pending', 'Confirmed', 'InProgress', 'Completed', 'Cancelled', 'Disputed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Payment status enum
DO $$ BEGIN
    CREATE TYPE payment_status AS ENUM ('Pending', 'Processing', 'Completed', 'Failed', 'Refunded', 'Disputed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Payment method enum
DO $$ BEGIN
    CREATE TYPE payment_method AS ENUM ('Stripe', 'PayPal', 'MTNMoMo', 'OrangeMoney', 'Flutterwave', 'Cash');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create audit function for tracking changes
CREATE OR REPLACE FUNCTION audit.audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(NEW),
            current_setting('app.current_user_id', true)::uuid,
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            old_values,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            current_setting('app.current_user_id', true)::uuid,
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            old_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            current_setting('app.current_user_id', true)::uuid,
            NOW()
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit.audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name TEXT NOT NULL,
    operation TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON audit.audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit.audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit.audit_log(user_id);

-- Create function for full-text search
CREATE OR REPLACE FUNCTION public.create_search_vector(title TEXT, description TEXT, tags TEXT[])
RETURNS tsvector AS $$
BEGIN
    RETURN to_tsvector('french', 
        COALESCE(title, '') || ' ' || 
        COALESCE(description, '') || ' ' || 
        COALESCE(array_to_string(tags, ' '), '')
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA audit TO servicelink_user;
GRANT SELECT, INSERT ON audit.audit_log TO servicelink_user;
GRANT USAGE ON SCHEMA logs TO servicelink_user;

-- Set default permissions for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO servicelink_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT SELECT, INSERT ON TABLES TO servicelink_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA logs GRANT ALL ON TABLES TO servicelink_user;

-- Create initial admin user (will be properly created by the application)
-- This is just a placeholder for development
INSERT INTO public."Users" (
    "Id", 
    "Email", 
    "FirstName", 
    "LastName", 
    "Role", 
    "IsEmailConfirmed", 
    "CreatedAt", 
    "UpdatedAt"
) VALUES (
    uuid_generate_v4(),
    '<EMAIL>',
    'System',
    'Administrator',
    'Admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT DO NOTHING;

COMMIT;
