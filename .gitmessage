# <type>[optional scope]: <description>
#
# [optional body]
#
# [optional footer(s)]

# Types:
# feat:     A new feature
# fix:      A bug fix
# docs:     Documentation only changes
# style:    Changes that do not affect the meaning of the code
# refactor: A code change that neither fixes a bug nor adds a feature
# perf:     A code change that improves performance
# test:     Adding missing tests or correcting existing tests
# build:    Changes that affect the build system or external dependencies
# ci:       Changes to our CI configuration files and scripts
# chore:    Other changes that don't modify src or test files
# revert:   Reverts a previous commit

# Scopes (examples):
# auth, api, frontend, db, payment, booking, user, provider, admin, docker, config

# Examples:
# feat(auth): add JWT token refresh mechanism
# fix(booking): resolve booking cancellation bug
# docs(api): add swagger documentation for user endpoints
# test(payment): add integration tests for stripe payment
# refactor(user): extract user validation logic to service layer

# Rules:
# - Use the imperative mood in the subject line
# - Do not end the subject line with a period
# - Limit the subject line to 50 characters
# - Separate subject from body with a blank line
# - Use the body to explain what and why vs. how
# - Can use multiple lines with "-" for breaking changes
# - Reference issues and pull requests liberally after the first line
