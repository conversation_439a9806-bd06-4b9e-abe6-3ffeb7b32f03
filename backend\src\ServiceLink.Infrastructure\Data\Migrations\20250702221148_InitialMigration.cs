﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ServiceLink.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class InitialMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false, comment: "Adresse email de l'utilisateur"),
                    FirstName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "Prénom de l'utilisateur"),
                    LastName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, comment: "Nom de famille de l'utilisateur"),
                    PhoneNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true, comment: "Numéro de téléphone de l'utilisateur"),
                    PhoneCountryCode = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: true, comment: "Code pays du numéro de téléphone"),
                    PhoneNationalNumber = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: true, comment: "Numéro national sans le code pays"),
                    Role = table.Column<int>(type: "integer", nullable: false, comment: "Rôle de l'utilisateur dans le système"),
                    PasswordHash = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false, comment: "Hash du mot de passe"),
                    PasswordSalt = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false, comment: "Salt pour le hachage du mot de passe"),
                    IsEmailConfirmed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false, comment: "Indique si l'email est confirmé"),
                    IsPhoneConfirmed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false, comment: "Indique si le numéro de téléphone est confirmé"),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true, comment: "Indique si l'utilisateur est actif"),
                    LastLoginAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, comment: "Date de dernière connexion"),
                    FailedLoginAttempts = table.Column<int>(type: "integer", nullable: false, defaultValue: 0, comment: "Nombre de tentatives de connexion échouées"),
                    LockedUntil = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, comment: "Date de fin de verrouillage du compte"),
                    EmailConfirmationToken = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "Token de confirmation d'email"),
                    EmailConfirmationTokenExpiry = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, comment: "Date d'expiration du token de confirmation d'email"),
                    PasswordResetToken = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "Token de réinitialisation de mot de passe"),
                    PasswordResetTokenExpiry = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, comment: "Date d'expiration du token de réinitialisation"),
                    TwoFactorSecret = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "Secret pour l'authentification à deux facteurs"),
                    IsTwoFactorEnabled = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false, comment: "Indique si l'authentification à deux facteurs est activée"),
                    TwoFactorRecoveryCodes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true, comment: "Codes de récupération pour l'authentification à deux facteurs"),
                    AvatarUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true, comment: "URL de l'avatar de l'utilisateur"),
                    ProfileCompletionPercentage = table.Column<int>(type: "integer", nullable: false, defaultValue: 0, comment: "Pourcentage de complétion du profil"),
                    Preferences = table.Column<string>(type: "jsonb", maxLength: 500, nullable: true, comment: "Préférences de l'utilisateur au format JSON"),
                    Language = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "fr-FR", comment: "Langue préférée de l'utilisateur"),
                    TimeZone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Europe/Paris", comment: "Fuseau horaire de l'utilisateur"),
                    PasswordChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, comment: "Date de dernière modification du mot de passe"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                    table.CheckConstraint("CK_Users_EmailTokenExpiry", "\"EmailConfirmationToken\" IS NULL OR \"EmailConfirmationTokenExpiry\" IS NOT NULL");
                    table.CheckConstraint("CK_Users_FailedLoginAttempts", "\"FailedLoginAttempts\" >= 0");
                    table.CheckConstraint("CK_Users_PasswordTokenExpiry", "\"PasswordResetToken\" IS NULL OR \"PasswordResetTokenExpiry\" IS NOT NULL");
                    table.CheckConstraint("CK_Users_ProfileCompletionPercentage", "\"ProfileCompletionPercentage\" >= 0 AND \"ProfileCompletionPercentage\" <= 100");
                    table.CheckConstraint("CK_Users_TwoFactorConsistency", "\"IsTwoFactorEnabled\" = false OR \"TwoFactorSecret\" IS NOT NULL");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Users_CreatedAt",
                table: "Users",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "Users",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_EmailConfirmationToken",
                table: "Users",
                column: "EmailConfirmationToken",
                filter: "\"EmailConfirmationToken\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_IsActive",
                table: "Users",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Users_IsDeleted",
                table: "Users",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_Users_IsEmailConfirmed",
                table: "Users",
                column: "IsEmailConfirmed");

            migrationBuilder.CreateIndex(
                name: "IX_Users_LastLoginAt",
                table: "Users",
                column: "LastLoginAt");

            migrationBuilder.CreateIndex(
                name: "IX_Users_LockedAccount",
                table: "Users",
                columns: new[] { "LockedUntil", "FailedLoginAttempts" },
                filter: "\"LockedUntil\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_PasswordResetToken",
                table: "Users",
                column: "PasswordResetToken",
                filter: "\"PasswordResetToken\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_PhoneNumber",
                table: "Users",
                column: "PhoneNumber",
                unique: true,
                filter: "\"PhoneNumber\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Role",
                table: "Users",
                column: "Role");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Users");
        }
    }
}
