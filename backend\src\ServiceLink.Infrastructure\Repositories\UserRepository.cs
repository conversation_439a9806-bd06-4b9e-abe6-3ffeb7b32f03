using Microsoft.EntityFrameworkCore;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;
using ServiceLink.Infrastructure.Data;

namespace ServiceLink.Infrastructure.Repositories;

/// <summary>
/// Implémentation du repository pour les utilisateurs
/// </summary>
public class UserRepository : Repository<User>, IUserRepository
{
    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="context">Contexte de base de données</param>
    public UserRepository(ServiceLinkDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Trouve un utilisateur par son adresse email
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    public async Task<User?> GetByEmailAsync(Email email, CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .FirstOrDefaultAsync(u => u.Email!.Value == email.Value, cancellationToken);
    }

    /// <summary>
    /// Trouve un utilisateur par son adresse email (string)
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        var normalizedEmail = email.ToLowerInvariant();
        return await Context.Users
            .FirstOrDefaultAsync(u => u.Email!.Value == normalizedEmail, cancellationToken);
    }

    /// <summary>
    /// Trouve un utilisateur par son numéro de téléphone
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    public async Task<User?> GetByPhoneNumberAsync(PhoneNumber phoneNumber, CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .FirstOrDefaultAsync(u => u.PhoneNumber!.Value == phoneNumber.Value, cancellationToken);
    }

    /// <summary>
    /// Trouve un utilisateur par son token de confirmation d'email
    /// </summary>
    /// <param name="token">Token de confirmation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    public async Task<User?> GetByEmailConfirmationTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .FirstOrDefaultAsync(u => u.EmailConfirmationToken == token && 
                                     u.EmailConfirmationTokenExpiry > DateTime.UtcNow, 
                                cancellationToken);
    }

    /// <summary>
    /// Trouve un utilisateur par son token de réinitialisation de mot de passe
    /// </summary>
    /// <param name="token">Token de réinitialisation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    public async Task<User?> GetByPasswordResetTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .FirstOrDefaultAsync(u => u.PasswordResetToken == token && 
                                     u.PasswordResetTokenExpiry > DateTime.UtcNow, 
                                cancellationToken);
    }

    /// <summary>
    /// Vérifie si un email existe déjà
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="excludeUserId">ID utilisateur à exclure de la vérification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email existe</returns>
    public async Task<bool> EmailExistsAsync(Email email, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Users.Where(u => u.Email!.Value == email.Value);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    /// <summary>
    /// Vérifie si un numéro de téléphone existe déjà
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone</param>
    /// <param name="excludeUserId">ID utilisateur à exclure de la vérification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si le numéro existe</returns>
    public async Task<bool> PhoneNumberExistsAsync(PhoneNumber phoneNumber, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Users.Where(u => u.PhoneNumber!.Value == phoneNumber.Value);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les utilisateurs par rôle
    /// </summary>
    /// <param name="role">Rôle recherché</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs avec ce rôle</returns>
    public async Task<IEnumerable<User>> GetByRoleAsync(UserRole role, CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .Where(u => u.Role == role)
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les utilisateurs actifs
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs actifs</returns>
    public async Task<IEnumerable<User>> GetActiveUsersAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .Where(u => u.IsActive)
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les utilisateurs inactifs
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs inactifs</returns>
    public async Task<IEnumerable<User>> GetInactiveUsersAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .Where(u => !u.IsActive)
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les utilisateurs avec email non confirmé
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs avec email non confirmé</returns>
    public async Task<IEnumerable<User>> GetUsersWithUnconfirmedEmailAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .Where(u => !u.IsEmailConfirmed)
            .OrderBy(u => u.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les utilisateurs verrouillés
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs verrouillés</returns>
    public async Task<IEnumerable<User>> GetLockedUsersAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await Context.Users
            .Where(u => u.LockedUntil.HasValue && u.LockedUntil.Value > now)
            .OrderBy(u => u.LockedUntil)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les utilisateurs créés dans une période
    /// </summary>
    /// <param name="startDate">Date de début</param>
    /// <param name="endDate">Date de fin</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs créés dans la période</returns>
    public async Task<IEnumerable<User>> GetUsersCreatedBetweenAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .Where(u => u.CreatedAt >= startDate && u.CreatedAt <= endDate)
            .OrderBy(u => u.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les utilisateurs connectés récemment
    /// </summary>
    /// <param name="since">Date depuis laquelle chercher</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs connectés récemment</returns>
    public async Task<IEnumerable<User>> GetRecentlyLoggedInUsersAsync(DateTime since, CancellationToken cancellationToken = default)
    {
        return await Context.Users
            .Where(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= since)
            .OrderByDescending(u => u.LastLoginAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Recherche des utilisateurs par nom ou email
    /// </summary>
    /// <param name="searchTerm">Terme de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs correspondants</returns>
    public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var normalizedTerm = searchTerm.ToLowerInvariant();
        
        return await Context.Users
            .Where(u => u.FirstName.ToLower().Contains(normalizedTerm) ||
                       u.LastName.ToLower().Contains(normalizedTerm) ||
                       u.Email!.Value.Contains(normalizedTerm))
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Obtient les statistiques des utilisateurs
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statistiques des utilisateurs</returns>
    public async Task<UserStatistics> GetUserStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var startOfMonth = new DateTime(now.Year, now.Month, 1);
        var startOfDay = now.Date;
        var startOfWeek = startOfDay.AddDays(-(int)now.DayOfWeek);

        var stats = new UserStatistics
        {
            TotalUsers = await Context.Users.CountAsync(cancellationToken),
            ActiveUsers = await Context.Users.CountAsync(u => u.IsActive, cancellationToken),
            InactiveUsers = await Context.Users.CountAsync(u => !u.IsActive, cancellationToken),
            EmailConfirmedUsers = await Context.Users.CountAsync(u => u.IsEmailConfirmed, cancellationToken),
            EmailUnconfirmedUsers = await Context.Users.CountAsync(u => !u.IsEmailConfirmed, cancellationToken),
            LockedUsers = await Context.Users.CountAsync(u => u.LockedUntil.HasValue && u.LockedUntil.Value > now, cancellationToken),
            TwoFactorEnabledUsers = await Context.Users.CountAsync(u => u.IsTwoFactorEnabled, cancellationToken),
            NewUsersThisMonth = await Context.Users.CountAsync(u => u.CreatedAt >= startOfMonth, cancellationToken),
            UsersLoggedInToday = await Context.Users.CountAsync(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= startOfDay, cancellationToken),
            UsersLoggedInThisWeek = await Context.Users.CountAsync(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= startOfWeek, cancellationToken),
            AverageProfileCompletion = await Context.Users.AverageAsync(u => (double)u.ProfileCompletionPercentage, cancellationToken)
        };

        // Répartition par rôle
        var roleStats = await Context.Users
            .GroupBy(u => u.Role)
            .Select(g => new { Role = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);

        stats.UsersByRole = roleStats.ToDictionary(r => r.Role, r => r.Count);

        return stats;
    }

    /// <summary>
    /// Obtient les utilisateurs avec pagination et filtres
    /// </summary>
    /// <param name="pageNumber">Numéro de page</param>
    /// <param name="pageSize">Taille de la page</param>
    /// <param name="role">Filtre par rôle (optionnel)</param>
    /// <param name="isActive">Filtre par statut actif (optionnel)</param>
    /// <param name="isEmailConfirmed">Filtre par email confirmé (optionnel)</param>
    /// <param name="searchTerm">Terme de recherche (optionnel)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat paginé avec filtres</returns>
    public async Task<PagedResult<User>> GetUsersPagedAsync(
        int pageNumber, 
        int pageSize, 
        UserRole? role = null, 
        bool? isActive = null, 
        bool? isEmailConfirmed = null, 
        string? searchTerm = null, 
        CancellationToken cancellationToken = default)
    {
        var query = Context.Users.AsQueryable();

        // Application des filtres
        if (role.HasValue)
        {
            query = query.Where(u => u.Role == role.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(u => u.IsActive == isActive.Value);
        }

        if (isEmailConfirmed.HasValue)
        {
            query = query.Where(u => u.IsEmailConfirmed == isEmailConfirmed.Value);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var normalizedTerm = searchTerm.ToLowerInvariant();
            query = query.Where(u => u.FirstName.ToLower().Contains(normalizedTerm) ||
                                    u.LastName.ToLower().Contains(normalizedTerm) ||
                                    u.Email!.Value.Contains(normalizedTerm));
        }

        // Tri par défaut
        query = query.OrderBy(u => u.LastName).ThenBy(u => u.FirstName);

        // Compte total
        var totalCount = await query.CountAsync(cancellationToken);

        // Pagination
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<User>(items, totalCount, pageNumber, pageSize);
    }

    /// <summary>
    /// Nettoie les tokens expirés
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre de tokens nettoyés</returns>
    public async Task<int> CleanupExpiredTokensAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var usersWithExpiredTokens = await Context.Users
            .Where(u => (u.EmailConfirmationToken != null && u.EmailConfirmationTokenExpiry <= now) ||
                       (u.PasswordResetToken != null && u.PasswordResetTokenExpiry <= now))
            .ToListAsync(cancellationToken);

        var count = 0;
        foreach (var user in usersWithExpiredTokens)
        {
            if (user.EmailConfirmationToken != null && user.EmailConfirmationTokenExpiry <= now)
            {
                user.ClearEmailConfirmationToken();
                count++;
            }

            if (user.PasswordResetToken != null && user.PasswordResetTokenExpiry <= now)
            {
                user.ClearPasswordResetToken();
                count++;
            }
        }

        if (count > 0)
        {
            await Context.SaveChangesAsync(cancellationToken);
        }

        return count;
    }

    /// <summary>
    /// Déverrouille automatiquement les comptes dont la période de verrouillage est expirée
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre de comptes déverrouillés</returns>
    public async Task<int> UnlockExpiredAccountsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var lockedUsers = await Context.Users
            .Where(u => u.LockedUntil.HasValue && u.LockedUntil.Value <= now)
            .ToListAsync(cancellationToken);

        foreach (var user in lockedUsers)
        {
            user.UnlockAccount();
        }

        if (lockedUsers.Any())
        {
            await Context.SaveChangesAsync(cancellationToken);
        }

        return lockedUsers.Count;
    }
}
