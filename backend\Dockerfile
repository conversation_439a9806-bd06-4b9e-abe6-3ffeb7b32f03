# Dockerfile pour ServiceLink API
# Utilise une approche multi-stage pour optimiser la taille de l'image finale

# Stage 1: Build
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers de projet pour la restauration des packages
COPY ["src/ServiceLink.API/ServiceLink.API.csproj", "src/ServiceLink.API/"]
COPY ["src/ServiceLink.Application/ServiceLink.Application.csproj", "src/ServiceLink.Application/"]
COPY ["src/ServiceLink.Domain/ServiceLink.Domain.csproj", "src/ServiceLink.Domain/"]
COPY ["src/ServiceLink.Infrastructure/ServiceLink.Infrastructure.csproj", "src/ServiceLink.Infrastructure/"]

# Restaurer les dépendances
RUN dotnet restore "src/ServiceLink.API/ServiceLink.API.csproj"

# Copier tout le code source
COPY . .

# Build de l'application
WORKDIR "/src/src/ServiceLink.API"
RUN dotnet build "ServiceLink.API.csproj" -c Release -o /app/build

# Stage 2: Publish
FROM build AS publish
RUN dotnet publish "ServiceLink.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Stage 3: Runtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app

# Créer un utilisateur non-root pour la sécurité
RUN addgroup --system --gid 1001 servicelink && \
    adduser --system --uid 1001 --gid 1001 servicelink

# Installer les dépendances système nécessaires
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copier les fichiers publiés
COPY --from=publish /app/publish .

# Changer le propriétaire des fichiers
RUN chown -R servicelink:servicelink /app

# Utiliser l'utilisateur non-root
USER servicelink

# Exposer le port
EXPOSE 8080

# Variables d'environnement par défaut
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080

# Point d'entrée
ENTRYPOINT ["dotnet", "ServiceLink.API.dll"]

# Healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Labels pour la métadonnée
LABEL maintainer="ServiceLink Team"
LABEL version="1.0.0"
LABEL description="ServiceLink API - Plateforme de gestion de services"
