using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace ServiceLink.API.Configuration;

/// <summary>
/// Configuration pour Swagger/OpenAPI
/// </summary>
public static class SwaggerConfiguration
{
    /// <summary>
    /// Configure Swagger avec authentification JWT
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="swaggerSettings">Configuration Swagger</param>
    /// <returns>Collection de services</returns>
    public static IServiceCollection AddSwaggerConfiguration(this IServiceCollection services, SwaggerSettings swaggerSettings)
    {
        services.AddSwaggerGen(options =>
        {
            // Configuration de base
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = swaggerSettings.Title,
                Version = swaggerSettings.Version,
                Description = swaggerSettings.Description,
                Contact = new OpenApiContact
                {
                    Name = swaggerSettings.ContactName,
                    Email = swaggerSettings.ContactEmail
                },
                License = new OpenApiLicense
                {
                    Name = swaggerSettings.LicenseName
                }
            });

            // Configuration JWT
            if (swaggerSettings.EnableJwtAuthentication)
            {
                options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.Http,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "Entrez 'Bearer' suivi d'un espace et de votre token JWT.\n\nExemple: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                });

                options.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            }

            // Commentaires XML
            if (swaggerSettings.EnableXmlComments)
            {
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                
                if (File.Exists(xmlPath))
                {
                    options.IncludeXmlComments(xmlPath);
                }

                // Inclure les commentaires des autres assemblies
                var applicationXmlFile = "ServiceLink.Application.xml";
                var applicationXmlPath = Path.Combine(AppContext.BaseDirectory, applicationXmlFile);
                if (File.Exists(applicationXmlPath))
                {
                    options.IncludeXmlComments(applicationXmlPath);
                }

                var domainXmlFile = "ServiceLink.Domain.xml";
                var domainXmlPath = Path.Combine(AppContext.BaseDirectory, domainXmlFile);
                if (File.Exists(domainXmlPath))
                {
                    options.IncludeXmlComments(domainXmlPath);
                }
            }

            // Configuration des schémas
            options.CustomSchemaIds(type => type.FullName?.Replace("+", "."));

            // Filtres personnalisés
            options.OperationFilter<SwaggerOperationFilter>();
            options.DocumentFilter<SwaggerDocumentFilter>();
        });

        return services;
    }

    /// <summary>
    /// Configure l'interface Swagger
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <param name="swaggerSettings">Configuration Swagger</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseSwaggerConfiguration(this IApplicationBuilder app, SwaggerSettings swaggerSettings)
    {
        app.UseSwagger(options =>
        {
            options.RouteTemplate = "api/docs/{documentName}/swagger.json";
        });

        app.UseSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/api/docs/v1/swagger.json", $"{swaggerSettings.Title} {swaggerSettings.Version}");
            options.RoutePrefix = "api/docs";
            options.DocumentTitle = swaggerSettings.Title;
            
            // Configuration de l'interface
            options.DefaultModelsExpandDepth(2);
            options.DefaultModelExpandDepth(2);
            options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
            options.EnableDeepLinking();
            options.DisplayOperationId();
            options.DisplayRequestDuration();
            
            // Thème et style
            options.InjectStylesheet("/swagger-ui/custom.css");
            
            // Configuration JWT
            if (swaggerSettings.EnableJwtAuthentication)
            {
                options.OAuthClientId("swagger-ui");
                options.OAuthAppName(swaggerSettings.Title);
                options.OAuthUsePkce();
            }
        });

        return app;
    }
}

/// <summary>
/// Filtre d'opération pour Swagger
/// </summary>
public class SwaggerOperationFilter : IOperationFilter
{
    /// <summary>
    /// Applique le filtre à une opération
    /// </summary>
    /// <param name="operation">Opération OpenAPI</param>
    /// <param name="context">Contexte</param>
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Ajouter des exemples de réponses d'erreur
        if (!operation.Responses.ContainsKey("400"))
        {
            operation.Responses.Add("400", new OpenApiResponse
            {
                Description = "Requête invalide",
                Content = new Dictionary<string, OpenApiMediaType>
                {
                    ["application/json"] = new OpenApiMediaType
                    {
                        Schema = new OpenApiSchema
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.Schema,
                                Id = "ErrorResponse"
                            }
                        }
                    }
                }
            });
        }

        if (!operation.Responses.ContainsKey("401"))
        {
            operation.Responses.Add("401", new OpenApiResponse
            {
                Description = "Non autorisé"
            });
        }

        if (!operation.Responses.ContainsKey("500"))
        {
            operation.Responses.Add("500", new OpenApiResponse
            {
                Description = "Erreur interne du serveur",
                Content = new Dictionary<string, OpenApiMediaType>
                {
                    ["application/json"] = new OpenApiMediaType
                    {
                        Schema = new OpenApiSchema
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.Schema,
                                Id = "ErrorResponse"
                            }
                        }
                    }
                }
            });
        }

        // Marquer les paramètres obligatoires
        foreach (var parameter in operation.Parameters ?? Enumerable.Empty<OpenApiParameter>())
        {
            if (parameter.Required)
            {
                parameter.Description = $"**[Obligatoire]** {parameter.Description}";
            }
        }
    }
}

/// <summary>
/// Filtre de document pour Swagger
/// </summary>
public class SwaggerDocumentFilter : IDocumentFilter
{
    /// <summary>
    /// Applique le filtre au document
    /// </summary>
    /// <param name="swaggerDoc">Document OpenAPI</param>
    /// <param name="context">Contexte</param>
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // Ajouter le schéma ErrorResponse
        swaggerDoc.Components.Schemas.Add("ErrorResponse", new OpenApiSchema
        {
            Type = "object",
            Properties = new Dictionary<string, OpenApiSchema>
            {
                ["status"] = new OpenApiSchema { Type = "integer", Description = "Code de statut HTTP" },
                ["title"] = new OpenApiSchema { Type = "string", Description = "Titre de l'erreur" },
                ["detail"] = new OpenApiSchema { Type = "string", Description = "Détail de l'erreur" },
                ["traceId"] = new OpenApiSchema { Type = "string", Description = "ID de trace" },
                ["timestamp"] = new OpenApiSchema { Type = "string", Format = "date-time", Description = "Timestamp" },
                ["path"] = new OpenApiSchema { Type = "string", Description = "Chemin de la requête" },
                ["method"] = new OpenApiSchema { Type = "string", Description = "Méthode HTTP" },
                ["errors"] = new OpenApiSchema
                {
                    Type = "object",
                    AdditionalProperties = new OpenApiSchema
                    {
                        Type = "array",
                        Items = new OpenApiSchema { Type = "string" }
                    },
                    Description = "Erreurs de validation détaillées"
                }
            }
        });

        // Trier les endpoints par tags
        var sortedPaths = swaggerDoc.Paths
            .OrderBy(p => p.Key)
            .ToDictionary(p => p.Key, p => p.Value);
        
        swaggerDoc.Paths.Clear();
        foreach (var path in sortedPaths)
        {
            swaggerDoc.Paths.Add(path.Key, path.Value);
        }
    }
}
