using BCrypt.Net;
using ServiceLink.Application.Interfaces;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace ServiceLink.Infrastructure.Services;

/// <summary>
/// Service pour la gestion des mots de passe
/// </summary>
public class PasswordService : IPasswordService
{
    private const int WorkFactor = 12; // BCrypt work factor (2^12 = 4096 rounds)
    private const int MinPasswordLength = 8;
    private const int MaxPasswordLength = 128;

    /// <summary>
    /// Hache un mot de passe avec un salt généré automatiquement
    /// </summary>
    /// <param name="password">Mot de passe en clair</param>
    /// <returns>Tuple contenant le hash et le salt</returns>
    public (string hash, string salt) HashPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Le mot de passe ne peut pas être vide.", nameof(password));

        if (password.Length > MaxPasswordLength)
            throw new ArgumentException($"Le mot de passe ne peut pas dépasser {MaxPasswordLength} caractères.", nameof(password));

        // BCrypt génère automatiquement le salt et l'inclut dans le hash
        var hash = BCrypt.Net.BCrypt.HashPassword(password, WorkFactor);
        
        // Pour la compatibilité avec l'interface, on extrait le salt du hash BCrypt
        var salt = hash.Substring(0, 29); // Les 29 premiers caractères contiennent le salt et les paramètres
        
        return (hash, salt);
    }

    /// <summary>
    /// Vérifie si un mot de passe correspond au hash stocké
    /// </summary>
    /// <param name="password">Mot de passe en clair</param>
    /// <param name="hash">Hash stocké</param>
    /// <param name="salt">Salt utilisé (non utilisé avec BCrypt mais requis par l'interface)</param>
    /// <returns>True si le mot de passe est correct</returns>
    public bool VerifyPassword(string password, string hash, string salt)
    {
        if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hash))
            return false;

        try
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Génère un mot de passe aléatoire sécurisé
    /// </summary>
    /// <param name="length">Longueur du mot de passe (défaut: 12)</param>
    /// <param name="includeSpecialChars">Inclure des caractères spéciaux</param>
    /// <returns>Mot de passe généré</returns>
    public string GeneratePassword(int length = 12, bool includeSpecialChars = true)
    {
        if (length < MinPasswordLength)
            throw new ArgumentException($"La longueur minimale est {MinPasswordLength} caractères.", nameof(length));

        if (length > MaxPasswordLength)
            throw new ArgumentException($"La longueur maximale est {MaxPasswordLength} caractères.", nameof(length));

        const string lowercase = "abcdefghijklmnopqrstuvwxyz";
        const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string digits = "0123456789";
        const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        var allChars = lowercase + uppercase + digits;
        if (includeSpecialChars)
            allChars += specialChars;

        var password = new StringBuilder();
        using var rng = RandomNumberGenerator.Create();

        // Garantir au moins un caractère de chaque type
        password.Append(GetRandomChar(lowercase, rng));
        password.Append(GetRandomChar(uppercase, rng));
        password.Append(GetRandomChar(digits, rng));
        
        if (includeSpecialChars)
            password.Append(GetRandomChar(specialChars, rng));

        // Remplir le reste avec des caractères aléatoires
        var remainingLength = length - password.Length;
        for (int i = 0; i < remainingLength; i++)
        {
            password.Append(GetRandomChar(allChars, rng));
        }

        // Mélanger les caractères pour éviter un pattern prévisible
        return ShuffleString(password.ToString(), rng);
    }

    /// <summary>
    /// Valide la force d'un mot de passe
    /// </summary>
    /// <param name="password">Mot de passe à valider</param>
    /// <returns>Résultat de la validation</returns>
    public PasswordValidationResult ValidatePasswordStrength(string password)
    {
        var result = new PasswordValidationResult();

        if (string.IsNullOrWhiteSpace(password))
        {
            result.Errors.Add("Le mot de passe ne peut pas être vide.");
            result.StrengthLevel = PasswordStrengthLevel.VeryWeak;
            return result;
        }

        var score = 0;
        var suggestions = new List<string>();

        // Longueur
        if (password.Length < MinPasswordLength)
        {
            result.Errors.Add($"Le mot de passe doit contenir au moins {MinPasswordLength} caractères.");
        }
        else if (password.Length >= MinPasswordLength)
        {
            score += 10;
            if (password.Length >= 12) score += 10;
            if (password.Length >= 16) score += 10;
        }

        if (password.Length > MaxPasswordLength)
        {
            result.Errors.Add($"Le mot de passe ne peut pas dépasser {MaxPasswordLength} caractères.");
        }

        // Minuscules
        if (Regex.IsMatch(password, @"[a-z]"))
        {
            score += 10;
        }
        else
        {
            suggestions.Add("Ajouter des lettres minuscules.");
        }

        // Majuscules
        if (Regex.IsMatch(password, @"[A-Z]"))
        {
            score += 10;
        }
        else
        {
            suggestions.Add("Ajouter des lettres majuscules.");
        }

        // Chiffres
        if (Regex.IsMatch(password, @"[0-9]"))
        {
            score += 10;
        }
        else
        {
            suggestions.Add("Ajouter des chiffres.");
        }

        // Caractères spéciaux
        if (Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]"))
        {
            score += 15;
        }
        else
        {
            suggestions.Add("Ajouter des caractères spéciaux (!@#$%^&*...).");
        }

        // Diversité des caractères
        var uniqueChars = password.Distinct().Count();
        var diversityRatio = (double)uniqueChars / password.Length;
        if (diversityRatio > 0.7) score += 10;
        else if (diversityRatio < 0.5) suggestions.Add("Éviter la répétition excessive de caractères.");

        // Patterns courants (malus)
        if (Regex.IsMatch(password, @"(.)\1{2,}")) // 3+ caractères identiques consécutifs
        {
            score -= 10;
            suggestions.Add("Éviter la répétition de caractères consécutifs.");
        }

        if (Regex.IsMatch(password, @"(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)", RegexOptions.IgnoreCase))
        {
            score -= 15;
            suggestions.Add("Éviter les séquences de caractères (123, abc, etc.).");
        }

        // Mots courants (basique)
        var commonPasswords = new[] { "password", "123456", "qwerty", "admin", "letmein", "welcome", "monkey", "dragon" };
        if (commonPasswords.Any(common => password.ToLowerInvariant().Contains(common)))
        {
            score -= 20;
            suggestions.Add("Éviter les mots de passe courants.");
        }

        // Déterminer le niveau de force
        result.StrengthScore = Math.Max(0, Math.Min(100, score));
        result.StrengthLevel = result.StrengthScore switch
        {
            < 30 => PasswordStrengthLevel.VeryWeak,
            < 50 => PasswordStrengthLevel.Weak,
            < 70 => PasswordStrengthLevel.Medium,
            < 90 => PasswordStrengthLevel.Strong,
            _ => PasswordStrengthLevel.VeryStrong
        };

        result.IsValid = result.Errors.Count == 0 && result.StrengthLevel >= PasswordStrengthLevel.Medium;
        result.Suggestions = suggestions;

        return result;
    }

    /// <summary>
    /// Génère un token de réinitialisation de mot de passe
    /// </summary>
    /// <returns>Token sécurisé</returns>
    public string GeneratePasswordResetToken()
    {
        return GenerateSecureToken(32);
    }

    /// <summary>
    /// Génère un token de confirmation d'email
    /// </summary>
    /// <returns>Token sécurisé</returns>
    public string GenerateEmailConfirmationToken()
    {
        return GenerateSecureToken(32);
    }

    /// <summary>
    /// Génère un token sécurisé
    /// </summary>
    /// <param name="length">Longueur en bytes</param>
    /// <returns>Token en base64</returns>
    private static string GenerateSecureToken(int length = 32)
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    /// <summary>
    /// Obtient un caractère aléatoire d'une chaîne
    /// </summary>
    /// <param name="chars">Chaîne de caractères</param>
    /// <param name="rng">Générateur de nombres aléatoires</param>
    /// <returns>Caractère aléatoire</returns>
    private static char GetRandomChar(string chars, RandomNumberGenerator rng)
    {
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var value = BitConverter.ToUInt32(bytes, 0);
        return chars[(int)(value % chars.Length)];
    }

    /// <summary>
    /// Mélange les caractères d'une chaîne
    /// </summary>
    /// <param name="input">Chaîne à mélanger</param>
    /// <param name="rng">Générateur de nombres aléatoires</param>
    /// <returns>Chaîne mélangée</returns>
    private static string ShuffleString(string input, RandomNumberGenerator rng)
    {
        var array = input.ToCharArray();
        var n = array.Length;
        
        for (int i = n - 1; i > 0; i--)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var j = (int)(BitConverter.ToUInt32(bytes, 0) % (i + 1));
            (array[i], array[j]) = (array[j], array[i]);
        }

        return new string(array);
    }

    /// <summary>
    /// Génère un mot de passe aléatoire sécurisé
    /// </summary>
    /// <param name="length">Longueur du mot de passe (minimum 8)</param>
    /// <returns>Mot de passe généré</returns>
    public string GenerateRandomPassword(int length = 12)
    {
        if (length < MinPasswordLength)
            throw new ArgumentException($"La longueur minimale est de {MinPasswordLength} caractères.", nameof(length));

        const string lowercase = "abcdefghijklmnopqrstuvwxyz";
        const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string digits = "0123456789";
        const string special = "!@#$%^&*()_+-=[]{}|;':\"\\,.<>/?";

        var password = new StringBuilder();
        var random = new Random();

        // Assurer au moins un caractère de chaque type
        password.Append(lowercase[random.Next(lowercase.Length)]);
        password.Append(uppercase[random.Next(uppercase.Length)]);
        password.Append(digits[random.Next(digits.Length)]);
        password.Append(special[random.Next(special.Length)]);

        // Remplir le reste avec des caractères aléatoires
        var allChars = lowercase + uppercase + digits + special;
        for (int i = 4; i < length; i++)
        {
            password.Append(allChars[random.Next(allChars.Length)]);
        }

        // Mélanger les caractères
        var chars = password.ToString().ToCharArray();
        for (int i = chars.Length - 1; i > 0; i--)
        {
            int j = random.Next(i + 1);
            (chars[i], chars[j]) = (chars[j], chars[i]);
        }

        return new string(chars);
    }

    /// <summary>
    /// Vérifie si un mot de passe est compromis (dans une liste de mots de passe courants)
    /// </summary>
    /// <param name="password">Mot de passe à vérifier</param>
    /// <returns>True si le mot de passe est compromis</returns>
    public bool IsPasswordCompromised(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Le mot de passe ne peut pas être vide.", nameof(password));

        // Liste des mots de passe les plus courants (version simplifiée)
        var commonPasswords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "password", "123456", "password123", "admin", "qwerty",
            "letmein", "welcome", "monkey", "1234567890", "abc123",
            "password1", "123456789", "welcome123", "admin123", "root",
            "toor", "pass", "test", "guest", "user", "login", "demo",
            "sample", "temp", "temporary", "changeme", "newpassword"
        };

        return commonPasswords.Contains(password);
    }
}
