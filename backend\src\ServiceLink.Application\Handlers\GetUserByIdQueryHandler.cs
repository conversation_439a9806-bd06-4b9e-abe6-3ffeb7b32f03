using MediatR;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.DTOs;
using ServiceLink.Application.Queries;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;

namespace ServiceLink.Application.Handlers;

/// <summary>
/// Handler pour la requête de récupération d'utilisateur par ID
/// </summary>
public class GetUserByIdQueryHandler : IRequestHandler<GetUserByIdQuery, UserResponse?>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GetUserByIdQueryHandler> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="unitOfWork">Unit of Work</param>
    /// <param name="logger">Logger</param>
    public GetUserByIdQueryHandler(IUnitOfWork unitOfWork, ILogger<GetUserByIdQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête de récupération d'utilisateur par ID
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Réponse utilisateur ou null si non trouvé</returns>
    public async Task<UserResponse?> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Récupération de l'utilisateur avec l'ID {UserId}", request.UserId);

        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(request.UserId, cancellationToken);
            
            if (user == null)
            {
                _logger.LogDebug("Aucun utilisateur trouvé avec l'ID {UserId}", request.UserId);
                return null;
            }

            _logger.LogDebug("Utilisateur trouvé: {Email}", user.Email.Value);
            return MapToUserResponse(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'utilisateur avec l'ID {UserId}", request.UserId);
            throw;
        }
    }

    /// <summary>
    /// Convertit une entité User en UserResponse
    /// </summary>
    /// <param name="user">Entité utilisateur</param>
    /// <returns>DTO de réponse</returns>
    private static UserResponse MapToUserResponse(User user)
    {
        return new UserResponse
        {
            Id = user.Id,
            Email = user.Email.Value,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            RoleDescription = user.Role.GetDescription(),
            IsEmailConfirmed = user.IsEmailConfirmed,
            IsPhoneConfirmed = user.IsPhoneConfirmed,
            IsActive = user.IsActive,
            IsTwoFactorEnabled = user.IsTwoFactorEnabled,
            AvatarUrl = user.AvatarUrl,
            ProfileCompletionPercentage = user.ProfileCompletionPercentage,
            Language = user.Language,
            TimeZone = user.TimeZone,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            LastLoginAt = user.LastLoginAt
        };
    }
}

/// <summary>
/// Handler pour la requête de récupération d'utilisateur par email
/// </summary>
public class GetUserByEmailQueryHandler : IRequestHandler<GetUserByEmailQuery, UserResponse?>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GetUserByEmailQueryHandler> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="unitOfWork">Unit of Work</param>
    /// <param name="logger">Logger</param>
    public GetUserByEmailQueryHandler(IUnitOfWork unitOfWork, ILogger<GetUserByEmailQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête de récupération d'utilisateur par email
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Réponse utilisateur ou null si non trouvé</returns>
    public async Task<UserResponse?> Handle(GetUserByEmailQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Récupération de l'utilisateur avec l'email {Email}", request.Email);

        try
        {
            var user = await _unitOfWork.Users.GetByEmailAsync(request.Email, cancellationToken);
            
            if (user == null)
            {
                _logger.LogDebug("Aucun utilisateur trouvé avec l'email {Email}", request.Email);
                return null;
            }

            _logger.LogDebug("Utilisateur trouvé avec l'ID {UserId}", user.Id);
            return MapToUserResponse(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'utilisateur avec l'email {Email}", request.Email);
            throw;
        }
    }

    /// <summary>
    /// Convertit une entité User en UserResponse
    /// </summary>
    /// <param name="user">Entité utilisateur</param>
    /// <returns>DTO de réponse</returns>
    private static UserResponse MapToUserResponse(User user)
    {
        return new UserResponse
        {
            Id = user.Id,
            Email = user.Email.Value,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            RoleDescription = user.Role.GetDescription(),
            IsEmailConfirmed = user.IsEmailConfirmed,
            IsPhoneConfirmed = user.IsPhoneConfirmed,
            IsActive = user.IsActive,
            IsTwoFactorEnabled = user.IsTwoFactorEnabled,
            AvatarUrl = user.AvatarUrl,
            ProfileCompletionPercentage = user.ProfileCompletionPercentage,
            Language = user.Language,
            TimeZone = user.TimeZone,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            LastLoginAt = user.LastLoginAt
        };
    }
}

/// <summary>
/// Handler pour la requête de récupération des statistiques utilisateurs
/// </summary>
public class GetUserStatisticsQueryHandler : IRequestHandler<GetUserStatisticsQuery, UserStatisticsResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GetUserStatisticsQueryHandler> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="unitOfWork">Unit of Work</param>
    /// <param name="logger">Logger</param>
    public GetUserStatisticsQueryHandler(IUnitOfWork unitOfWork, ILogger<GetUserStatisticsQueryHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// Traite la requête de récupération des statistiques utilisateurs
    /// </summary>
    /// <param name="request">Requête</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statistiques des utilisateurs</returns>
    public async Task<UserStatisticsResponse> Handle(GetUserStatisticsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Récupération des statistiques utilisateurs");

        try
        {
            var stats = await _unitOfWork.Users.GetUserStatisticsAsync(cancellationToken);

            return new UserStatisticsResponse
            {
                TotalUsers = stats.TotalUsers,
                ActiveUsers = stats.ActiveUsers,
                InactiveUsers = stats.InactiveUsers,
                EmailConfirmedUsers = stats.EmailConfirmedUsers,
                EmailUnconfirmedUsers = stats.EmailUnconfirmedUsers,
                LockedUsers = stats.LockedUsers,
                TwoFactorEnabledUsers = stats.TwoFactorEnabledUsers,
                UsersByRole = stats.UsersByRole.ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value),
                NewUsersThisMonth = stats.NewUsersThisMonth,
                UsersLoggedInToday = stats.UsersLoggedInToday,
                UsersLoggedInThisWeek = stats.UsersLoggedInThisWeek,
                AverageProfileCompletion = stats.AverageProfileCompletion
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques utilisateurs");
            throw;
        }
    }
}
