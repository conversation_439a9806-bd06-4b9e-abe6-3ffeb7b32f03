using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Infrastructure.Configuration;

/// <summary>
/// Configuration Entity Framework pour l'entité User
/// </summary>
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    /// <summary>
    /// Configure l'entité User
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    public void Configure(EntityTypeBuilder<User> builder)
    {
        // Configuration de la table
        builder.ToTable("Users");

        // Clé primaire
        builder.HasKey(u => u.Id);
        builder.Property(u => u.Id)
               .ValueGeneratedNever(); // Les GUID sont générés par l'application

        // Configuration des propriétés de base
        ConfigureBasicProperties(builder);

        // Configuration des Value Objects
        ConfigureValueObjects(builder);

        // Configuration des propriétés d'authentification
        ConfigureAuthenticationProperties(builder);

        // Configuration des propriétés de sécurité
        ConfigureSecurityProperties(builder);

        // Configuration des propriétés de profil
        ConfigureProfileProperties(builder);

        // Configuration des index
        ConfigureIndexes(builder);

        // Configuration des contraintes
        ConfigureConstraints(builder);
    }

    /// <summary>
    /// Configure les propriétés de base
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    private static void ConfigureBasicProperties(EntityTypeBuilder<User> builder)
    {
        // Prénom
        builder.Property(u => u.FirstName)
               .IsRequired()
               .HasMaxLength(50)
               .HasComment("Prénom de l'utilisateur");

        // Nom de famille
        builder.Property(u => u.LastName)
               .IsRequired()
               .HasMaxLength(50)
               .HasComment("Nom de famille de l'utilisateur");

        // Nom complet (propriété calculée)
        builder.Ignore(u => u.FullName);

        // Rôle
        builder.Property(u => u.Role)
               .IsRequired()
               .HasConversion<int>()
               .HasComment("Rôle de l'utilisateur dans le système");

        // Statut actif
        builder.Property(u => u.IsActive)
               .IsRequired()
               .HasDefaultValue(true)
               .HasComment("Indique si l'utilisateur est actif");
    }

    /// <summary>
    /// Configure les Value Objects
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    private static void ConfigureValueObjects(EntityTypeBuilder<User> builder)
    {
        // Configuration du Value Object Email
        builder.OwnsOne(u => u.Email, email =>
        {
            email.Property(e => e.Value)
                 .IsRequired()
                 .HasMaxLength(254)
                 .HasColumnName("Email")
                 .HasComment("Adresse email de l'utilisateur");

            email.Ignore(e => e.Domain);
            email.Ignore(e => e.LocalPart);
        });

        // Configuration du Value Object PhoneNumber (optionnel)
        builder.OwnsOne(u => u.PhoneNumber, phone =>
        {
            phone.Property(p => p.Value)
                 .HasMaxLength(20)
                 .HasColumnName("PhoneNumber")
                 .HasComment("Numéro de téléphone de l'utilisateur");

            phone.Property(p => p.CountryCode)
                 .HasMaxLength(5)
                 .HasColumnName("PhoneCountryCode")
                 .HasComment("Code pays du numéro de téléphone");

            phone.Property(p => p.NationalNumber)
                 .HasMaxLength(15)
                 .HasColumnName("PhoneNationalNumber")
                 .HasComment("Numéro national sans le code pays");

            phone.Ignore(p => p.Value);
        });
    }

    /// <summary>
    /// Configure les propriétés d'authentification
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    private static void ConfigureAuthenticationProperties(EntityTypeBuilder<User> builder)
    {
        // Hash du mot de passe
        builder.Property(u => u.PasswordHash)
               .IsRequired()
               .HasMaxLength(500)
               .HasComment("Hash du mot de passe");

        // Salt du mot de passe
        builder.Property(u => u.PasswordSalt)
               .IsRequired()
               .HasMaxLength(500)
               .HasComment("Salt pour le hachage du mot de passe");

        // Date de changement du mot de passe
        builder.Property(u => u.PasswordChangedAt)
               .HasComment("Date de dernière modification du mot de passe");

        // Confirmation d'email
        builder.Property(u => u.IsEmailConfirmed)
               .IsRequired()
               .HasDefaultValue(false)
               .HasComment("Indique si l'email est confirmé");

        // Confirmation de téléphone
        builder.Property(u => u.IsPhoneConfirmed)
               .IsRequired()
               .HasDefaultValue(false)
               .HasComment("Indique si le numéro de téléphone est confirmé");

        // Dernière connexion
        builder.Property(u => u.LastLoginAt)
               .HasComment("Date de dernière connexion");
    }

    /// <summary>
    /// Configure les propriétés de sécurité
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    private static void ConfigureSecurityProperties(EntityTypeBuilder<User> builder)
    {
        // Tentatives de connexion échouées
        builder.Property(u => u.FailedLoginAttempts)
               .IsRequired()
               .HasDefaultValue(0)
               .HasComment("Nombre de tentatives de connexion échouées");

        // Verrouillage du compte
        builder.Property(u => u.LockedUntil)
               .HasComment("Date de fin de verrouillage du compte");

        // Token de confirmation d'email
        builder.Property(u => u.EmailConfirmationToken)
               .HasMaxLength(500)
               .HasComment("Token de confirmation d'email");

        // Expiration du token de confirmation d'email
        builder.Property(u => u.EmailConfirmationTokenExpiry)
               .HasComment("Date d'expiration du token de confirmation d'email");

        // Token de réinitialisation de mot de passe
        builder.Property(u => u.PasswordResetToken)
               .HasMaxLength(500)
               .HasComment("Token de réinitialisation de mot de passe");

        // Expiration du token de réinitialisation
        builder.Property(u => u.PasswordResetTokenExpiry)
               .HasComment("Date d'expiration du token de réinitialisation");

        // Authentification à deux facteurs
        builder.Property(u => u.TwoFactorSecret)
               .HasMaxLength(500)
               .HasComment("Secret pour l'authentification à deux facteurs");

        builder.Property(u => u.IsTwoFactorEnabled)
               .IsRequired()
               .HasDefaultValue(false)
               .HasComment("Indique si l'authentification à deux facteurs est activée");

        builder.Property(u => u.TwoFactorRecoveryCodes)
               .HasMaxLength(2000)
               .HasComment("Codes de récupération pour l'authentification à deux facteurs");
    }

    /// <summary>
    /// Configure les propriétés de profil
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    private static void ConfigureProfileProperties(EntityTypeBuilder<User> builder)
    {
        // Avatar
        builder.Property(u => u.AvatarUrl)
               .HasMaxLength(500)
               .HasComment("URL de l'avatar de l'utilisateur");

        // Pourcentage de complétion du profil
        builder.Property(u => u.ProfileCompletionPercentage)
               .IsRequired()
               .HasDefaultValue(0)
               .HasComment("Pourcentage de complétion du profil");

        // Préférences utilisateur
        builder.Property(u => u.Preferences)
               .HasColumnType("jsonb")
               .HasComment("Préférences de l'utilisateur au format JSON");

        // Langue
        builder.Property(u => u.Language)
               .IsRequired()
               .HasMaxLength(10)
               .HasDefaultValue("fr-FR")
               .HasComment("Langue préférée de l'utilisateur");

        // Fuseau horaire
        builder.Property(u => u.TimeZone)
               .IsRequired()
               .HasMaxLength(50)
               .HasDefaultValue("Europe/Paris")
               .HasComment("Fuseau horaire de l'utilisateur");
    }

    /// <summary>
    /// Configure les index pour les performances
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    private static void ConfigureIndexes(EntityTypeBuilder<User> builder)
    {
        // Index unique sur l'email
        builder.HasIndex(u => u.Email!.Value)
               .IsUnique()
               .HasDatabaseName("IX_Users_Email");

        // Index sur le numéro de téléphone (unique si non null)
        builder.HasIndex(u => u.PhoneNumber!.Value)
               .IsUnique()
               .HasDatabaseName("IX_Users_PhoneNumber")
               .HasFilter("\"PhoneNumber\" IS NOT NULL");

        // Index sur le rôle pour les requêtes par rôle
        builder.HasIndex(u => u.Role)
               .HasDatabaseName("IX_Users_Role");

        // Index sur le statut actif
        builder.HasIndex(u => u.IsActive)
               .HasDatabaseName("IX_Users_IsActive");

        // Index sur la confirmation d'email
        builder.HasIndex(u => u.IsEmailConfirmed)
               .HasDatabaseName("IX_Users_IsEmailConfirmed");

        // Index sur la dernière connexion pour les statistiques
        builder.HasIndex(u => u.LastLoginAt)
               .HasDatabaseName("IX_Users_LastLoginAt");

        // Index sur les tokens pour les recherches rapides
        builder.HasIndex(u => u.EmailConfirmationToken)
               .HasDatabaseName("IX_Users_EmailConfirmationToken")
               .HasFilter("\"EmailConfirmationToken\" IS NOT NULL");

        builder.HasIndex(u => u.PasswordResetToken)
               .HasDatabaseName("IX_Users_PasswordResetToken")
               .HasFilter("\"PasswordResetToken\" IS NOT NULL");

        // Index composé pour les comptes verrouillés
        builder.HasIndex(u => new { u.LockedUntil, u.FailedLoginAttempts })
               .HasDatabaseName("IX_Users_LockedAccount")
               .HasFilter("\"LockedUntil\" IS NOT NULL");
    }

    /// <summary>
    /// Configure les contraintes de validation
    /// </summary>
    /// <param name="builder">Builder de configuration</param>
    private static void ConfigureConstraints(EntityTypeBuilder<User> builder)
    {
        // Contrainte sur le pourcentage de complétion (0-100)
        builder.HasCheckConstraint("CK_Users_ProfileCompletionPercentage", 
            "\"ProfileCompletionPercentage\" >= 0 AND \"ProfileCompletionPercentage\" <= 100");

        // Contrainte sur les tentatives de connexion échouées (>= 0)
        builder.HasCheckConstraint("CK_Users_FailedLoginAttempts", 
            "\"FailedLoginAttempts\" >= 0");

        // Contrainte sur la cohérence des dates de tokens
        builder.HasCheckConstraint("CK_Users_EmailTokenExpiry", 
            "\"EmailConfirmationToken\" IS NULL OR \"EmailConfirmationTokenExpiry\" IS NOT NULL");

        builder.HasCheckConstraint("CK_Users_PasswordTokenExpiry", 
            "\"PasswordResetToken\" IS NULL OR \"PasswordResetTokenExpiry\" IS NOT NULL");

        // Contrainte sur la cohérence 2FA
        builder.HasCheckConstraint("CK_Users_TwoFactorConsistency", 
            "\"IsTwoFactorEnabled\" = false OR \"TwoFactorSecret\" IS NOT NULL");
    }
}
