using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Repositories;

namespace ServiceLink.Infrastructure.Data;

/// <summary>
/// Implémentation de l'Unit of Work pattern
/// Gère les transactions et coordonne les repositories
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly ServiceLinkDbContext _context;
    private IDbContextTransaction? _currentTransaction;
    private bool _disposed;

    // Repositories
    private IUserRepository? _users;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="context">Contexte de base de données</param>
    public UnitOfWork(ServiceLinkDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Repository des utilisateurs
    /// </summary>
    public IUserRepository Users => _users ??= new UserRepository(_context);

    /// <summary>
    /// Sauvegarde tous les changements dans une transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre d'entités affectées</returns>
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateConcurrencyException ex)
        {
            // Gestion des conflits de concurrence
            throw new InvalidOperationException("Un conflit de concurrence s'est produit. L'entité a été modifiée par un autre utilisateur.", ex);
        }
        catch (DbUpdateException ex)
        {
            // Gestion des erreurs de mise à jour de la base de données
            throw new InvalidOperationException("Une erreur s'est produite lors de la sauvegarde des données.", ex);
        }
    }

    /// <summary>
    /// Démarre une nouvelle transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Transaction</returns>
    public async Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_currentTransaction != null)
        {
            throw new InvalidOperationException("Une transaction est déjà en cours.");
        }

        _currentTransaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        return new DbTransactionWrapper(_currentTransaction);
    }

    /// <summary>
    /// Valide la transaction en cours
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_currentTransaction == null)
        {
            throw new InvalidOperationException("Aucune transaction en cours à valider.");
        }

        try
        {
            await _currentTransaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
        finally
        {
            await _currentTransaction.DisposeAsync();
            _currentTransaction = null;
        }
    }

    /// <summary>
    /// Annule la transaction en cours
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_currentTransaction == null)
        {
            throw new InvalidOperationException("Aucune transaction en cours à annuler.");
        }

        try
        {
            await _currentTransaction.RollbackAsync(cancellationToken);
        }
        finally
        {
            await _currentTransaction.DisposeAsync();
            _currentTransaction = null;
        }
    }

    /// <summary>
    /// Exécute une opération dans une transaction
    /// </summary>
    /// <param name="operation">Opération à exécuter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'opération</returns>
    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        var wasTransactionStarted = _currentTransaction == null;
        
        if (wasTransactionStarted)
        {
            await BeginTransactionAsync(cancellationToken);
        }

        try
        {
            var result = await operation();
            
            if (wasTransactionStarted)
            {
                await CommitTransactionAsync(cancellationToken);
            }
            
            return result;
        }
        catch
        {
            if (wasTransactionStarted)
            {
                await RollbackTransactionAsync(cancellationToken);
            }
            throw;
        }
    }

    /// <summary>
    /// Exécute une opération dans une transaction (sans retour)
    /// </summary>
    /// <param name="operation">Opération à exécuter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public async Task ExecuteInTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default)
    {
        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        await ExecuteInTransactionAsync(async () =>
        {
            await operation();
            return true; // Valeur de retour factice
        }, cancellationToken);
    }

    /// <summary>
    /// Détache une entité du contexte
    /// </summary>
    /// <param name="entity">Entité à détacher</param>
    public void Detach(object entity)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        var entry = _context.Entry(entity);
        if (entry != null)
        {
            entry.State = EntityState.Detached;
        }
    }

    /// <summary>
    /// Recharge une entité depuis la base de données
    /// </summary>
    /// <param name="entity">Entité à recharger</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public async Task ReloadAsync(object entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        var entry = _context.Entry(entity);
        if (entry != null)
        {
            await entry.ReloadAsync(cancellationToken);
        }
    }

    /// <summary>
    /// Vérifie s'il y a des changements en attente
    /// </summary>
    /// <returns>True s'il y a des changements</returns>
    public bool HasChanges()
    {
        return _context.ChangeTracker.HasChanges();
    }

    /// <summary>
    /// Annule tous les changements en attente
    /// </summary>
    public void DiscardChanges()
    {
        foreach (var entry in _context.ChangeTracker.Entries())
        {
            switch (entry.State)
            {
                case EntityState.Modified:
                case EntityState.Deleted:
                    entry.State = EntityState.Unchanged;
                    break;
                case EntityState.Added:
                    entry.State = EntityState.Detached;
                    break;
            }
        }
    }

    /// <summary>
    /// Libère les ressources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Libère les ressources
    /// </summary>
    /// <param name="disposing">Indique si on libère les ressources managées</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _currentTransaction?.Dispose();
            _context.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Wrapper pour les transactions Entity Framework
/// </summary>
internal class DbTransactionWrapper : IDbTransaction
{
    private readonly IDbContextTransaction _transaction;
    private bool _disposed;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="transaction">Transaction Entity Framework</param>
    public DbTransactionWrapper(IDbContextTransaction transaction)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
    }

    /// <summary>
    /// Valide la transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.CommitAsync(cancellationToken);
    }

    /// <summary>
    /// Annule la transaction
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.RollbackAsync(cancellationToken);
    }

    /// <summary>
    /// Libère les ressources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Libère les ressources
    /// </summary>
    /// <param name="disposing">Indique si on libère les ressources managées</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _transaction.Dispose();
            _disposed = true;
        }
    }
}
