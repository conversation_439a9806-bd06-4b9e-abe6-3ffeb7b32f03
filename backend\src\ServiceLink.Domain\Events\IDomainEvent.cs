using MediatR;

namespace ServiceLink.Domain.Events;

/// <summary>
/// Interface pour les événements de domaine dans ServiceLink
/// Hérite de INotification pour l'intégration avec MediatR
/// </summary>
public interface IDomainEvent : INotification
{
    /// <summary>
    /// Identifiant unique de l'événement
    /// </summary>
    Guid EventId { get; }

    /// <summary>
    /// Date et heure de création de l'événement
    /// </summary>
    DateTime OccurredOn { get; }

    /// <summary>
    /// Type d'événement pour la sérialisation et le routage
    /// </summary>
    string EventType { get; }

    /// <summary>
    /// Version de l'événement pour la compatibilité
    /// </summary>
    int Version { get; }

    /// <summary>
    /// Identifiant de l'utilisateur qui a déclenché l'événement (optionnel)
    /// </summary>
    Guid? UserId { get; }

    /// <summary>
    /// Métadonnées additionnelles de l'événement
    /// </summary>
    Dictionary<string, object> Metadata { get; }
}

/// <summary>
/// Classe de base abstraite pour les événements de domaine
/// Implémente les propriétés communes de IDomainEvent
/// </summary>
public abstract class BaseDomainEvent : IDomainEvent
{
    /// <summary>
    /// Constructeur protégé pour initialiser les propriétés communes
    /// </summary>
    /// <param name="userId">Identifiant de l'utilisateur qui déclenche l'événement</param>
    protected BaseDomainEvent(Guid? userId = null)
    {
        EventId = Guid.NewGuid();
        OccurredOn = DateTime.UtcNow;
        EventType = GetType().Name;
        Version = 1;
        UserId = userId;
        Metadata = new Dictionary<string, object>();
    }

    /// <inheritdoc />
    public Guid EventId { get; private set; }

    /// <inheritdoc />
    public DateTime OccurredOn { get; private set; }

    /// <inheritdoc />
    public string EventType { get; private set; }

    /// <inheritdoc />
    public int Version { get; protected set; }

    /// <inheritdoc />
    public Guid? UserId { get; private set; }

    /// <inheritdoc />
    public Dictionary<string, object> Metadata { get; private set; }

    /// <summary>
    /// Ajoute une métadonnée à l'événement
    /// </summary>
    /// <param name="key">Clé de la métadonnée</param>
    /// <param name="value">Valeur de la métadonnée</param>
    protected void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    /// <summary>
    /// Ajoute plusieurs métadonnées à l'événement
    /// </summary>
    /// <param name="metadata">Dictionnaire des métadonnées à ajouter</param>
    protected void AddMetadata(Dictionary<string, object> metadata)
    {
        foreach (var kvp in metadata)
        {
            Metadata[kvp.Key] = kvp.Value;
        }
    }
}
