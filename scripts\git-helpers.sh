#!/bin/bash
# ServiceLink Git Helper Scripts
# Bash scripts pour faciliter le workflow Git

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction pour créer une nouvelle feature branch
new_feature_branch() {
    local feature_name="$1"
    local scope="$2"
    
    if [ -z "$feature_name" ]; then
        echo -e "${RED}❌ Nom de la feature requis${NC}"
        echo "Usage: new_feature_branch <feature_name> [scope]"
        return 1
    fi
    
    local branch_name
    if [ -n "$scope" ]; then
        branch_name="feature/$scope/$feature_name"
    else
        branch_name="feature/$feature_name"
    fi
    
    echo -e "${GREEN}🌿 Création de la branche: $branch_name${NC}"
    
    # S'assurer qu'on est sur develop et à jour
    git checkout develop
    git pull origin develop
    
    # Créer la nouvelle branche
    git checkout -b "$branch_name"
    
    echo -e "${GREEN}✅ Branche $branch_name créée avec succès!${NC}"
    echo -e "${YELLOW}💡 N'oubliez pas de faire des commits réguliers avec les conventions:${NC}"
    echo -e "${CYAN}   feat($scope): description de la fonctionnalité${NC}"
}

# Fonction pour créer une hotfix branch
new_hotfix_branch() {
    local hotfix_name="$1"
    local scope="$2"
    
    if [ -z "$hotfix_name" ]; then
        echo -e "${RED}❌ Nom du hotfix requis${NC}"
        echo "Usage: new_hotfix_branch <hotfix_name> [scope]"
        return 1
    fi
    
    local branch_name
    if [ -n "$scope" ]; then
        branch_name="hotfix/$scope/$hotfix_name"
    else
        branch_name="hotfix/$hotfix_name"
    fi
    
    echo -e "${RED}🔥 Création de la branche hotfix: $branch_name${NC}"
    
    # S'assurer qu'on est sur main et à jour
    git checkout main
    git pull origin main
    
    # Créer la nouvelle branche
    git checkout -b "$branch_name"
    
    echo -e "${GREEN}✅ Branche hotfix $branch_name créée avec succès!${NC}"
    echo -e "${YELLOW}⚠️  ATTENTION: Cette branche sera mergée vers main ET develop${NC}"
}

# Fonction pour créer une release branch
new_release_branch() {
    local version="$1"
    
    if [ -z "$version" ]; then
        echo -e "${RED}❌ Version requise${NC}"
        echo "Usage: new_release_branch <version>"
        return 1
    fi
    
    local branch_name="release/v$version"
    
    echo -e "${BLUE}🚀 Création de la branche release: $branch_name${NC}"
    
    # S'assurer qu'on est sur develop et à jour
    git checkout develop
    git pull origin develop
    
    # Créer la nouvelle branche
    git checkout -b "$branch_name"
    
    echo -e "${GREEN}✅ Branche release $branch_name créée avec succès!${NC}"
    echo -e "${YELLOW}📝 Prochaines étapes:${NC}"
    echo -e "${CYAN}   1. Mettre à jour les numéros de version${NC}"
    echo -e "${CYAN}   2. Finaliser la documentation${NC}"
    echo -e "${CYAN}   3. Effectuer les tests finaux${NC}"
    echo -e "${CYAN}   4. Créer PR vers main${NC}"
}

# Fonction pour faire un commit avec les conventions
conventional_commit() {
    local type="$1"
    local scope="$2"
    local description="$3"
    local body="$4"
    local breaking_change="$5"
    
    if [ -z "$type" ] || [ -z "$description" ]; then
        echo -e "${RED}❌ Type et description requis${NC}"
        echo "Usage: conventional_commit <type> [scope] <description> [body] [breaking_change]"
        echo "Types: feat, fix, docs, style, refactor, perf, test, build, ci, chore, revert"
        return 1
    fi
    
    local commit_message
    if [ -n "$scope" ]; then
        commit_message="$type($scope): $description"
    else
        commit_message="$type: $description"
    fi
    
    if [ -n "$body" ]; then
        commit_message="$commit_message\n\n$body"
    fi
    
    if [ "$breaking_change" = "true" ]; then
        commit_message="$commit_message\n\nBREAKING CHANGE: $description"
    fi
    
    echo -e "${GREEN}📝 Commit message:${NC}"
    echo -e "${CYAN}$commit_message${NC}"
    
    read -p "Confirmer ce commit? (y/N): " confirm
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        git add .
        echo -e "$commit_message" | git commit -F -
        echo -e "${GREEN}✅ Commit effectué avec succès!${NC}"
    else
        echo -e "${RED}❌ Commit annulé${NC}"
    fi
}

# Fonction pour synchroniser avec develop
sync_with_develop() {
    local current_branch=$(git branch --show-current)
    
    echo -e "${BLUE}🔄 Synchronisation de $current_branch avec develop...${NC}"
    
    # Sauvegarder les changements en cours
    git stash push -m "Auto-stash before sync with develop"
    
    # Aller sur develop et récupérer les derniers changements
    git checkout develop
    git pull origin develop
    
    # Retourner sur la branche et rebaser
    git checkout "$current_branch"
    git rebase develop
    
    # Restaurer les changements
    if git stash list | grep -q "Auto-stash before sync with develop"; then
        git stash pop
    fi
    
    echo -e "${GREEN}✅ Synchronisation terminée!${NC}"
}

# Fonction pour nettoyer les branches locales
remove_merged_branches() {
    echo -e "${YELLOW}🧹 Nettoyage des branches mergées...${NC}"
    
    # Lister les branches mergées (sauf main et develop)
    local merged_branches=$(git branch --merged | grep -v -E "(main|develop|\*)" | xargs)
    
    if [ -z "$merged_branches" ]; then
        echo -e "${GREEN}✅ Aucune branche à nettoyer${NC}"
        return
    fi
    
    echo -e "${RED}Branches à supprimer:${NC}"
    echo "$merged_branches" | tr ' ' '\n' | sed 's/^/  - /'
    
    read -p "Supprimer ces branches? (y/N): " confirm
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        echo "$merged_branches" | xargs git branch -d
        echo -e "${GREEN}✅ Branches supprimées!${NC}"
    else
        echo -e "${RED}❌ Nettoyage annulé${NC}"
    fi
}

# Fonction pour afficher le statut du projet
show_project_status() {
    echo -e "${BLUE}📊 ServiceLink - Statut du Projet${NC}"
    echo -e "${BLUE}=================================${NC}"
    
    # Branche actuelle
    local current_branch=$(git branch --show-current)
    echo -e "${GREEN}🌿 Branche actuelle: $current_branch${NC}"
    
    # Statut Git
    echo -e "\n${YELLOW}📋 Statut Git:${NC}"
    git status --short
    
    # Derniers commits
    echo -e "\n${YELLOW}📝 Derniers commits:${NC}"
    git log --oneline -5
    
    # Branches locales
    echo -e "\n${YELLOW}🌿 Branches locales:${NC}"
    git branch
    
    # Vérifier si les services sont en cours d'exécution
    echo -e "\n${YELLOW}🚀 Services:${NC}"
    
    if pgrep -f "vite" > /dev/null; then
        echo -e "  ${GREEN}✅ Frontend (React) - Running on http://localhost:5173${NC}"
    else
        echo -e "  ${RED}❌ Frontend (React) - Not running${NC}"
    fi
    
    if pgrep -f "ServiceLink.API" > /dev/null; then
        echo -e "  ${GREEN}✅ Backend (.NET) - Running${NC}"
    else
        echo -e "  ${RED}❌ Backend (.NET) - Not running${NC}"
    fi
    
    # Docker services
    if command -v docker-compose > /dev/null && docker-compose ps > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Docker services available${NC}"
    else
        echo -e "  ${RED}❌ Docker not available${NC}"
    fi
}

# Fonction d'aide
show_git_help() {
    echo -e "${BLUE}🛠️  ServiceLink Git Helper Commands${NC}"
    echo -e "${BLUE}====================================${NC}"
    echo ""
    echo -e "${YELLOW}📝 Création de branches:${NC}"
    echo -e "${CYAN}  new_feature_branch 'auth-system' 'auth'${NC}"
    echo -e "${CYAN}  new_hotfix_branch 'security-fix' 'auth'${NC}"
    echo -e "${CYAN}  new_release_branch '1.0.0'${NC}"
    echo ""
    echo -e "${YELLOW}📝 Commits conventionnels:${NC}"
    echo -e "${CYAN}  conventional_commit 'feat' 'auth' 'add JWT authentication'${NC}"
    echo -e "${CYAN}  conventional_commit 'fix' '' 'resolve login bug' '' 'true'${NC}"
    echo ""
    echo -e "${YELLOW}🔄 Synchronisation:${NC}"
    echo -e "${CYAN}  sync_with_develop${NC}"
    echo ""
    echo -e "${YELLOW}🧹 Maintenance:${NC}"
    echo -e "${CYAN}  remove_merged_branches${NC}"
    echo -e "${CYAN}  show_project_status${NC}"
    echo ""
    echo -e "${YELLOW}💡 Exemples de workflow:${NC}"
    echo -e "${CYAN}  1. new_feature_branch 'user-dashboard' 'frontend'${NC}"
    echo -e "${CYAN}  2. # Développer la fonctionnalité${NC}"
    echo -e "${CYAN}  3. conventional_commit 'feat' 'frontend' 'add user dashboard'${NC}"
    echo -e "${CYAN}  4. git push origin feature/frontend/user-dashboard${NC}"
    echo -e "${CYAN}  5. # Créer PR via GitHub${NC}"
}

# Rendre le script exécutable et afficher l'aide
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    echo -e "${GREEN}🛠️  ServiceLink Git Helpers chargés!${NC}"
    echo -e "${YELLOW}Tapez 'show_git_help' pour voir les commandes disponibles${NC}"
    show_git_help
fi
