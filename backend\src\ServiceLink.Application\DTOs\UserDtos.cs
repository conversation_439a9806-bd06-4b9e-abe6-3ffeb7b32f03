using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.DTOs;

/// <summary>
/// DTO pour la création d'un utilisateur
/// </summary>
public class CreateUserRequest
{
    /// <summary>
    /// Adresse email de l'utilisateur
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Prénom de l'utilisateur
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom de famille de l'utilisateur
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Mot de passe de l'utilisateur
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation du mot de passe
    /// </summary>
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// Numéro de téléphone (optionnel)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole Role { get; set; } = UserRole.Client;

    /// <summary>
    /// Langue préférée
    /// </summary>
    public string Language { get; set; } = "fr-FR";

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";
}

/// <summary>
/// DTO pour la mise à jour d'un utilisateur
/// </summary>
public class UpdateUserRequest
{
    /// <summary>
    /// Prénom de l'utilisateur
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom de famille de l'utilisateur
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Numéro de téléphone (optionnel)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Langue préférée
    /// </summary>
    public string Language { get; set; } = "fr-FR";

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// URL de l'avatar
    /// </summary>
    public string? AvatarUrl { get; set; }
}

/// <summary>
/// DTO pour le changement de mot de passe
/// </summary>
public class ChangePasswordRequest
{
    /// <summary>
    /// Mot de passe actuel
    /// </summary>
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// Nouveau mot de passe
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation du nouveau mot de passe
    /// </summary>
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour la réinitialisation de mot de passe
/// </summary>
public class ResetPasswordRequest
{
    /// <summary>
    /// Token de réinitialisation
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Nouveau mot de passe
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// Confirmation du nouveau mot de passe
    /// </summary>
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour l'authentification
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Adresse email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Mot de passe
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Code 2FA (optionnel)
    /// </summary>
    public string? TwoFactorCode { get; set; }

    /// <summary>
    /// Se souvenir de moi
    /// </summary>
    public bool RememberMe { get; set; }
}

/// <summary>
/// DTO de réponse pour l'utilisateur
/// </summary>
public class UserResponse
{
    /// <summary>
    /// Identifiant unique de l'utilisateur
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Adresse email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Prénom
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom de famille
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Nom complet
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Numéro de téléphone
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// Description du rôle
    /// </summary>
    public string RoleDescription { get; set; } = string.Empty;

    /// <summary>
    /// Indique si l'email est confirmé
    /// </summary>
    public bool IsEmailConfirmed { get; set; }

    /// <summary>
    /// Indique si le téléphone est confirmé
    /// </summary>
    public bool IsPhoneConfirmed { get; set; }

    /// <summary>
    /// Indique si l'utilisateur est actif
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Indique si l'authentification à deux facteurs est activée
    /// </summary>
    public bool IsTwoFactorEnabled { get; set; }

    /// <summary>
    /// URL de l'avatar
    /// </summary>
    public string? AvatarUrl { get; set; }

    /// <summary>
    /// Pourcentage de complétion du profil
    /// </summary>
    public int ProfileCompletionPercentage { get; set; }

    /// <summary>
    /// Langue préférée
    /// </summary>
    public string Language { get; set; } = string.Empty;

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = string.Empty;

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date de dernière mise à jour
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Date de dernière connexion
    /// </summary>
    public DateTime? LastLoginAt { get; set; }
}

/// <summary>
/// DTO de réponse pour l'authentification
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// Token JWT
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Token de rafraîchissement
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// Date d'expiration du token
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Informations de l'utilisateur
    /// </summary>
    public UserResponse User { get; set; } = null!;
}

/// <summary>
/// DTO pour la liste paginée d'utilisateurs
/// </summary>
public class UserListResponse
{
    /// <summary>
    /// Liste des utilisateurs
    /// </summary>
    public List<UserResponse> Users { get; set; } = new();

    /// <summary>
    /// Nombre total d'utilisateurs
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Numéro de page actuel
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Taille de la page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Nombre total de pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Indique s'il y a une page précédente
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Indique s'il y a une page suivante
    /// </summary>
    public bool HasNextPage { get; set; }
}

/// <summary>
/// DTO pour les statistiques des utilisateurs
/// </summary>
public class UserStatisticsResponse
{
    /// <summary>
    /// Nombre total d'utilisateurs
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs actifs
    /// </summary>
    public int ActiveUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs inactifs
    /// </summary>
    public int InactiveUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs avec email confirmé
    /// </summary>
    public int EmailConfirmedUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs avec email non confirmé
    /// </summary>
    public int EmailUnconfirmedUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs verrouillés
    /// </summary>
    public int LockedUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs avec 2FA activé
    /// </summary>
    public int TwoFactorEnabledUsers { get; set; }

    /// <summary>
    /// Répartition par rôle
    /// </summary>
    public Dictionary<string, int> UsersByRole { get; set; } = new();

    /// <summary>
    /// Nouveaux utilisateurs ce mois
    /// </summary>
    public int NewUsersThisMonth { get; set; }

    /// <summary>
    /// Utilisateurs connectés aujourd'hui
    /// </summary>
    public int UsersLoggedInToday { get; set; }

    /// <summary>
    /// Utilisateurs connectés cette semaine
    /// </summary>
    public int UsersLoggedInThisWeek { get; set; }

    /// <summary>
    /// Pourcentage de complétion moyenne des profils
    /// </summary>
    public double AverageProfileCompletion { get; set; }
}
