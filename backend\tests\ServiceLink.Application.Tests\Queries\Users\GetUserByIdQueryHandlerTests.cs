using Bogus;
using FluentAssertions;
using Moq;
using ServiceLink.Application.Queries;
using ServiceLink.Application.Handlers;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Application.Tests.Queries.Users;

/// <summary>
/// Tests unitaires pour GetUserByIdQueryHandler
/// </summary>
public class GetUserByIdQueryHandlerTests
{
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly GetUserByIdQueryHandler _handler;
    private readonly Faker _faker;

    public GetUserByIdQueryHandlerTests()
    {
        _userRepositoryMock = new Mock<IUserRepository>();
        _handler = new GetUserByIdQueryHandler(_userRepositoryMock.Object);
        _faker = new Faker("fr");
    }

    [Fact]
    public async Task Handle_WithExistingUserId_ShouldReturnUserResponse()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var email = Email.Create("<EMAIL>");
        var phoneNumber = PhoneNumber.Create("+33123456789");
        
        var user = new User(email, "John", "Doe", UserRole.Client, phoneNumber)
        {
            Id = userId
        };

        var query = new GetUserByIdQuery { Id = userId };

        _userRepositoryMock
            .Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(userId);
        result.Email.Should().Be(email.Value);
        result.FirstName.Should().Be("John");
        result.LastName.Should().Be("Doe");
        result.FullName.Should().Be("John Doe");
        result.Role.Should().Be(UserRole.Client);
        result.PhoneNumber.Should().Be(phoneNumber.Value);
        result.IsActive.Should().BeTrue();
        result.IsEmailConfirmed.Should().BeFalse();
        result.IsPhoneConfirmed.Should().BeFalse();
        result.IsTwoFactorEnabled.Should().BeFalse();

        // Verify repository interaction
        _userRepositoryMock.Verify(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistingUserId_ShouldReturnNull()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetUserByIdQuery { Id = userId };

        _userRepositoryMock
            .Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeNull();

        // Verify repository interaction
        _userRepositoryMock.Verify(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithEmptyGuid_ShouldReturnNull()
    {
        // Arrange
        var query = new GetUserByIdQuery { Id = Guid.Empty };

        _userRepositoryMock
            .Setup(x => x.GetByIdAsync(Guid.Empty, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeNull();

        // Verify repository interaction
        _userRepositoryMock.Verify(x => x.GetByIdAsync(Guid.Empty, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithUserHavingAllProperties_ShouldMapAllProperties()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var email = Email.Create("<EMAIL>");
        var phoneNumber = PhoneNumber.Create("+33987654321");
        var createdBy = Guid.NewGuid();
        var updatedBy = Guid.NewGuid();
        
        var user = new User(email, "Jane", "Smith", UserRole.Admin, phoneNumber, createdBy)
        {
            Id = userId
        };

        // Set additional properties
        user.SetPassword("hashedPassword", "salt", updatedBy);
        user.ConfirmEmail(updatedBy);
        user.ConfirmPhone(updatedBy);
        user.EnableTwoFactor("secret", "codes", updatedBy);
        user.RecordSuccessfulLogin();

        var query = new GetUserByIdQuery { Id = userId };

        _userRepositoryMock
            .Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(userId);
        result.Email.Should().Be(email.Value);
        result.FirstName.Should().Be("Jane");
        result.LastName.Should().Be("Smith");
        result.FullName.Should().Be("Jane Smith");
        result.Role.Should().Be(UserRole.Admin);
        result.PhoneNumber.Should().Be(phoneNumber.Value);
        result.IsActive.Should().BeTrue();
        result.IsEmailConfirmed.Should().BeTrue();
        result.IsPhoneConfirmed.Should().BeTrue();
        result.IsTwoFactorEnabled.Should().BeTrue();
        result.CreatedBy.Should().Be(createdBy);
        result.UpdatedBy.Should().Be(updatedBy);
        result.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        result.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        result.LastLoginAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        result.ProfileCompletionPercentage.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_WithDeactivatedUser_ShouldReturnUserWithCorrectStatus()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var email = Email.Create("<EMAIL>");
        var deactivatedBy = Guid.NewGuid();
        
        var user = new User(email, "Deactivated", "User", UserRole.Client)
        {
            Id = userId
        };

        user.Deactivate(deactivatedBy);

        var query = new GetUserByIdQuery { Id = userId };

        _userRepositoryMock
            .Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(userId);
        result.IsActive.Should().BeFalse();
        result.UpdatedBy.Should().Be(deactivatedBy);
    }

    [Fact]
    public async Task Handle_WithLockedUser_ShouldReturnUserWithLockInfo()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var email = Email.Create("<EMAIL>");
        
        var user = new User(email, "Locked", "User", UserRole.Client)
        {
            Id = userId
        };

        // Lock the user by recording 5 failed login attempts
        for (int i = 0; i < 5; i++)
        {
            user.RecordFailedLogin();
        }

        var query = new GetUserByIdQuery { Id = userId };

        _userRepositoryMock
            .Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(userId);
        result.FailedLoginAttempts.Should().Be(5);
        result.LockedUntil.Should().BeCloseTo(DateTime.UtcNow.AddMinutes(30), TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task Handle_ShouldHandleCancellationToken()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetUserByIdQuery { Id = userId };
        var cancellationToken = new CancellationToken(true);

        _userRepositoryMock
            .Setup(x => x.GetByIdAsync(userId, cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        var act = async () => await _handler.Handle(query, cancellationToken);
        await act.Should().ThrowAsync<OperationCanceledException>();

        // Verify repository interaction
        _userRepositoryMock.Verify(x => x.GetByIdAsync(userId, cancellationToken), Times.Once);
    }

    private User CreateValidUser(UserRole role = UserRole.Client)
    {
        var email = Email.Create(_faker.Internet.Email());
        var firstName = _faker.Name.FirstName();
        var lastName = _faker.Name.LastName();
        var phoneNumber = PhoneNumber.Create("+33123456789");
        
        return new User(email, firstName, lastName, role, phoneNumber);
    }
}
