using System.Text.RegularExpressions;

namespace ServiceLink.Domain.ValueObjects;

/// <summary>
/// Value Object représentant une adresse email valide
/// Garantit l'immutabilité et la validation de l'email
/// </summary>
public sealed class Email : IEquatable<Email>
{
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    /// <summary>
    /// Valeur de l'adresse email
    /// </summary>
    public string Value { get; }

    /// <summary>
    /// Domaine de l'adresse email
    /// </summary>
    public string Domain => Value.Split('@')[1];

    /// <summary>
    /// Partie locale de l'adresse email (avant @)
    /// </summary>
    public string LocalPart => Value.Split('@')[0];

    /// <summary>
    /// Constructeur privé pour garantir la validation
    /// </summary>
    /// <param name="value">Valeur de l'email</param>
    private Email(string value)
    {
        Value = value.ToLowerInvariant().Trim();
    }

    /// <summary>
    /// Crée une nouvelle instance d'Email après validation
    /// </summary>
    /// <param name="email">Adresse email à valider</param>
    /// <returns>Instance d'Email valide</returns>
    /// <exception cref="ArgumentException">Si l'email n'est pas valide</exception>
    public static Email Create(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("L'adresse email ne peut pas être vide.", nameof(email));

        email = email.Trim().ToLowerInvariant();

        if (email.Length > 254)
            throw new ArgumentException("L'adresse email ne peut pas dépasser 254 caractères.", nameof(email));

        if (!EmailRegex.IsMatch(email))
            throw new ArgumentException($"L'adresse email '{email}' n'est pas valide.", nameof(email));

        // Vérifications supplémentaires
        if (email.Contains(".."))
            throw new ArgumentException("L'adresse email ne peut pas contenir deux points consécutifs.", nameof(email));

        if (email.StartsWith('.') || email.EndsWith('.'))
            throw new ArgumentException("L'adresse email ne peut pas commencer ou finir par un point.", nameof(email));

        var parts = email.Split('@');
        if (parts[0].Length > 64)
            throw new ArgumentException("La partie locale de l'email ne peut pas dépasser 64 caractères.", nameof(email));

        return new Email(email);
    }

    /// <summary>
    /// Tente de créer une instance d'Email
    /// </summary>
    /// <param name="email">Adresse email à valider</param>
    /// <param name="result">Instance d'Email si valide</param>
    /// <returns>True si l'email est valide</returns>
    public static bool TryCreate(string email, out Email? result)
    {
        try
        {
            result = Create(email);
            return true;
        }
        catch
        {
            result = null;
            return false;
        }
    }

    /// <summary>
    /// Vérifie si l'email appartient à un domaine spécifique
    /// </summary>
    /// <param name="domain">Domaine à vérifier</param>
    /// <returns>True si l'email appartient au domaine</returns>
    public bool BelongsToDomain(string domain)
    {
        return Domain.Equals(domain, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Vérifie si l'email est un email jetable/temporaire
    /// </summary>
    /// <returns>True si l'email est probablement jetable</returns>
    public bool IsDisposable()
    {
        var disposableDomains = new[]
        {
            "10minutemail.com", "guerrillamail.com", "mailinator.com",
            "tempmail.org", "yopmail.com", "throwaway.email"
        };

        return disposableDomains.Any(d => Domain.Contains(d, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Vérifie si l'email appartient à un fournisseur d'email populaire
    /// </summary>
    /// <returns>True si c'est un fournisseur populaire</returns>
    public bool IsFromPopularProvider()
    {
        var popularProviders = new[]
        {
            "gmail.com", "yahoo.com", "hotmail.com", "outlook.com",
            "icloud.com", "protonmail.com", "aol.com"
        };

        return popularProviders.Any(p => Domain.Equals(p, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Masque l'email pour l'affichage (ex: j***@example.com)
    /// </summary>
    /// <returns>Email masqué</returns>
    public string ToMaskedString()
    {
        if (LocalPart.Length <= 2)
            return $"{LocalPart[0]}***@{Domain}";

        return $"{LocalPart[0]}{new string('*', LocalPart.Length - 2)}{LocalPart[^1]}@{Domain}";
    }

    /// <summary>
    /// Conversion implicite depuis string
    /// </summary>
    /// <param name="email">Adresse email</param>
    public static implicit operator string(Email email) => email.Value;

    /// <summary>
    /// Conversion explicite vers Email
    /// </summary>
    /// <param name="email">Adresse email</param>
    public static explicit operator Email(string email) => Create(email);

    /// <summary>
    /// Représentation string de l'email
    /// </summary>
    /// <returns>Valeur de l'email</returns>
    public override string ToString() => Value;

    /// <summary>
    /// Égalité basée sur la valeur
    /// </summary>
    /// <param name="other">Autre instance d'Email</param>
    /// <returns>True si les emails sont identiques</returns>
    public bool Equals(Email? other)
    {
        return other is not null && Value == other.Value;
    }

    /// <summary>
    /// Égalité avec object
    /// </summary>
    /// <param name="obj">Objet à comparer</param>
    /// <returns>True si égaux</returns>
    public override bool Equals(object? obj)
    {
        return obj is Email other && Equals(other);
    }

    /// <summary>
    /// Hash code basé sur la valeur
    /// </summary>
    /// <returns>Hash code</returns>
    public override int GetHashCode()
    {
        return Value.GetHashCode();
    }

    /// <summary>
    /// Opérateur d'égalité
    /// </summary>
    public static bool operator ==(Email? left, Email? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Opérateur d'inégalité
    /// </summary>
    public static bool operator !=(Email? left, Email? right)
    {
        return !Equals(left, right);
    }
}
