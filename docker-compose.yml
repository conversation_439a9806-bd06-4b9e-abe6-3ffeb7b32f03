version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: servicelink-postgres
    environment:
      POSTGRES_DB: servicelink_dev
      POSTGRES_USER: servicelink_user
      POSTGRES_PASSWORD: servicelink_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - servicelink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U servicelink_user -d servicelink_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: servicelink-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - servicelink-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass servicelink_redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Test Database
  postgres-test:
    image: postgres:15-alpine
    container_name: servicelink-postgres-test
    environment:
      POSTGRES_DB: servicelink_test
      POSTGRES_USER: servicelink_test_user
      POSTGRES_PASSWORD: servicelink_test_password
    ports:
      - "5437:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    networks:
      - servicelink-network
    restart: unless-stopped
    profiles:
      - testing

  # ServiceLink API (Development)
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: servicelink-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=servicelink_dev;Username=servicelink_user;Password=servicelink_password
      - ConnectionStrings__RedisConnection=redis:6379,password=servicelink_redis_password
    ports:
      - "5000:80"
      - "5001:443"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - servicelink-network
    volumes:
      - ./backend:/app
      - /app/bin
      - /app/obj
    restart: unless-stopped
    profiles:
      - development

  # ServiceLink Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: servicelink-frontend
    environment:
      - VITE_API_URL=http://localhost:5000
      - VITE_APP_ENV=development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - servicelink-network
    restart: unless-stopped
    profiles:
      - development

volumes:
  postgres_data:
    driver: local
  postgres_test_data:
    driver: local
  redis_data:
    driver: local

networks:
  servicelink-network:
    driver: bridge
    name: servicelink-network
