using FluentAssertions;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Domain.Tests.ValueObjects;

/// <summary>
/// Tests unitaires pour le Value Object PhoneNumber
/// </summary>
public class PhoneNumberTests
{
    [Theory]
    [InlineData("+33123456789", "+33", "123456789")]
    [InlineData("+1234567890", "+1", "234567890")]
    [InlineData("+44123456789", "+44", "123456789")]
    public void Create_WithValidInternationalNumber_ShouldReturnPhoneNumberInstance(string input, string expectedCountryCode, string expectedNational)
    {
        // Act
        var phoneNumber = PhoneNumber.Create(input);

        // Assert
        phoneNumber.Should().NotBeNull();
        phoneNumber.Value.Should().Be(input);
        phoneNumber.CountryCode.Should().Be(expectedCountryCode);
        phoneNumber.NationalNumber.Should().Be(expectedNational);
    }

    [Theory]
    [InlineData("0123456789", "+33", "+33123456789")]
    [InlineData("01 23 45 67 89", "+33", "+33123456789")]
    [InlineData("01.23.45.67.89", "+33", "+33123456789")]
    public void Create_WithFrenchLocalNumber_ShouldAddCountryCode(string input, string defaultCountryCode, string expected)
    {
        // Act
        var phoneNumber = PhoneNumber.Create(input, defaultCountryCode);

        // Assert
        phoneNumber.Value.Should().Be(expected);
        phoneNumber.CountryCode.Should().Be(defaultCountryCode);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Create_WithNullOrEmptyNumber_ShouldThrowArgumentException(string invalidNumber)
    {
        // Act & Assert
        var act = () => PhoneNumber.Create(invalidNumber);
        act.Should().Throw<ArgumentException>()
           .WithMessage("Le numéro de téléphone ne peut pas être vide.*");
    }

    [Theory]
    [InlineData("123")]        // Trop court
    [InlineData("12345678901234567")]  // Trop long
    [InlineData("abc123")]     // Lettres
    [InlineData("++123456789")] // Double +
    public void Create_WithInvalidFormat_ShouldThrowArgumentException(string invalidNumber)
    {
        // Act & Assert
        var act = () => PhoneNumber.Create(invalidNumber);
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData("+33 1 23 45 67 89", "+33123456789")]
    [InlineData("(+33) 1-23-45-67-89", "+33123456789")]
    [InlineData("+33.1.23.45.67.89", "+33123456789")]
    [InlineData("  +33 123 456 789  ", "+33123456789")]
    public void Create_ShouldCleanAndNormalizeNumber(string input, string expected)
    {
        // Act
        var phoneNumber = PhoneNumber.Create(input);

        // Assert
        phoneNumber.Value.Should().Be(expected);
    }

    [Fact]
    public void TryCreate_WithValidNumber_ShouldReturnTrueAndPhoneNumber()
    {
        // Arrange
        var validNumber = "+33123456789";

        // Act
        var success = PhoneNumber.TryCreate(validNumber, out var phoneNumber);

        // Assert
        success.Should().BeTrue();
        phoneNumber.Should().NotBeNull();
        phoneNumber!.Value.Should().Be(validNumber);
    }

    [Fact]
    public void TryCreate_WithInvalidNumber_ShouldReturnFalseAndNull()
    {
        // Arrange
        var invalidNumber = "invalid";

        // Act
        var success = PhoneNumber.TryCreate(invalidNumber, out var phoneNumber);

        // Assert
        success.Should().BeFalse();
        phoneNumber.Should().BeNull();
    }

    [Theory]
    [InlineData("+33123456789", "+33 1 23 45 67 89")]
    [InlineData("+12345678901", "+****************")]
    public void ToFormattedString_ShouldFormatAccordingToCountry(string input, string expectedFormat)
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create(input);

        // Act
        var formatted = phoneNumber.ToFormattedString();

        // Assert
        formatted.Should().Be(expectedFormat);
    }

    [Theory]
    [InlineData("+33", "France")]
    [InlineData("+1", "États-Unis/Canada")]
    [InlineData("+44", "Royaume-Uni")]
    [InlineData("+49", "Allemagne")]
    [InlineData("+999", "Inconnu")]
    public void GetCountryName_ShouldReturnCorrectCountryName(string countryCode, string expectedCountry)
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create(countryCode + "123456789");

        // Act
        var countryName = phoneNumber.GetCountryName();

        // Assert
        countryName.Should().Be(expectedCountry);
    }

    [Theory]
    [InlineData("+33612345678", true)]   // Mobile français
    [InlineData("+33712345678", true)]   // Mobile français
    [InlineData("+33123456789", false)]  // Fixe français
    [InlineData("+44712345678", true)]   // Mobile UK
    [InlineData("+44123456789", false)]  // Fixe UK
    public void IsMobile_ShouldDetectMobileNumbers(string input, bool expectedIsMobile)
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create(input);

        // Act
        var isMobile = phoneNumber.IsMobile();

        // Assert
        isMobile.Should().Be(expectedIsMobile);
    }

    [Theory]
    [InlineData("+33123456789", "+33 12***89")]
    [InlineData("+12345678901", "********01")]
    [InlineData("+441234", "+44 12***")]
    public void ToMaskedString_ShouldMaskNumberCorrectly(string input, string expectedMask)
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create(input);

        // Act
        var masked = phoneNumber.ToMaskedString();

        // Assert
        masked.Should().Be(expectedMask);
    }

    [Fact]
    public void Equals_WithSameNumber_ShouldReturnTrue()
    {
        // Arrange
        var phone1 = PhoneNumber.Create("+33123456789");
        var phone2 = PhoneNumber.Create("+33 1 23 45 67 89");

        // Act & Assert
        phone1.Should().Be(phone2);
        phone1.Equals(phone2).Should().BeTrue();
        (phone1 == phone2).Should().BeTrue();
        (phone1 != phone2).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentNumber_ShouldReturnFalse()
    {
        // Arrange
        var phone1 = PhoneNumber.Create("+33123456789");
        var phone2 = PhoneNumber.Create("+33987654321");

        // Act & Assert
        phone1.Should().NotBe(phone2);
        phone1.Equals(phone2).Should().BeFalse();
        (phone1 == phone2).Should().BeFalse();
        (phone1 != phone2).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_WithSameNumber_ShouldReturnSameHashCode()
    {
        // Arrange
        var phone1 = PhoneNumber.Create("+33123456789");
        var phone2 = PhoneNumber.Create("+33 1 23 45 67 89");

        // Act & Assert
        phone1.GetHashCode().Should().Be(phone2.GetHashCode());
    }

    [Fact]
    public void ToString_ShouldReturnPhoneValue()
    {
        // Arrange
        var phoneValue = "+33123456789";
        var phoneNumber = PhoneNumber.Create(phoneValue);

        // Act
        var result = phoneNumber.ToString();

        // Assert
        result.Should().Be(phoneValue);
    }

    [Fact]
    public void ImplicitConversion_ToString_ShouldWork()
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create("+33123456789");

        // Act
        string phoneString = phoneNumber;

        // Assert
        phoneString.Should().Be("+33123456789");
    }

    [Fact]
    public void ExplicitConversion_FromString_ShouldWork()
    {
        // Arrange
        var phoneString = "+33123456789";

        // Act
        var phoneNumber = (PhoneNumber)phoneString;

        // Assert
        phoneNumber.Value.Should().Be(phoneString);
    }

    [Theory]
    [InlineData("+33123456789")]
    [InlineData("+12345678901")]
    [InlineData("+44123456789")]
    [InlineData("+49123456789")]
    [InlineData("+81123456789")]
    public void Create_WithVariousCountryCodes_ShouldSucceed(string validNumber)
    {
        // Act
        var act = () => PhoneNumber.Create(validNumber);

        // Assert
        act.Should().NotThrow();
    }

    [Fact]
    public void Equals_WithNull_ShouldReturnFalse()
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create("+33123456789");

        // Act & Assert
        phoneNumber.Equals(null).Should().BeFalse();
        (phoneNumber == null).Should().BeFalse();
        (phoneNumber != null).Should().BeTrue();
    }

    [Fact]
    public void Equals_WithDifferentType_ShouldReturnFalse()
    {
        // Arrange
        var phoneNumber = PhoneNumber.Create("+33123456789");
        var otherObject = "+33123456789";

        // Act & Assert
        phoneNumber.Equals(otherObject).Should().BeFalse();
    }

    [Theory]
    [InlineData("123456789", "+1")]
    [InlineData("123456789", "+44")]
    [InlineData("123456789", "+49")]
    public void Create_WithDifferentDefaultCountryCodes_ShouldUseCorrectDefault(string localNumber, string defaultCountryCode)
    {
        // Act
        var phoneNumber = PhoneNumber.Create(localNumber, defaultCountryCode);

        // Assert
        phoneNumber.CountryCode.Should().Be(defaultCountryCode);
        phoneNumber.Value.Should().StartWith(defaultCountryCode);
    }
}
