namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Service pour l'authentification à deux facteurs (2FA)
/// </summary>
public interface ITwoFactorService
{
    /// <summary>
    /// Génère un secret 2FA pour un utilisateur
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="issuer">Nom de l'application (défaut: ServiceLink)</param>
    /// <returns>Secret 2FA encodé en base32</returns>
    string GenerateSecret(string userEmail, string issuer = "ServiceLink");

    /// <summary>
    /// Génère une URL QR Code pour configurer l'authentificateur
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="secret">Secret 2FA</param>
    /// <param name="issuer">Nom de l'application</param>
    /// <returns>URL pour le QR Code</returns>
    string GenerateQrCodeUrl(string userEmail, string secret, string issuer = "ServiceLink");

    /// <summary>
    /// Génère une image QR Code en base64
    /// </summary>
    /// <param name="qrCodeUrl">URL du QR Code</param>
    /// <param name="size">Taille de l'image (défaut: 200px)</param>
    /// <returns>Image QR Code en base64</returns>
    Task<string> GenerateQrCodeImageAsync(string qrCodeUrl, int size = 200);

    /// <summary>
    /// Valide un code TOTP
    /// </summary>
    /// <param name="secret">Secret 2FA de l'utilisateur</param>
    /// <param name="code">Code à valider</param>
    /// <param name="window">Fenêtre de tolérance en périodes (défaut: 1)</param>
    /// <returns>True si le code est valide</returns>
    bool ValidateCode(string secret, string code, int window = 1);

    /// <summary>
    /// Génère des codes de récupération
    /// </summary>
    /// <param name="count">Nombre de codes à générer (défaut: 10)</param>
    /// <returns>Liste des codes de récupération</returns>
    string[] GenerateRecoveryCodes(int count = 10);

    /// <summary>
    /// Valide un code de récupération
    /// </summary>
    /// <param name="recoveryCodes">Codes de récupération stockés (séparés par des virgules)</param>
    /// <param name="code">Code à valider</param>
    /// <returns>Tuple (isValid, updatedRecoveryCodes) - les codes mis à jour sans le code utilisé</returns>
    (bool isValid, string updatedRecoveryCodes) ValidateRecoveryCode(string recoveryCodes, string code);

    /// <summary>
    /// Génère un code de sauvegarde temporaire pour la désactivation d'urgence
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="validityMinutes">Durée de validité en minutes (défaut: 30)</param>
    /// <returns>Code de sauvegarde temporaire</returns>
    string GenerateBackupCode(string userEmail, int validityMinutes = 30);

    /// <summary>
    /// Valide un code de sauvegarde temporaire
    /// </summary>
    /// <param name="userEmail">Email de l'utilisateur</param>
    /// <param name="backupCode">Code de sauvegarde</param>
    /// <returns>True si le code est valide et non expiré</returns>
    bool ValidateBackupCode(string userEmail, string backupCode);

    /// <summary>
    /// Vérifie si un secret 2FA est valide
    /// </summary>
    /// <param name="secret">Secret à valider</param>
    /// <returns>True si le secret est valide</returns>
    bool IsValidSecret(string secret);

    /// <summary>
    /// Génère un code TOTP pour un secret donné (utile pour les tests)
    /// </summary>
    /// <param name="secret">Secret 2FA</param>
    /// <param name="timestamp">Timestamp Unix (optionnel, utilise l'heure actuelle par défaut)</param>
    /// <returns>Code TOTP à 6 chiffres</returns>
    string GenerateCode(string secret, long? timestamp = null);

    /// <summary>
    /// Obtient le temps restant avant expiration du code actuel
    /// </summary>
    /// <returns>Secondes restantes avant le prochain code</returns>
    int GetRemainingSeconds();

    /// <summary>
    /// Formate les codes de récupération pour l'affichage
    /// </summary>
    /// <param name="recoveryCodes">Codes de récupération (séparés par des virgules)</param>
    /// <returns>Codes formatés pour l'affichage</returns>
    string[] FormatRecoveryCodesForDisplay(string recoveryCodes);
}

/// <summary>
/// Résultat de la configuration 2FA
/// </summary>
public class TwoFactorSetupResult
{
    /// <summary>
    /// Secret 2FA encodé en base32
    /// </summary>
    public string Secret { get; set; } = string.Empty;

    /// <summary>
    /// URL pour le QR Code
    /// </summary>
    public string QrCodeUrl { get; set; } = string.Empty;

    /// <summary>
    /// Image QR Code en base64
    /// </summary>
    public string QrCodeImage { get; set; } = string.Empty;

    /// <summary>
    /// Codes de récupération
    /// </summary>
    public string[] RecoveryCodes { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Instructions pour l'utilisateur
    /// </summary>
    public string Instructions { get; set; } = string.Empty;
}
