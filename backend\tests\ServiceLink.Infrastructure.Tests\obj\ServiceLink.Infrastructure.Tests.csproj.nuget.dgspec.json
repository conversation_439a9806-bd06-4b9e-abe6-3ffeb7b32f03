{"format": 1, "restore": {"D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.Infrastructure.Tests\\ServiceLink.Infrastructure.Tests.csproj": {}}, "projects": {"D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\ServiceLink.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\ServiceLink.Application.csproj", "projectName": "ServiceLink.Application", "projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\ServiceLink.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj": {"projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[12.0.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[12.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj", "projectName": "ServiceLink.Domain", "projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MediatR": {"target": "Package", "version": "[12.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\ServiceLink.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\ServiceLink.Infrastructure.csproj", "projectName": "ServiceLink.Infrastructure", "projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\ServiceLink.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\ServiceLink.Application.csproj": {"projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\ServiceLink.Application.csproj"}, "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj": {"projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Otp.NET": {"target": "Package", "version": "[1.4.0, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.Infrastructure.Tests\\ServiceLink.Infrastructure.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.Infrastructure.Tests\\ServiceLink.Infrastructure.Tests.csproj", "projectName": "ServiceLink.Infrastructure.Tests", "projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.Infrastructure.Tests\\ServiceLink.Infrastructure.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.Infrastructure.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\ServiceLink.Application.csproj": {"projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\ServiceLink.Application.csproj"}, "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj": {"projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Domain\\ServiceLink.Domain.csproj"}, "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\ServiceLink.Infrastructure.csproj": {"projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\ServiceLink.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Bogus": {"target": "Package", "version": "[35.6.1, )"}, "FluentAssertions": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "Testcontainers.PostgreSql": {"target": "Package", "version": "[3.10.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}