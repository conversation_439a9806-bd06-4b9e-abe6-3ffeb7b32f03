# Docker Compose pour le développement
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: '3.8'

services:
  # Configuration spécifique au développement pour l'API
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
      target: development
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080;https://+:8081
      - ASPNETCORE_Kestrel__Certificates__Default__Password=password
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
    volumes:
      - ./backend:/app
      - ./backend/.aspnet/https:/https:ro
      - /app/bin
      - /app/obj
    ports:
      - "8080:8080"
      - "8081:8081"
    command: dotnet watch run --project src/ServiceLink.API/ServiceLink.API.csproj

  # Base de données de développement avec données de test
  postgres:
    environment:
      POSTGRES_DB: servicelink_dev
      POSTGRES_USER: servicelink_dev
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/dev-data:/docker-entrypoint-initdb.d

  # Redis de développement sans mot de passe
  redis:
    command: redis-server --appendonly yes
    # Pas de mot de passe en développement pour simplifier

  # Frontend avec hot reload
  frontend:
    profiles: [] # Toujours actif en développement
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8080
      - VITE_WS_URL=ws://localhost:8080
      - CHOKIDAR_USEPOLLING=true
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0"

  # Mailhog pour tester les emails en développement
  mailhog:
    image: mailhog/mailhog:latest
    container_name: servicelink-mailhog
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - servicelink-network

  # pgAdmin pour gérer la base de données
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: servicelink-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - servicelink-network
    depends_on:
      - postgres

  # Redis Commander pour gérer Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: servicelink-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - servicelink-network
    depends_on:
      - redis

volumes:
  postgres_dev_data:
    driver: local
  pgadmin_data:
    driver: local
