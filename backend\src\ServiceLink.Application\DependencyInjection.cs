using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using ServiceLink.Application.Behaviors;
using System.Reflection;

namespace ServiceLink.Application;

/// <summary>
/// Configuration de l'injection de dépendances pour la couche Application
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Ajoute les services de la couche Application
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Enregistrement de MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
        });

        // Enregistrement des validateurs FluentValidation
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        // Enregistrement des behaviors MediatR dans l'ordre d'exécution
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));

        return services;
    }
}

/// <summary>
/// Extensions pour la configuration des services Application
/// </summary>
public static class ApplicationServiceExtensions
{
    /// <summary>
    /// Configure les options de validation
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection ConfigureValidation(this IServiceCollection services)
    {
        // Configuration globale de FluentValidation
        ValidatorOptions.Global.LanguageManager.Enabled = true;
        ValidatorOptions.Global.DefaultRuleLevelCascadeMode = CascadeMode.Stop;
        ValidatorOptions.Global.DefaultClassLevelCascadeMode = CascadeMode.Stop;

        return services;
    }

    /// <summary>
    /// Configure les options de MediatR
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection ConfigureMediatR(this IServiceCollection services)
    {
        // Configuration personnalisée de MediatR si nécessaire
        // Par exemple, configuration des timeouts, retry policies, etc.
        
        return services;
    }

    /// <summary>
    /// Ajoute tous les services Application avec configuration
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        return services
            .AddApplicationServices()
            .ConfigureValidation()
            .ConfigureMediatR();
    }
}
