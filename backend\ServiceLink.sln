﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServiceLink.API", "src\ServiceLink.API\ServiceLink.API.csproj", "{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServiceLink.Domain", "src\ServiceLink.Domain\ServiceLink.Domain.csproj", "{18623B65-C932-40E3-8079-2C0BF329FA41}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServiceLink.Application", "src\ServiceLink.Application\ServiceLink.Application.csproj", "{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ServiceLink.Infrastructure", "src\ServiceLink.Infrastructure\ServiceLink.Infrastructure.csproj", "{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Debug|x64.Build.0 = Debug|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Debug|x86.Build.0 = Debug|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Release|x64.ActiveCfg = Release|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Release|x64.Build.0 = Release|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Release|x86.ActiveCfg = Release|Any CPU
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}.Release|x86.Build.0 = Release|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Debug|x64.ActiveCfg = Debug|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Debug|x64.Build.0 = Debug|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Debug|x86.ActiveCfg = Debug|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Debug|x86.Build.0 = Debug|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Release|Any CPU.Build.0 = Release|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Release|x64.ActiveCfg = Release|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Release|x64.Build.0 = Release|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Release|x86.ActiveCfg = Release|Any CPU
		{18623B65-C932-40E3-8079-2C0BF329FA41}.Release|x86.Build.0 = Release|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Debug|x64.Build.0 = Debug|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Debug|x86.Build.0 = Debug|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Release|x64.ActiveCfg = Release|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Release|x64.Build.0 = Release|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Release|x86.ActiveCfg = Release|Any CPU
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}.Release|x86.Build.0 = Release|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Debug|x64.Build.0 = Debug|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Debug|x86.Build.0 = Debug|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Release|x64.ActiveCfg = Release|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Release|x64.Build.0 = Release|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Release|x86.ActiveCfg = Release|Any CPU
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{18623B65-C932-40E3-8079-2C0BF329FA41} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{EF41BA6F-68DC-4F69-A28C-818F9BC12D64} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{A8C67AA4-7F5F-4C10-8AA7-72976269E44A} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
	EndGlobalSection
EndGlobal
