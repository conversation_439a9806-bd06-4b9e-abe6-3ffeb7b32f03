using ServiceLink.Domain.Entities;
using System.Linq.Expressions;

namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface générique pour les repositories
/// Définit les opérations CRUD de base pour toutes les entités
/// </summary>
/// <typeparam name="T">Type d'entité héritant de BaseEntity</typeparam>
public interface IRepository<T> where T : BaseEntity
{
    /// <summary>
    /// Obtient une entité par son identifiant
    /// </summary>
    /// <param name="id">Identifiant de l'entité</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'entité si trouvée, null sinon</returns>
    Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient toutes les entités
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste de toutes les entités</returns>
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Trouve des entités selon un prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des entités correspondantes</returns>
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trouve la première entité correspondant au prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>La première entité trouvée ou null</returns>
    Task<T?> FindFirstAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si une entité existe selon un prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'entité existe</returns>
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Compte le nombre d'entités selon un prédicat
    /// </summary>
    /// <param name="predicate">Condition de recherche (optionnelle)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre d'entités</returns>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient des entités avec pagination
    /// </summary>
    /// <param name="pageNumber">Numéro de page (commence à 1)</param>
    /// <param name="pageSize">Taille de la page</param>
    /// <param name="predicate">Condition de recherche (optionnelle)</param>
    /// <param name="orderBy">Fonction de tri (optionnelle)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat paginé</returns>
    Task<PagedResult<T>> GetPagedAsync(
        int pageNumber,
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Ajoute une nouvelle entité
    /// </summary>
    /// <param name="entity">Entité à ajouter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'entité ajoutée</returns>
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ajoute plusieurs entités
    /// </summary>
    /// <param name="entities">Entités à ajouter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);

    /// <summary>
    /// Met à jour une entité
    /// </summary>
    /// <param name="entity">Entité à mettre à jour</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'entité mise à jour</returns>
    Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Supprime une entité (soft delete)
    /// </summary>
    /// <param name="entity">Entité à supprimer</param>
    /// <param name="deletedBy">Utilisateur qui supprime</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task DeleteAsync(T entity, Guid? deletedBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Supprime une entité par son identifiant (soft delete)
    /// </summary>
    /// <param name="id">Identifiant de l'entité</param>
    /// <param name="deletedBy">Utilisateur qui supprime</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task DeleteByIdAsync(Guid id, Guid? deletedBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Supprime définitivement une entité (hard delete)
    /// </summary>
    /// <param name="entity">Entité à supprimer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task HardDeleteAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Restaure une entité supprimée
    /// </summary>
    /// <param name="entity">Entité à restaurer</param>
    /// <param name="restoredBy">Utilisateur qui restaure</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    Task RestoreAsync(T entity, Guid? restoredBy = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Résultat paginé pour les requêtes
/// </summary>
/// <typeparam name="T">Type d'entité</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="items">Éléments de la page</param>
    /// <param name="totalCount">Nombre total d'éléments</param>
    /// <param name="pageNumber">Numéro de page actuel</param>
    /// <param name="pageSize">Taille de la page</param>
    public PagedResult(IEnumerable<T> items, int totalCount, int pageNumber, int pageSize)
    {
        Items = items.ToList();
        TotalCount = totalCount;
        PageNumber = pageNumber;
        PageSize = pageSize;
        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        HasPreviousPage = pageNumber > 1;
        HasNextPage = pageNumber < TotalPages;
    }

    /// <summary>
    /// Éléments de la page courante
    /// </summary>
    public IReadOnlyList<T> Items { get; }

    /// <summary>
    /// Nombre total d'éléments
    /// </summary>
    public int TotalCount { get; }

    /// <summary>
    /// Numéro de page actuel
    /// </summary>
    public int PageNumber { get; }

    /// <summary>
    /// Taille de la page
    /// </summary>
    public int PageSize { get; }

    /// <summary>
    /// Nombre total de pages
    /// </summary>
    public int TotalPages { get; }

    /// <summary>
    /// Indique s'il y a une page précédente
    /// </summary>
    public bool HasPreviousPage { get; }

    /// <summary>
    /// Indique s'il y a une page suivante
    /// </summary>
    public bool HasNextPage { get; }

    /// <summary>
    /// Index du premier élément de la page
    /// </summary>
    public int FirstItemIndex => (PageNumber - 1) * PageSize + 1;

    /// <summary>
    /// Index du dernier élément de la page
    /// </summary>
    public int LastItemIndex => Math.Min(PageNumber * PageSize, TotalCount);
}
