namespace ServiceLink.Application.Interfaces;

/// <summary>
/// Service pour la gestion des mots de passe
/// </summary>
public interface IPasswordService
{
    /// <summary>
    /// Hache un mot de passe avec un salt généré automatiquement
    /// </summary>
    /// <param name="password">Mot de passe en clair</param>
    /// <returns>Tuple contenant le hash et le salt</returns>
    (string hash, string salt) HashPassword(string password);

    /// <summary>
    /// Vérifie si un mot de passe correspond au hash stocké
    /// </summary>
    /// <param name="password">Mot de passe en clair</param>
    /// <param name="hash">Hash stocké</param>
    /// <param name="salt">Salt utilisé</param>
    /// <returns>True si le mot de passe est correct</returns>
    bool VerifyPassword(string password, string hash, string salt);

    /// <summary>
    /// Génère un mot de passe aléatoire sécurisé
    /// </summary>
    /// <param name="length">Longueur du mot de passe (défaut: 12)</param>
    /// <param name="includeSpecialChars">Inclure des caractères spéciaux</param>
    /// <returns>Mot de passe généré</returns>
    string GeneratePassword(int length = 12, bool includeSpecialChars = true);

    /// <summary>
    /// Valide la force d'un mot de passe
    /// </summary>
    /// <param name="password">Mot de passe à valider</param>
    /// <returns>Résultat de la validation</returns>
    PasswordValidationResult ValidatePasswordStrength(string password);

    /// <summary>
    /// Génère un token de réinitialisation de mot de passe
    /// </summary>
    /// <returns>Token sécurisé</returns>
    string GeneratePasswordResetToken();

    /// <summary>
    /// Génère un token de confirmation d'email
    /// </summary>
    /// <returns>Token sécurisé</returns>
    string GenerateEmailConfirmationToken();
}

/// <summary>
/// Résultat de la validation d'un mot de passe
/// </summary>
public class PasswordValidationResult
{
    /// <summary>
    /// Indique si le mot de passe est valide
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Score de force du mot de passe (0-100)
    /// </summary>
    public int StrengthScore { get; set; }

    /// <summary>
    /// Liste des erreurs de validation
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Suggestions d'amélioration
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// Niveau de force du mot de passe
    /// </summary>
    public PasswordStrengthLevel StrengthLevel { get; set; }
}

/// <summary>
/// Niveaux de force d'un mot de passe
/// </summary>
public enum PasswordStrengthLevel
{
    /// <summary>
    /// Très faible
    /// </summary>
    VeryWeak = 0,

    /// <summary>
    /// Faible
    /// </summary>
    Weak = 1,

    /// <summary>
    /// Moyen
    /// </summary>
    Medium = 2,

    /// <summary>
    /// Fort
    /// </summary>
    Strong = 3,

    /// <summary>
    /// Très fort
    /// </summary>
    VeryStrong = 4
}
