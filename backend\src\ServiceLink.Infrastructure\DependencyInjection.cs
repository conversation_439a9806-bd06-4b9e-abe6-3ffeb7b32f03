using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Infrastructure.Data;
using ServiceLink.Infrastructure.Repositories;
using ServiceLink.Infrastructure.Services;

namespace ServiceLink.Infrastructure;

/// <summary>
/// Configuration de l'injection de dépendances pour la couche Infrastructure
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Ajoute les services de la couche Infrastructure
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration de l'application</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration de la base de données
        services.AddDatabase(configuration);

        // Enregistrement des repositories
        services.AddRepositories();

        // Enregistrement des services application
        services.AddApplicationServices(configuration);

        return services;
    }

    /// <summary>
    /// Configure la base de données
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Collection de services configurée</returns>
    private static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? "Host=localhost;Database=ServiceLink;Username=********;Password=********";

        services.AddDbContext<ServiceLinkDbContext>(options =>
        {
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsAssembly(typeof(ServiceLinkDbContext).Assembly.FullName);
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);
            });

            // Configuration pour le développement
            if (configuration.GetValue<bool>("Development:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }

            if (configuration.GetValue<bool>("Development:EnableDetailedErrors"))
            {
                options.EnableDetailedErrors();
            }

            // Configuration pour la production
            options.EnableServiceProviderCaching();
            options.EnableSensitiveDataLogging(false); // Désactivé en production
        });

        return services;
    }

    /// <summary>
    /// Enregistre les repositories
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <returns>Collection de services configurée</returns>
    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        // Repository générique
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Repositories spécialisés
        services.AddScoped<IUserRepository, UserRepository>();

        // Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        return services;
    }

    /// <summary>
    /// Enregistre les services application
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Collection de services configurée</returns>
    private static IServiceCollection AddApplicationServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration des services
        services.Configure<EmailSettings>(options => configuration.GetSection("Email").Bind(options));

        // Services application
        services.AddScoped<IPasswordService, PasswordService>();
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<ITwoFactorService, TwoFactorService>();

        return services;
    }

    /// <summary>
    /// Configure les options de performance
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection ConfigurePerformance(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration du pool de connexions
        services.AddDbContextPool<ServiceLinkDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? "Host=localhost;Database=ServiceLink;Username=********;Password=********";

            options.UseNpgsql(connectionString);
        }, poolSize: configuration.GetValue<int>("Database:PoolSize", 128));

        return services;
    }

    /// <summary>
    /// Configure les health checks
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddInfrastructureHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHealthChecks()
            .AddDbContextCheck<ServiceLinkDbContext>("database")
            .AddCheck("email_service", () => 
            {
                // Vérification basique du service email
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Email service is available");
            });

        return services;
    }

    /// <summary>
    /// Ajoute tous les services Infrastructure avec configuration complète
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddInfrastructureServices(configuration)
            .AddInfrastructureHealthChecks(configuration);
    }
}

/// <summary>
/// Extensions pour la configuration des services Infrastructure
/// </summary>
public static class InfrastructureServiceExtensions
{
    /// <summary>
    /// Configure les migrations automatiques
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="autoMigrate">Activer les migrations automatiques</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection ConfigureAutoMigration(this IServiceCollection services, bool autoMigrate = false)
    {
        if (autoMigrate)
        {
            services.AddHostedService<DatabaseMigrationService>();
        }

        return services;
    }

    /// <summary>
    /// Configure la résilience de la base de données
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="maxRetryCount">Nombre maximum de tentatives</param>
    /// <param name="maxRetryDelay">Délai maximum entre les tentatives</param>
    /// <returns>Collection de services configurée</returns>
    public static IServiceCollection ConfigureDatabaseResilience(this IServiceCollection services, int maxRetryCount = 3, TimeSpan? maxRetryDelay = null)
    {
        var delay = maxRetryDelay ?? TimeSpan.FromSeconds(30);

        services.AddDbContext<ServiceLinkDbContext>(options =>
        {
            options.UseNpgsql(connectionString =>
            {
                connectionString.EnableRetryOnFailure(
                    maxRetryCount: maxRetryCount,
                    maxRetryDelay: delay,
                    errorCodesToAdd: null);
            });
        });

        return services;
    }
}

/// <summary>
/// Service pour les migrations automatiques de base de données
/// </summary>
public class DatabaseMigrationService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseMigrationService> _logger;

    /// <summary>
    /// Constructeur
    /// </summary>
    /// <param name="serviceProvider">Provider de services</param>
    /// <param name="logger">Logger</param>
    public DatabaseMigrationService(IServiceProvider serviceProvider, ILogger<DatabaseMigrationService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// Démarre le service et applique les migrations
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Démarrage du service de migration de base de données");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ServiceLinkDbContext>();

            _logger.LogInformation("Application des migrations en attente...");
            await context.Database.MigrateAsync(cancellationToken);
            _logger.LogInformation("Migrations appliquées avec succès");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'application des migrations");
            throw;
        }
    }

    /// <summary>
    /// Arrête le service
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Task</returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Arrêt du service de migration de base de données");
        return Task.CompletedTask;
    }
}
