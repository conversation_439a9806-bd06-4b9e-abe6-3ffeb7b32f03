{"format": 1, "restore": {"D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.API.Tests\\ServiceLink.API.Tests.csproj": {}}, "projects": {"D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.API.Tests\\ServiceLink.API.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.API.Tests\\ServiceLink.API.Tests.csproj", "projectName": "ServiceLink.API.Tests", "projectPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.API.Tests\\ServiceLink.API.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\tests\\ServiceLink.API.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}