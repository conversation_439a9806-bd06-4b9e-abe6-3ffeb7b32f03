# Script de déploiement PowerShell pour ServiceLink
# Usage: .\scripts\deploy.ps1 -Environment [development|staging|production] -Version [version]
# Exemple: .\scripts\deploy.ps1 -Environment production -Version v1.0.0

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("development", "staging", "production")]
    [string]$Environment = "staging",
    
    [Parameter(Mandatory=$false)]
    [string]$Version = "latest",
    
    [Parameter(Mandatory=$false)]
    [string]$DockerRegistry = $env:DOCKER_REGISTRY
)

# Configuration
$ProjectName = "servicelink"
$ErrorActionPreference = "Stop"

# Fonctions utilitaires pour les messages colorés
function Write-Log {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ✓ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ⚠ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ✗ $Message" -ForegroundColor Red
}

# Vérifier les prérequis
function Test-Prerequisites {
    Write-Log "Vérification des prérequis..."
    
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker n'est pas installé ou n'est pas dans le PATH"
        exit 1
    }
    
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "Docker Compose n'est pas installé ou n'est pas dans le PATH"
        exit 1
    }
    
    Write-Success "Prérequis vérifiés"
}

# Charger les variables d'environnement
function Import-EnvironmentVariables {
    Write-Log "Chargement des variables d'environnement..."
    
    $envFile = ".env.$Environment"
    if (Test-Path $envFile) {
        Get-Content $envFile | Where-Object { $_ -notmatch '^#' -and $_ -match '=' } | ForEach-Object {
            $key, $value = $_ -split '=', 2
            [Environment]::SetEnvironmentVariable($key, $value, "Process")
        }
        Write-Success "Variables d'environnement chargées depuis $envFile"
    }
    elseif (Test-Path ".env") {
        Get-Content ".env" | Where-Object { $_ -notmatch '^#' -and $_ -match '=' } | ForEach-Object {
            $key, $value = $_ -split '=', 2
            [Environment]::SetEnvironmentVariable($key, $value, "Process")
        }
        Write-Warning "Utilisation du fichier .env par défaut"
    }
    else {
        Write-Warning "Aucun fichier d'environnement trouvé"
    }
}

# Construire les images Docker
function Build-DockerImages {
    Write-Log "Construction des images Docker..."
    
    $env:DOCKER_TAG = $Version
    
    try {
        if ($Environment -eq "development") {
            docker-compose -f docker-compose.yml -f docker-compose.dev.yml build
        }
        else {
            docker-compose build
        }
        Write-Success "Images Docker construites avec le tag: $Version"
    }
    catch {
        Write-Error "Erreur lors de la construction des images: $_"
        throw
    }
}

# Pousser les images vers le registry
function Push-DockerImages {
    if ($Environment -ne "development" -and $DockerRegistry) {
        Write-Log "Envoi des images vers le registry..."
        
        try {
            docker tag "${ProjectName}-api:$Version" "$DockerRegistry/${ProjectName}-api:$Version"
            docker push "$DockerRegistry/${ProjectName}-api:$Version"
            Write-Success "Images envoyées vers le registry"
        }
        catch {
            Write-Error "Erreur lors de l'envoi vers le registry: $_"
            throw
        }
    }
}

# Déployer l'application
function Deploy-Application {
    Write-Log "Déploiement de l'application..."
    
    try {
        switch ($Environment) {
            "development" {
                docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
            }
            default {
                docker-compose up -d
            }
        }
        Write-Success "Application déployée en mode $Environment"
    }
    catch {
        Write-Error "Erreur lors du déploiement: $_"
        throw
    }
}

# Vérifier la santé de l'application
function Test-ApplicationHealth {
    Write-Log "Vérification de la santé de l'application..."
    
    $maxAttempts = 30
    $attempt = 0
    
    do {
        $attempt++
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Success "Application en bonne santé"
                return $true
            }
        }
        catch {
            # Ignorer les erreurs et continuer à essayer
        }
        
        Start-Sleep -Seconds 2
    } while ($attempt -lt $maxAttempts)
    
    Write-Error "L'application ne répond pas après $($maxAttempts * 2) secondes"
    return $false
}

# Afficher les logs en cas d'erreur
function Show-Logs {
    Write-Error "Déploiement échoué. Affichage des logs..."
    docker-compose logs --tail=50
}

# Fonction principale
function Main {
    Write-Log "Début du déploiement ServiceLink"
    Write-Log "Environnement: $Environment"
    Write-Log "Version: $Version"
    
    try {
        Test-Prerequisites
        Import-EnvironmentVariables
        Build-DockerImages
        Push-DockerImages
        Deploy-Application
        
        # Attendre un peu avant de vérifier la santé
        Start-Sleep -Seconds 10
        
        if (Test-ApplicationHealth) {
            Write-Success "Déploiement réussi! 🎉"
            Write-Log "L'application est accessible sur: http://localhost:8080"
            
            if ($Environment -eq "development") {
                Write-Log "Interface d'administration de la base de données: http://localhost:5050"
                Write-Log "Interface Redis: http://localhost:8081"
                Write-Log "Interface email (MailHog): http://localhost:8025"
            }
        }
        else {
            Show-Logs
            exit 1
        }
    }
    catch {
        Write-Error "Erreur durant le déploiement: $_"
        Show-Logs
        exit 1
    }
}

# Exécuter le script principal
Main
