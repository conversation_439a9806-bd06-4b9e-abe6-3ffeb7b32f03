using MediatR;
using ServiceLink.Application.DTOs;
using ServiceLink.Domain.Enums;

namespace ServiceLink.Application.Commands;

/// <summary>
/// Commande pour créer un nouvel utilisateur
/// </summary>
public class CreateUserCommand : IRequest<UserResponse>
{
    /// <summary>
    /// Adresse email de l'utilisateur
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Prénom de l'utilisateur
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom de famille de l'utilisateur
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Mot de passe de l'utilisateur
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Numéro de téléphone (optionnel)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Rôle de l'utilisateur
    /// </summary>
    public UserRole Role { get; set; } = UserRole.Client;

    /// <summary>
    /// Langue préférée
    /// </summary>
    public string Language { get; set; } = "fr-FR";

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// Utilisateur qui crée (pour l'audit)
    /// </summary>
    public Guid? CreatedBy { get; set; }
}

/// <summary>
/// Commande pour mettre à jour un utilisateur
/// </summary>
public class UpdateUserCommand : IRequest<UserResponse>
{
    /// <summary>
    /// Identifiant de l'utilisateur à mettre à jour
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Prénom de l'utilisateur
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Nom de famille de l'utilisateur
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Numéro de téléphone (optionnel)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Langue préférée
    /// </summary>
    public string Language { get; set; } = "fr-FR";

    /// <summary>
    /// Fuseau horaire
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// URL de l'avatar
    /// </summary>
    public string? AvatarUrl { get; set; }

    /// <summary>
    /// Utilisateur qui modifie (pour l'audit)
    /// </summary>
    public Guid? UpdatedBy { get; set; }
}

/// <summary>
/// Commande pour changer le mot de passe d'un utilisateur
/// </summary>
public class ChangePasswordCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Mot de passe actuel
    /// </summary>
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// Nouveau mot de passe
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// Utilisateur qui modifie (pour l'audit)
    /// </summary>
    public Guid? UpdatedBy { get; set; }
}

/// <summary>
/// Commande pour confirmer l'email d'un utilisateur
/// </summary>
public class ConfirmEmailCommand : IRequest<bool>
{
    /// <summary>
    /// Token de confirmation d'email
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Utilisateur qui confirme (pour l'audit)
    /// </summary>
    public Guid? ConfirmedBy { get; set; }
}

/// <summary>
/// Commande pour confirmer le téléphone d'un utilisateur
/// </summary>
public class ConfirmPhoneCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Code de confirmation SMS
    /// </summary>
    public string ConfirmationCode { get; set; } = string.Empty;

    /// <summary>
    /// Utilisateur qui confirme (pour l'audit)
    /// </summary>
    public Guid? ConfirmedBy { get; set; }
}

/// <summary>
/// Commande pour activer l'authentification à deux facteurs
/// </summary>
public class EnableTwoFactorCommand : IRequest<string[]>
{
    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Utilisateur qui active (pour l'audit)
    /// </summary>
    public Guid? EnabledBy { get; set; }
}

/// <summary>
/// Commande pour désactiver l'authentification à deux facteurs
/// </summary>
public class DisableTwoFactorCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Code 2FA pour confirmation
    /// </summary>
    public string TwoFactorCode { get; set; } = string.Empty;

    /// <summary>
    /// Utilisateur qui désactive (pour l'audit)
    /// </summary>
    public Guid? DisabledBy { get; set; }
}

/// <summary>
/// Commande pour désactiver un utilisateur
/// </summary>
public class DeactivateUserCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur à désactiver
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Raison de la désactivation
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// Utilisateur qui désactive (pour l'audit)
    /// </summary>
    public Guid? DeactivatedBy { get; set; }
}

/// <summary>
/// Commande pour réactiver un utilisateur
/// </summary>
public class ActivateUserCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur à réactiver
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Utilisateur qui réactive (pour l'audit)
    /// </summary>
    public Guid? ActivatedBy { get; set; }
}

/// <summary>
/// Commande pour changer le rôle d'un utilisateur
/// </summary>
public class ChangeUserRoleCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Nouveau rôle
    /// </summary>
    public UserRole NewRole { get; set; }

    /// <summary>
    /// Utilisateur qui change le rôle (pour l'audit)
    /// </summary>
    public Guid? ChangedBy { get; set; }
}

/// <summary>
/// Commande pour déverrouiller un compte utilisateur
/// </summary>
public class UnlockUserAccountCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur à déverrouiller
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Utilisateur qui déverrouille (pour l'audit)
    /// </summary>
    public Guid? UnlockedBy { get; set; }
}

/// <summary>
/// Commande pour supprimer un utilisateur (soft delete)
/// </summary>
public class DeleteUserCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur à supprimer
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Utilisateur qui supprime (pour l'audit)
    /// </summary>
    public Guid? DeletedBy { get; set; }
}

/// <summary>
/// Commande pour restaurer un utilisateur supprimé
/// </summary>
public class RestoreUserCommand : IRequest<bool>
{
    /// <summary>
    /// Identifiant de l'utilisateur à restaurer
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Utilisateur qui restaure (pour l'audit)
    /// </summary>
    public Guid? RestoredBy { get; set; }
}

/// <summary>
/// Commande pour demander la réinitialisation du mot de passe
/// </summary>
public class RequestPasswordResetCommand : IRequest<bool>
{
    /// <summary>
    /// Adresse email de l'utilisateur
    /// </summary>
    public string Email { get; set; } = string.Empty;
}

/// <summary>
/// Commande pour réinitialiser le mot de passe
/// </summary>
public class ResetPasswordCommand : IRequest<bool>
{
    /// <summary>
    /// Token de réinitialisation
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Nouveau mot de passe
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;
}
