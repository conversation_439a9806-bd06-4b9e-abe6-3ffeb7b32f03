﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using ServiceLink.Infrastructure.Data;

#nullable disable

namespace ServiceLink.Infrastructure.Data.Migrations
{
    [DbContext(typeof(ServiceLinkDbContext))]
    [Migration("20250702221148_InitialMigration")]
    partial class InitialMigration
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("ServiceLink.Domain.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AvatarUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("URL de l'avatar de l'utilisateur");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("EmailConfirmationToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("Token de confirmation d'email");

                    b.Property<DateTime?>("EmailConfirmationTokenExpiry")
                        .HasColumnType("timestamp with time zone")
                        .HasComment("Date d'expiration du token de confirmation d'email");

                    b.Property<int>("FailedLoginAttempts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasComment("Nombre de tentatives de connexion échouées");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("Prénom de l'utilisateur");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasComment("Indique si l'utilisateur est actif");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEmailConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasComment("Indique si l'email est confirmé");

                    b.Property<bool>("IsPhoneConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasComment("Indique si le numéro de téléphone est confirmé");

                    b.Property<bool>("IsTwoFactorEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasComment("Indique si l'authentification à deux facteurs est activée");

                    b.Property<string>("Language")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("fr-FR")
                        .HasComment("Langue préférée de l'utilisateur");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone")
                        .HasComment("Date de dernière connexion");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasComment("Nom de famille de l'utilisateur");

                    b.Property<DateTime?>("LockedUntil")
                        .HasColumnType("timestamp with time zone")
                        .HasComment("Date de fin de verrouillage du compte");

                    b.Property<DateTime?>("PasswordChangedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasComment("Date de dernière modification du mot de passe");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("Hash du mot de passe");

                    b.Property<string>("PasswordResetToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("Token de réinitialisation de mot de passe");

                    b.Property<DateTime?>("PasswordResetTokenExpiry")
                        .HasColumnType("timestamp with time zone")
                        .HasComment("Date d'expiration du token de réinitialisation");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("Salt pour le hachage du mot de passe");

                    b.Property<string>("Preferences")
                        .HasMaxLength(500)
                        .HasColumnType("jsonb")
                        .HasComment("Préférences de l'utilisateur au format JSON");

                    b.Property<int>("ProfileCompletionPercentage")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasComment("Pourcentage de complétion du profil");

                    b.Property<int>("Role")
                        .HasColumnType("integer")
                        .HasComment("Rôle de l'utilisateur dans le système");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("bytea");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Europe/Paris")
                        .HasComment("Fuseau horaire de l'utilisateur");

                    b.Property<string>("TwoFactorRecoveryCodes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasComment("Codes de récupération pour l'authentification à deux facteurs");

                    b.Property<string>("TwoFactorSecret")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasComment("Secret pour l'authentification à deux facteurs");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Users_CreatedAt");

                    b.HasIndex("EmailConfirmationToken")
                        .HasDatabaseName("IX_Users_EmailConfirmationToken")
                        .HasFilter("\"EmailConfirmationToken\" IS NOT NULL");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Users_IsActive");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_Users_IsDeleted");

                    b.HasIndex("IsEmailConfirmed")
                        .HasDatabaseName("IX_Users_IsEmailConfirmed");

                    b.HasIndex("LastLoginAt")
                        .HasDatabaseName("IX_Users_LastLoginAt");

                    b.HasIndex("PasswordResetToken")
                        .HasDatabaseName("IX_Users_PasswordResetToken")
                        .HasFilter("\"PasswordResetToken\" IS NOT NULL");

                    b.HasIndex("Role")
                        .HasDatabaseName("IX_Users_Role");

                    b.HasIndex("LockedUntil", "FailedLoginAttempts")
                        .HasDatabaseName("IX_Users_LockedAccount")
                        .HasFilter("\"LockedUntil\" IS NOT NULL");

                    b.ToTable("Users", null, t =>
                        {
                            t.HasCheckConstraint("CK_Users_EmailTokenExpiry", "\"EmailConfirmationToken\" IS NULL OR \"EmailConfirmationTokenExpiry\" IS NOT NULL");

                            t.HasCheckConstraint("CK_Users_FailedLoginAttempts", "\"FailedLoginAttempts\" >= 0");

                            t.HasCheckConstraint("CK_Users_PasswordTokenExpiry", "\"PasswordResetToken\" IS NULL OR \"PasswordResetTokenExpiry\" IS NOT NULL");

                            t.HasCheckConstraint("CK_Users_ProfileCompletionPercentage", "\"ProfileCompletionPercentage\" >= 0 AND \"ProfileCompletionPercentage\" <= 100");

                            t.HasCheckConstraint("CK_Users_TwoFactorConsistency", "\"IsTwoFactorEnabled\" = false OR \"TwoFactorSecret\" IS NOT NULL");
                        });
                });

            modelBuilder.Entity("ServiceLink.Domain.Entities.User", b =>
                {
                    b.OwnsOne("ServiceLink.Domain.ValueObjects.Email", "Email", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(254)
                                .HasColumnType("character varying(254)")
                                .HasColumnName("Email")
                                .HasComment("Adresse email de l'utilisateur");

                            b1.HasKey("UserId");

                            b1.HasIndex("Value")
                                .IsUnique()
                                .HasDatabaseName("IX_Users_Email");

                            b1.ToTable("Users");

                            b1.WithOwner()
                                .HasForeignKey("UserId");
                        });

                    b.OwnsOne("ServiceLink.Domain.ValueObjects.PhoneNumber", "PhoneNumber", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("uuid");

                            b1.Property<string>("CountryCode")
                                .IsRequired()
                                .HasMaxLength(5)
                                .HasColumnType("character varying(5)")
                                .HasColumnName("PhoneCountryCode")
                                .HasComment("Code pays du numéro de téléphone");

                            b1.Property<string>("NationalNumber")
                                .IsRequired()
                                .HasMaxLength(15)
                                .HasColumnType("character varying(15)")
                                .HasColumnName("PhoneNationalNumber")
                                .HasComment("Numéro national sans le code pays");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("PhoneNumber")
                                .HasComment("Numéro de téléphone de l'utilisateur");

                            b1.HasKey("UserId");

                            b1.HasIndex("Value")
                                .IsUnique()
                                .HasDatabaseName("IX_Users_PhoneNumber")
                                .HasFilter("\"PhoneNumber\" IS NOT NULL");

                            b1.ToTable("Users");

                            b1.WithOwner()
                                .HasForeignKey("UserId");
                        });

                    b.Navigation("Email")
                        .IsRequired();

                    b.Navigation("PhoneNumber");
                });
#pragma warning restore 612, 618
        }
    }
}
