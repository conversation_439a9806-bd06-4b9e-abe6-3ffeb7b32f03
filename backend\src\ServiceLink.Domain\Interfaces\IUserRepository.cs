using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Domain.Interfaces;

/// <summary>
/// Interface spécialisée pour le repository des utilisateurs
/// Étend IRepository avec des méthodes spécifiques aux utilisateurs
/// </summary>
public interface IUserRepository : IRepository<User>
{
    /// <summary>
    /// Trouve un utilisateur par son adresse email
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    Task<User?> GetByEmailAsync(Email email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trouve un utilisateur par son adresse email (string)
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trouve un utilisateur par son numéro de téléphone
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    Task<User?> GetByPhoneNumberAsync(PhoneNumber phoneNumber, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trouve un utilisateur par son token de confirmation d'email
    /// </summary>
    /// <param name="token">Token de confirmation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    Task<User?> GetByEmailConfirmationTokenAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trouve un utilisateur par son token de réinitialisation de mot de passe
    /// </summary>
    /// <param name="token">Token de réinitialisation</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>L'utilisateur si trouvé, null sinon</returns>
    Task<User?> GetByPasswordResetTokenAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si un email existe déjà
    /// </summary>
    /// <param name="email">Adresse email</param>
    /// <param name="excludeUserId">ID utilisateur à exclure de la vérification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si l'email existe</returns>
    Task<bool> EmailExistsAsync(Email email, Guid? excludeUserId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si un numéro de téléphone existe déjà
    /// </summary>
    /// <param name="phoneNumber">Numéro de téléphone</param>
    /// <param name="excludeUserId">ID utilisateur à exclure de la vérification</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>True si le numéro existe</returns>
    Task<bool> PhoneNumberExistsAsync(PhoneNumber phoneNumber, Guid? excludeUserId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs par rôle
    /// </summary>
    /// <param name="role">Rôle recherché</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs avec ce rôle</returns>
    Task<IEnumerable<User>> GetByRoleAsync(UserRole role, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs actifs
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs actifs</returns>
    Task<IEnumerable<User>> GetActiveUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs inactifs
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs inactifs</returns>
    Task<IEnumerable<User>> GetInactiveUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs avec email non confirmé
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs avec email non confirmé</returns>
    Task<IEnumerable<User>> GetUsersWithUnconfirmedEmailAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs verrouillés
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs verrouillés</returns>
    Task<IEnumerable<User>> GetLockedUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs créés dans une période
    /// </summary>
    /// <param name="startDate">Date de début</param>
    /// <param name="endDate">Date de fin</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs créés dans la période</returns>
    Task<IEnumerable<User>> GetUsersCreatedBetweenAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs connectés récemment
    /// </summary>
    /// <param name="since">Date depuis laquelle chercher</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs connectés récemment</returns>
    Task<IEnumerable<User>> GetRecentlyLoggedInUsersAsync(DateTime since, CancellationToken cancellationToken = default);

    /// <summary>
    /// Recherche des utilisateurs par nom ou email
    /// </summary>
    /// <param name="searchTerm">Terme de recherche</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste des utilisateurs correspondants</returns>
    Task<IEnumerable<User>> SearchUsersAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les statistiques des utilisateurs
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Statistiques des utilisateurs</returns>
    Task<UserStatistics> GetUserStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les utilisateurs avec pagination et filtres
    /// </summary>
    /// <param name="pageNumber">Numéro de page</param>
    /// <param name="pageSize">Taille de la page</param>
    /// <param name="role">Filtre par rôle (optionnel)</param>
    /// <param name="isActive">Filtre par statut actif (optionnel)</param>
    /// <param name="isEmailConfirmed">Filtre par email confirmé (optionnel)</param>
    /// <param name="searchTerm">Terme de recherche (optionnel)</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat paginé avec filtres</returns>
    Task<PagedResult<User>> GetUsersPagedAsync(
        int pageNumber,
        int pageSize,
        UserRole? role = null,
        bool? isActive = null,
        bool? isEmailConfirmed = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Nettoie les tokens expirés
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre de tokens nettoyés</returns>
    Task<int> CleanupExpiredTokensAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Déverrouille automatiquement les comptes dont la période de verrouillage est expirée
    /// </summary>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nombre de comptes déverrouillés</returns>
    Task<int> UnlockExpiredAccountsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistiques des utilisateurs
/// </summary>
public class UserStatistics
{
    /// <summary>
    /// Nombre total d'utilisateurs
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs actifs
    /// </summary>
    public int ActiveUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs inactifs
    /// </summary>
    public int InactiveUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs avec email confirmé
    /// </summary>
    public int EmailConfirmedUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs avec email non confirmé
    /// </summary>
    public int EmailUnconfirmedUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs verrouillés
    /// </summary>
    public int LockedUsers { get; set; }

    /// <summary>
    /// Nombre d'utilisateurs avec 2FA activé
    /// </summary>
    public int TwoFactorEnabledUsers { get; set; }

    /// <summary>
    /// Répartition par rôle
    /// </summary>
    public Dictionary<UserRole, int> UsersByRole { get; set; } = new();

    /// <summary>
    /// Nouveaux utilisateurs ce mois
    /// </summary>
    public int NewUsersThisMonth { get; set; }

    /// <summary>
    /// Utilisateurs connectés aujourd'hui
    /// </summary>
    public int UsersLoggedInToday { get; set; }

    /// <summary>
    /// Utilisateurs connectés cette semaine
    /// </summary>
    public int UsersLoggedInThisWeek { get; set; }

    /// <summary>
    /// Pourcentage de complétion moyenne des profils
    /// </summary>
    public double AverageProfileCompletion { get; set; }
}
