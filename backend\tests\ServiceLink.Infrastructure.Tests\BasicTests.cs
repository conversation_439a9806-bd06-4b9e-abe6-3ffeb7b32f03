using FluentAssertions;

namespace ServiceLink.Infrastructure.Tests;

/// <summary>
/// Tests de base pour vérifier que l'infrastructure de test fonctionne
/// </summary>
public class BasicTests
{
    [Fact]
    public void BasicTest_ShouldPass()
    {
        // Arrange
        var expected = "Hello World";

        // Act
        var actual = "Hello World";

        // Assert
        actual.Should().Be(expected);
    }

    [Theory]
    [InlineData("test", 4)]
    [InlineData("hello", 5)]
    [InlineData("", 0)]
    public void StringLength_ShouldReturnCorrectLength(string input, int expectedLength)
    {
        // Act
        var result = input.Length;

        // Assert
        result.Should().Be(expectedLength);
    }
}
