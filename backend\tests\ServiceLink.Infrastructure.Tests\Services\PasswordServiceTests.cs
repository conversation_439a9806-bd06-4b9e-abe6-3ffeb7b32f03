using FluentAssertions;
using ServiceLink.Infrastructure.Services;

namespace ServiceLink.Infrastructure.Tests.Services;

/// <summary>
/// Tests unitaires pour PasswordService
/// </summary>
public class PasswordServiceTests
{
    private readonly PasswordService _passwordService;

    public PasswordServiceTests()
    {
        _passwordService = new PasswordService();
    }

    [Fact]
    public void HashPassword_WithValidPassword_ShouldReturnHashAndSalt()
    {
        // Arrange
        var password = "SecurePassword123!";

        // Act
        var (hash, salt) = _passwordService.HashPassword(password);

        // Assert
        hash.Should().NotBeNullOrEmpty();
        salt.Should().NotBeNullOrEmpty();
        hash.Should().NotBe(password);
        salt.Should().NotBe(password);
        hash.Length.Should().BeGreaterThan(50); // BCrypt hashes are typically 60 characters
    }

    [Fact]
    public void HashPassword_WithSamePassword_ShouldReturnDifferentHashes()
    {
        // Arrange
        var password = "SecurePassword123!";

        // Act
        var (hash1, salt1) = _passwordService.HashPassword(password);
        var (hash2, salt2) = _passwordService.HashPassword(password);

        // Assert
        hash1.Should().NotBe(hash2);
        salt1.Should().NotBe(salt2);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void HashPassword_WithInvalidPassword_ShouldThrowArgumentException(string invalidPassword)
    {
        // Act & Assert
        var act = () => _passwordService.HashPassword(invalidPassword);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void VerifyPassword_WithCorrectPassword_ShouldReturnTrue()
    {
        // Arrange
        var password = "SecurePassword123!";
        var (hash, salt) = _passwordService.HashPassword(password);

        // Act
        var result = _passwordService.VerifyPassword(password, hash, salt);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void VerifyPassword_WithIncorrectPassword_ShouldReturnFalse()
    {
        // Arrange
        var correctPassword = "SecurePassword123!";
        var incorrectPassword = "WrongPassword456!";
        var (hash, salt) = _passwordService.HashPassword(correctPassword);

        // Act
        var result = _passwordService.VerifyPassword(incorrectPassword, hash, salt);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("", "hash", "salt")]
    [InlineData(" ", "hash", "salt")]
    [InlineData(null, "hash", "salt")]
    [InlineData("password", "", "salt")]
    [InlineData("password", " ", "salt")]
    [InlineData("password", null, "salt")]
    [InlineData("password", "hash", "")]
    [InlineData("password", "hash", " ")]
    [InlineData("password", "hash", null)]
    public void VerifyPassword_WithInvalidParameters_ShouldThrowArgumentException(string password, string hash, string salt)
    {
        // Act & Assert
        var act = () => _passwordService.VerifyPassword(password, hash, salt);
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData("weak")]
    [InlineData("12345")]
    [InlineData("password")]
    [InlineData("PASSWORD")]
    [InlineData("Password")]
    [InlineData("Password1")]
    [InlineData("password123")]
    [InlineData("PASSWORD123")]
    [InlineData("Password!")]
    [InlineData("password!")]
    [InlineData("PASSWORD!")]
    [InlineData("12345678")]
    [InlineData("!@#$%^&*")]
    public void ValidatePasswordStrength_WithWeakPasswords_ShouldReturnWeakOrVeryWeak(string weakPassword)
    {
        // Act
        var strength = _passwordService.ValidatePasswordStrength(weakPassword);

        // Assert
        strength.Should().BeOneOf(
            ServiceLink.Domain.Enums.PasswordStrength.VeryWeak,
            ServiceLink.Domain.Enums.PasswordStrength.Weak);
    }

    [Theory]
    [InlineData("Password123")]
    [InlineData("MyPassword1")]
    [InlineData("SecurePass1")]
    public void ValidatePasswordStrength_WithMediumPasswords_ShouldReturnMedium(string mediumPassword)
    {
        // Act
        var strength = _passwordService.ValidatePasswordStrength(mediumPassword);

        // Assert
        strength.Should().Be(ServiceLink.Domain.Enums.PasswordStrength.Medium);
    }

    [Theory]
    [InlineData("Password123!")]
    [InlineData("MySecureP@ss1")]
    [InlineData("Str0ng!Password")]
    public void ValidatePasswordStrength_WithStrongPasswords_ShouldReturnStrong(string strongPassword)
    {
        // Act
        var strength = _passwordService.ValidatePasswordStrength(strongPassword);

        // Assert
        strength.Should().Be(ServiceLink.Domain.Enums.PasswordStrength.Strong);
    }

    [Theory]
    [InlineData("MyVerySecureP@ssw0rd123!")]
    [InlineData("C0mpl3x!P@ssw0rd#2024")]
    [InlineData("Sup3r$tr0ng!P@ssw0rd&2024")]
    public void ValidatePasswordStrength_WithVeryStrongPasswords_ShouldReturnVeryStrong(string veryStrongPassword)
    {
        // Act
        var strength = _passwordService.ValidatePasswordStrength(veryStrongPassword);

        // Assert
        strength.Should().Be(ServiceLink.Domain.Enums.PasswordStrength.VeryStrong);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void ValidatePasswordStrength_WithInvalidPassword_ShouldThrowArgumentException(string invalidPassword)
    {
        // Act & Assert
        var act = () => _passwordService.ValidatePasswordStrength(invalidPassword);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void GenerateRandomPassword_ShouldReturnValidPassword()
    {
        // Act
        var password = _passwordService.GenerateRandomPassword();

        // Assert
        password.Should().NotBeNullOrEmpty();
        password.Length.Should().BeGreaterOrEqualTo(12);
        
        // Should contain at least one of each required character type
        password.Should().MatchRegex(@"[a-z]"); // lowercase
        password.Should().MatchRegex(@"[A-Z]"); // uppercase
        password.Should().MatchRegex(@"[0-9]"); // digit
        password.Should().MatchRegex(@"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"); // special character
    }

    [Theory]
    [InlineData(8)]
    [InlineData(12)]
    [InlineData(16)]
    [InlineData(20)]
    public void GenerateRandomPassword_WithSpecificLength_ShouldReturnPasswordOfCorrectLength(int length)
    {
        // Act
        var password = _passwordService.GenerateRandomPassword(length);

        // Assert
        password.Should().NotBeNullOrEmpty();
        password.Length.Should().Be(length);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(7)] // Below minimum
    public void GenerateRandomPassword_WithInvalidLength_ShouldThrowArgumentException(int invalidLength)
    {
        // Act & Assert
        var act = () => _passwordService.GenerateRandomPassword(invalidLength);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void GenerateRandomPassword_MultipleCalls_ShouldReturnDifferentPasswords()
    {
        // Act
        var password1 = _passwordService.GenerateRandomPassword();
        var password2 = _passwordService.GenerateRandomPassword();
        var password3 = _passwordService.GenerateRandomPassword();

        // Assert
        password1.Should().NotBe(password2);
        password2.Should().NotBe(password3);
        password1.Should().NotBe(password3);
    }

    [Fact]
    public void IsPasswordCompromised_WithCommonPassword_ShouldReturnTrue()
    {
        // Arrange - Common passwords that should be detected
        var commonPasswords = new[]
        {
            "password",
            "123456",
            "password123",
            "admin",
            "qwerty"
        };

        foreach (var commonPassword in commonPasswords)
        {
            // Act
            var result = _passwordService.IsPasswordCompromised(commonPassword);

            // Assert
            result.Should().BeTrue($"'{commonPassword}' should be detected as compromised");
        }
    }

    [Fact]
    public void IsPasswordCompromised_WithUniquePassword_ShouldReturnFalse()
    {
        // Arrange
        var uniquePassword = "MyVeryUniqueP@ssw0rd2024!";

        // Act
        var result = _passwordService.IsPasswordCompromised(uniquePassword);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void IsPasswordCompromised_WithInvalidPassword_ShouldThrowArgumentException(string invalidPassword)
    {
        // Act & Assert
        var act = () => _passwordService.IsPasswordCompromised(invalidPassword);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void HashPassword_Performance_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var password = "SecurePassword123!";
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var (hash, salt) = _passwordService.HashPassword(password);
        stopwatch.Stop();

        // Assert
        hash.Should().NotBeNullOrEmpty();
        salt.Should().NotBeNullOrEmpty();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
    }

    [Fact]
    public void VerifyPassword_Performance_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var password = "SecurePassword123!";
        var (hash, salt) = _passwordService.HashPassword(password);
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = _passwordService.VerifyPassword(password, hash, salt);
        stopwatch.Stop();

        // Assert
        result.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
    }
}
