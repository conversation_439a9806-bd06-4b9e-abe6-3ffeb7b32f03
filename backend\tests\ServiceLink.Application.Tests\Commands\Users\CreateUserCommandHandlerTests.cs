using Bogus;
using FluentAssertions;
using Moq;
using ServiceLink.Application.Commands;
using ServiceLink.Application.Handlers;
using ServiceLink.Application.Interfaces;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.Interfaces;
using ServiceLink.Domain.ValueObjects;

namespace ServiceLink.Application.Tests.Commands.Users;

/// <summary>
/// Tests unitaires pour CreateUserCommandHandler
/// </summary>
public class CreateUserCommandHandlerTests
{
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IPasswordService> _passwordServiceMock;
    private readonly Mock<IEmailService> _emailServiceMock;
    private readonly CreateUserCommandHandler _handler;
    private readonly Faker _faker;

    public CreateUserCommandHandlerTests()
    {
        _userRepositoryMock = new Mock<IUserRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _passwordServiceMock = new Mock<IPasswordService>();
        _emailServiceMock = new Mock<IEmailService>();
        _faker = new Faker("fr");

        _handler = new CreateUserCommandHandler(
            _userRepositoryMock.Object,
            _unitOfWorkMock.Object,
            _passwordServiceMock.Object,
            _emailServiceMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateUserAndReturnResponse()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            PhoneNumber = "+33123456789",
            Password = "SecurePassword123!",
            CreatedBy = Guid.NewGuid()
        };

        var passwordHash = "hashedPassword";
        var passwordSalt = "salt";

        _userRepositoryMock
            .Setup(x => x.ExistsByEmailAsync(It.IsAny<Email>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        _passwordServiceMock
            .Setup(x => x.HashPassword(command.Password))
            .Returns((passwordHash, passwordSalt));

        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _unitOfWorkMock
            .Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        _emailServiceMock
            .Setup(x => x.SendWelcomeEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().NotBeEmpty();
        result.Email.Should().Be(command.Email);
        result.FirstName.Should().Be(command.FirstName);
        result.LastName.Should().Be(command.LastName);
        result.FullName.Should().Be("John Doe");
        result.Role.Should().Be(command.Role);
        result.IsActive.Should().BeTrue();
        result.IsEmailConfirmed.Should().BeFalse();

        // Verify interactions
        _userRepositoryMock.Verify(x => x.ExistsByEmailAsync(It.IsAny<Email>(), It.IsAny<CancellationToken>()), Times.Once);
        _passwordServiceMock.Verify(x => x.HashPassword(command.Password), Times.Once);
        _userRepositoryMock.Verify(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        _emailServiceMock.Verify(x => x.SendWelcomeEmailAsync(command.Email, command.FirstName, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExistingEmail_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            Password = "SecurePassword123!"
        };

        _userRepositoryMock
            .Setup(x => x.ExistsByEmailAsync(It.IsAny<Email>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Un utilisateur avec cette adresse email existe déjà.");

        // Verify no user creation attempted
        _userRepositoryMock.Verify(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()), Times.Never);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithoutPassword_ShouldCreateUserWithoutPasswordHash()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            CreatedBy = Guid.NewGuid()
            // No password provided
        };

        _userRepositoryMock
            .Setup(x => x.ExistsByEmailAsync(It.IsAny<Email>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        _userRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _unitOfWorkMock
            .Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        _emailServiceMock
            .Setup(x => x.SendWelcomeEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().NotBeEmpty();

        // Verify password service was not called
        _passwordServiceMock.Verify(x => x.HashPassword(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WhenSaveChangesFails_ShouldThrowException()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client,
            Password = "SecurePassword123!"
        };

        _userRepositoryMock
            .Setup(x => x.ExistsByEmailAsync(It.IsAny<Email>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        _passwordServiceMock
            .Setup(x => x.HashPassword(command.Password))
            .Returns(("hash", "salt"));

        _unitOfWorkMock
            .Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(0); // Simulate save failure

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Échec de la création de l'utilisateur.");

        // Verify email was not sent
        _emailServiceMock.Verify(x => x.SendWelcomeEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public async Task Handle_WithInvalidEmail_ShouldThrowArgumentException(string invalidEmail)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = invalidEmail,
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("A")]
    [InlineData(null)]
    public async Task Handle_WithInvalidFirstName_ShouldThrowArgumentException(string invalidFirstName)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = invalidFirstName,
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("A")]
    [InlineData(null)]
    public async Task Handle_WithInvalidLastName_ShouldThrowArgumentException(string invalidLastName)
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = invalidLastName,
            Role = UserRole.Client
        };

        // Act & Assert
        var act = async () => await _handler.Handle(command, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentException>();
    }
}
