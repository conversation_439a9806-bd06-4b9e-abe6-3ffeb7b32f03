D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\appsettings.Development.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\appsettings.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.API.staticwebassets.endpoints.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.API.exe
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.API.deps.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.API.runtimeconfig.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.API.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.API.pdb
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\MediatR.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\MediatR.Contracts.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.AspNetCore.OpenApi.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.IdentityModel.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.IdentityModel.Logging.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.IdentityModel.Tokens.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.OpenApi.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Npgsql.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\System.IdentityModel.Tokens.Jwt.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.Application.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.Domain.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.Infrastructure.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.Application.pdb
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.Infrastructure.pdb
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\ServiceLink.Domain.pdb
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.csproj.AssemblyReference.cache
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.GeneratedMSBuildEditorConfig.editorconfig
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.AssemblyInfoInputs.cache
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.AssemblyInfo.cs
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.csproj.CoreCompileInputs.cache
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.MvcApplicationPartsAssemblyInfo.cs
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.MvcApplicationPartsAssemblyInfo.cache
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\scopedcss\bundle\ServiceLink.API.styles.css
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\staticwebassets.build.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\staticwebassets.build.json.cache
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\staticwebassets.development.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\staticwebassets.build.endpoints.json
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceL.878051A4.Up2Date
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\refint\ServiceLink.API.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.pdb
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.genruntimeconfig.cache
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ref\ServiceLink.API.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\FluentValidation.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\FluentValidation.DependencyInjectionExtensions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\BCrypt.Net-Next.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Binder.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Diagnostics.HealthChecks.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Microsoft.Extensions.Hosting.Abstractions.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Otp.NET.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\QRCoder.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\AspNetCoreRateLimit.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Newtonsoft.Json.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\MyProjects\ServiceLink\Version2\backend\src\ServiceLink.API\obj\Debug\net9.0\ServiceLink.API.sourcelink.json
