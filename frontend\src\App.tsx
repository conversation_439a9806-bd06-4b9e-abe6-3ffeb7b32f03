import React from 'react'
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import { LoginForm } from './components/auth/LoginForm'
import { ProfilePage } from './pages/ProfilePage'
import { AdminPage } from './pages/AdminPage'
import { Header } from './components/layout/Header'

function App() {
  const { isAuthenticated, isLoading, user } = useAuth()
  const navigate = useNavigate()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Layout pour les pages authentifiées
  const AuthenticatedLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <div className="min-h-screen bg-gray-50">
      <Header
        onProfileClick={() => navigate('/profile')}
        onAdminClick={() => navigate('/admin')}
      />
      {children}
    </div>
  )

  return (
    <Routes>
      <Route
        path="/login"
        element={
          isAuthenticated ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
              <LoginForm onSuccess={() => navigate('/dashboard')} />
            </div>
          )
        }
      />

      <Route
        path="/dashboard"
        element={
          isAuthenticated ? (
            <AuthenticatedLayout>
              <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                  <div className="bg-white shadow rounded-lg p-8">
                    <div className="text-center">
                      <h1 className="text-3xl font-bold text-gray-900 mb-4">
                        Bienvenue, {user?.firstName} {user?.lastName} !
                      </h1>
                      <p className="text-gray-600 mb-6">
                        Vous êtes connecté à ServiceLink
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                        <div className="bg-blue-50 p-6 rounded-lg">
                          <h3 className="text-lg font-medium text-blue-900 mb-2">
                            Profil
                          </h3>
                          <p className="text-blue-700 text-sm mb-4">
                            Gérez vos informations personnelles
                          </p>
                          <button
                            onClick={() => navigate('/profile')}
                            className="text-blue-600 hover:text-blue-500 font-medium text-sm"
                          >
                            Voir mon profil →
                          </button>
                        </div>

                        <div className="bg-green-50 p-6 rounded-lg">
                          <h3 className="text-lg font-medium text-green-900 mb-2">
                            Services
                          </h3>
                          <p className="text-green-700 text-sm mb-4">
                            Découvrez nos services disponibles
                          </p>
                          <button className="text-green-600 hover:text-green-500 font-medium text-sm">
                            Explorer →
                          </button>
                        </div>

                        {user?.role === 'Admin' && (
                          <div className="bg-red-50 p-6 rounded-lg">
                            <h3 className="text-lg font-medium text-red-900 mb-2">
                              Administration
                            </h3>
                            <p className="text-red-700 text-sm mb-4">
                              Gérez les utilisateurs et paramètres
                            </p>
                            <button
                              onClick={() => navigate('/admin')}
                              className="text-red-600 hover:text-red-500 font-medium text-sm"
                            >
                              Accéder →
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AuthenticatedLayout>
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />

      <Route
        path="/profile"
        element={
          isAuthenticated ? (
            <AuthenticatedLayout>
              <ProfilePage onBack={() => navigate('/dashboard')} />
            </AuthenticatedLayout>
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />

      <Route
        path="/admin"
        element={
          isAuthenticated ? (
            <AuthenticatedLayout>
              <AdminPage onBack={() => navigate('/dashboard')} />
            </AuthenticatedLayout>
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />

      <Route
        path="/"
        element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />}
      />
    </Routes>
  )
}

export default App
