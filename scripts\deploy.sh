#!/bin/bash

# Script de déploiement pour ServiceLink
# Usage: ./scripts/deploy.sh [environment] [version]
# Exemple: ./scripts/deploy.sh production v1.0.0

set -e  # Arrêter le script en cas d'erreur

# Configuration par défaut
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-""}
PROJECT_NAME="servicelink"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ $1${NC}"
}

# Vérifier les prérequis
check_prerequisites() {
    log "Vérification des prérequis..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker n'est pas installé"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    success "Prérequis vérifiés"
}

# Valider l'environnement
validate_environment() {
    log "Validation de l'environnement: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        development|staging|production)
            success "Environnement valide: $ENVIRONMENT"
            ;;
        *)
            error "Environnement invalide: $ENVIRONMENT. Utilisez: development, staging, ou production"
            exit 1
            ;;
    esac
}

# Charger les variables d'environnement
load_environment() {
    log "Chargement des variables d'environnement..."
    
    ENV_FILE=".env.${ENVIRONMENT}"
    if [ -f "$ENV_FILE" ]; then
        export $(cat $ENV_FILE | grep -v '^#' | xargs)
        success "Variables d'environnement chargées depuis $ENV_FILE"
    elif [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
        warning "Utilisation du fichier .env par défaut"
    else
        warning "Aucun fichier d'environnement trouvé"
    fi
}

# Construire les images Docker
build_images() {
    log "Construction des images Docker..."
    
    # Tagger les images avec la version
    export DOCKER_TAG=$VERSION
    
    if [ "$ENVIRONMENT" = "development" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml build
    else
        docker-compose build
    fi
    
    success "Images Docker construites avec le tag: $VERSION"
}

# Pousser les images vers le registry (pour staging/production)
push_images() {
    if [ "$ENVIRONMENT" != "development" ] && [ -n "$DOCKER_REGISTRY" ]; then
        log "Envoi des images vers le registry..."
        
        docker tag ${PROJECT_NAME}-api:$VERSION $DOCKER_REGISTRY/${PROJECT_NAME}-api:$VERSION
        docker push $DOCKER_REGISTRY/${PROJECT_NAME}-api:$VERSION
        
        success "Images envoyées vers le registry"
    fi
}

# Exécuter les migrations de base de données
run_migrations() {
    log "Exécution des migrations de base de données..."
    
    # Attendre que la base de données soit prête
    docker-compose exec -T postgres pg_isready -U ${POSTGRES_USER:-servicelink} -d ${POSTGRES_DB:-servicelink}
    
    # Exécuter les migrations
    docker-compose exec -T api dotnet ef database update --project src/ServiceLink.Infrastructure
    
    success "Migrations de base de données exécutées"
}

# Déployer l'application
deploy_application() {
    log "Déploiement de l'application..."
    
    case $ENVIRONMENT in
        development)
            docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
            ;;
        staging|production)
            docker-compose up -d
            ;;
    esac
    
    success "Application déployée en mode $ENVIRONMENT"
}

# Vérifier la santé de l'application
health_check() {
    log "Vérification de la santé de l'application..."
    
    # Attendre que l'API soit prête
    for i in {1..30}; do
        if curl -f http://localhost:8080/health &> /dev/null; then
            success "Application en bonne santé"
            return 0
        fi
        sleep 2
    done
    
    error "L'application ne répond pas après 60 secondes"
    return 1
}

# Afficher les logs en cas d'erreur
show_logs() {
    if [ $? -ne 0 ]; then
        error "Déploiement échoué. Affichage des logs..."
        docker-compose logs --tail=50
    fi
}

# Fonction principale
main() {
    log "Début du déploiement ServiceLink"
    log "Environnement: $ENVIRONMENT"
    log "Version: $VERSION"
    
    check_prerequisites
    validate_environment
    load_environment
    build_images
    push_images
    deploy_application
    
    # Attendre un peu avant de vérifier la santé
    sleep 10
    
    if health_check; then
        success "Déploiement réussi! 🎉"
        log "L'application est accessible sur: http://localhost:8080"
        
        if [ "$ENVIRONMENT" = "development" ]; then
            log "Interface d'administration de la base de données: http://localhost:5050"
            log "Interface Redis: http://localhost:8081"
            log "Interface email (MailHog): http://localhost:8025"
        fi
    else
        error "Déploiement échoué"
        show_logs
        exit 1
    fi
}

# Gestion des signaux pour un arrêt propre
trap show_logs ERR

# Exécuter le script principal
main
