{"version": 3, "sources": ["../../@hookform/resolvers/src/validateFieldsNatively.ts", "../../@hookform/resolvers/src/toNestErrors.ts", "../../zod/v4/core/core.js", "../../zod/v4/core/util.js", "../../zod/v4/core/errors.js", "../../zod/v4/core/parse.js", "../../zod/v4/core/regexes.js", "../../zod/v4/core/checks.js", "../../zod/v4/core/doc.js", "../../zod/v4/core/versions.js", "../../zod/v4/core/schemas.js", "../../zod/v4/core/registries.js", "../../@hookform/resolvers/zod/src/zod.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field && field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => {\n  const path = escapeBrackets(name);\n  return names.some((n) => escapeBrackets(n).match(`^${path}\\\\.\\\\d+`));\n};\n\n/**\n * Escapes special characters in a string to be used in a regex pattern.\n * it removes the brackets from the string to match the `set` method.\n *\n * @param input - The input string to escape.\n * @returns The escaped string.\n */\nfunction escapeBrackets(input: string): string {\n  return input.replace(/\\]|\\[/g, '');\n}\n", "export /*@__NO_SIDE_EFFECTS__*/ function $constructor(name, initializer, params) {\n    function init(inst, def) {\n        var _a;\n        Object.defineProperty(inst, \"_zod\", {\n            value: inst._zod ?? {},\n            enumerable: false,\n        });\n        (_a = inst._zod).traits ?? (_a.traits = new Set());\n        inst._zod.traits.add(name);\n        initializer(inst, def);\n        // support prototype modifications\n        for (const k in _.prototype) {\n            if (!(k in inst))\n                Object.defineProperty(inst, k, { value: _.prototype[k].bind(inst) });\n        }\n        inst._zod.constr = _;\n        inst._zod.def = def;\n    }\n    // doesn't work if <PERSON><PERSON> has a constructor with arguments\n    const Parent = params?.Parent ?? Object;\n    class Definition extends Parent {\n    }\n    Object.defineProperty(Definition, \"name\", { value: name });\n    function _(def) {\n        var _a;\n        const inst = params?.Parent ? new Definition() : this;\n        init(inst, def);\n        (_a = inst._zod).deferred ?? (_a.deferred = []);\n        for (const fn of inst._zod.deferred) {\n            fn();\n        }\n        return inst;\n    }\n    Object.defineProperty(_, \"init\", { value: init });\n    Object.defineProperty(_, Symbol.hasInstance, {\n        value: (inst) => {\n            if (params?.Parent && inst instanceof params.Parent)\n                return true;\n            return inst?._zod?.traits?.has(name);\n        },\n    });\n    Object.defineProperty(_, \"name\", { value: name });\n    return _;\n}\n//////////////////////////////   UTILITIES   ///////////////////////////////////////\nexport const $brand = Symbol(\"zod_brand\");\nexport class $ZodAsyncError extends Error {\n    constructor() {\n        super(`Encountered Promise during synchronous parse. Use .parseAsync() instead.`);\n    }\n}\nexport const globalConfig = {};\nexport function config(newConfig) {\n    if (newConfig)\n        Object.assign(globalConfig, newConfig);\n    return globalConfig;\n}\n", "// functions\nexport function assertEqual(val) {\n    return val;\n}\nexport function assertNotEqual(val) {\n    return val;\n}\nexport function assertIs(_arg) { }\nexport function assertNever(_x) {\n    throw new Error();\n}\nexport function assert(_) { }\nexport function getEnumValues(entries) {\n    const numericValues = Object.values(entries).filter((v) => typeof v === \"number\");\n    const values = Object.entries(entries)\n        .filter(([k, _]) => numericValues.indexOf(+k) === -1)\n        .map(([_, v]) => v);\n    return values;\n}\nexport function joinValues(array, separator = \"|\") {\n    return array.map((val) => stringifyPrimitive(val)).join(separator);\n}\nexport function jsonStringifyReplacer(_, value) {\n    if (typeof value === \"bigint\")\n        return value.toString();\n    return value;\n}\nexport function cached(getter) {\n    const set = false;\n    return {\n        get value() {\n            if (!set) {\n                const value = getter();\n                Object.defineProperty(this, \"value\", { value });\n                return value;\n            }\n            throw new Error(\"cached value already set\");\n        },\n    };\n}\nexport function nullish(input) {\n    return input === null || input === undefined;\n}\nexport function cleanRegex(source) {\n    const start = source.startsWith(\"^\") ? 1 : 0;\n    const end = source.endsWith(\"$\") ? source.length - 1 : source.length;\n    return source.slice(start, end);\n}\nexport function floatSafeRemainder(val, step) {\n    const valDecCount = (val.toString().split(\".\")[1] || \"\").length;\n    const stepDecCount = (step.toString().split(\".\")[1] || \"\").length;\n    const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;\n    const valInt = Number.parseInt(val.toFixed(decCount).replace(\".\", \"\"));\n    const stepInt = Number.parseInt(step.toFixed(decCount).replace(\".\", \"\"));\n    return (valInt % stepInt) / 10 ** decCount;\n}\nexport function defineLazy(object, key, getter) {\n    const set = false;\n    Object.defineProperty(object, key, {\n        get() {\n            if (!set) {\n                const value = getter();\n                object[key] = value;\n                return value;\n            }\n            throw new Error(\"cached value already set\");\n        },\n        set(v) {\n            Object.defineProperty(object, key, {\n                value: v,\n                // configurable: true,\n            });\n            // object[key] = v;\n        },\n        configurable: true,\n    });\n}\nexport function assignProp(target, prop, value) {\n    Object.defineProperty(target, prop, {\n        value,\n        writable: true,\n        enumerable: true,\n        configurable: true,\n    });\n}\nexport function getElementAtPath(obj, path) {\n    if (!path)\n        return obj;\n    return path.reduce((acc, key) => acc?.[key], obj);\n}\nexport function promiseAllObject(promisesObj) {\n    const keys = Object.keys(promisesObj);\n    const promises = keys.map((key) => promisesObj[key]);\n    return Promise.all(promises).then((results) => {\n        const resolvedObj = {};\n        for (let i = 0; i < keys.length; i++) {\n            resolvedObj[keys[i]] = results[i];\n        }\n        return resolvedObj;\n    });\n}\nexport function randomString(length = 10) {\n    const chars = \"abcdefghijklmnopqrstuvwxyz\";\n    let str = \"\";\n    for (let i = 0; i < length; i++) {\n        str += chars[Math.floor(Math.random() * chars.length)];\n    }\n    return str;\n}\nexport function esc(str) {\n    return JSON.stringify(str);\n}\nexport const captureStackTrace = Error.captureStackTrace\n    ? Error.captureStackTrace\n    : (..._args) => { };\nexport function isObject(data) {\n    return typeof data === \"object\" && data !== null && !Array.isArray(data);\n}\nexport const allowsEval = cached(() => {\n    if (typeof navigator !== \"undefined\" && navigator?.userAgent?.includes(\"Cloudflare\")) {\n        return false;\n    }\n    try {\n        const F = Function;\n        new F(\"\");\n        return true;\n    }\n    catch (_) {\n        return false;\n    }\n});\nexport function isPlainObject(o) {\n    if (isObject(o) === false)\n        return false;\n    // modified constructor\n    const ctor = o.constructor;\n    if (ctor === undefined)\n        return true;\n    // modified prototype\n    const prot = ctor.prototype;\n    if (isObject(prot) === false)\n        return false;\n    // ctor doesn't have static `isPrototypeOf`\n    if (Object.prototype.hasOwnProperty.call(prot, \"isPrototypeOf\") === false) {\n        return false;\n    }\n    return true;\n}\nexport function numKeys(data) {\n    let keyCount = 0;\n    for (const key in data) {\n        if (Object.prototype.hasOwnProperty.call(data, key)) {\n            keyCount++;\n        }\n    }\n    return keyCount;\n}\nexport const getParsedType = (data) => {\n    const t = typeof data;\n    switch (t) {\n        case \"undefined\":\n            return \"undefined\";\n        case \"string\":\n            return \"string\";\n        case \"number\":\n            return Number.isNaN(data) ? \"nan\" : \"number\";\n        case \"boolean\":\n            return \"boolean\";\n        case \"function\":\n            return \"function\";\n        case \"bigint\":\n            return \"bigint\";\n        case \"symbol\":\n            return \"symbol\";\n        case \"object\":\n            if (Array.isArray(data)) {\n                return \"array\";\n            }\n            if (data === null) {\n                return \"null\";\n            }\n            if (data.then && typeof data.then === \"function\" && data.catch && typeof data.catch === \"function\") {\n                return \"promise\";\n            }\n            if (typeof Map !== \"undefined\" && data instanceof Map) {\n                return \"map\";\n            }\n            if (typeof Set !== \"undefined\" && data instanceof Set) {\n                return \"set\";\n            }\n            if (typeof Date !== \"undefined\" && data instanceof Date) {\n                return \"date\";\n            }\n            if (typeof File !== \"undefined\" && data instanceof File) {\n                return \"file\";\n            }\n            return \"object\";\n        default:\n            throw new Error(`Unknown data type: ${t}`);\n    }\n};\nexport const propertyKeyTypes = new Set([\"string\", \"number\", \"symbol\"]);\nexport const primitiveTypes = new Set([\"string\", \"number\", \"bigint\", \"boolean\", \"symbol\", \"undefined\"]);\nexport function escapeRegex(str) {\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\n// zod-specific utils\nexport function clone(inst, def, params) {\n    const cl = new inst._zod.constr(def ?? inst._zod.def);\n    if (!def || params?.parent)\n        cl._zod.parent = inst;\n    return cl;\n}\nexport function normalizeParams(_params) {\n    const params = _params;\n    if (!params)\n        return {};\n    if (typeof params === \"string\")\n        return { error: () => params };\n    if (params?.message !== undefined) {\n        if (params?.error !== undefined)\n            throw new Error(\"Cannot specify both `message` and `error` params\");\n        params.error = params.message;\n    }\n    delete params.message;\n    if (typeof params.error === \"string\")\n        return { ...params, error: () => params.error };\n    return params;\n}\nexport function createTransparentProxy(getter) {\n    let target;\n    return new Proxy({}, {\n        get(_, prop, receiver) {\n            target ?? (target = getter());\n            return Reflect.get(target, prop, receiver);\n        },\n        set(_, prop, value, receiver) {\n            target ?? (target = getter());\n            return Reflect.set(target, prop, value, receiver);\n        },\n        has(_, prop) {\n            target ?? (target = getter());\n            return Reflect.has(target, prop);\n        },\n        deleteProperty(_, prop) {\n            target ?? (target = getter());\n            return Reflect.deleteProperty(target, prop);\n        },\n        ownKeys(_) {\n            target ?? (target = getter());\n            return Reflect.ownKeys(target);\n        },\n        getOwnPropertyDescriptor(_, prop) {\n            target ?? (target = getter());\n            return Reflect.getOwnPropertyDescriptor(target, prop);\n        },\n        defineProperty(_, prop, descriptor) {\n            target ?? (target = getter());\n            return Reflect.defineProperty(target, prop, descriptor);\n        },\n    });\n}\nexport function stringifyPrimitive(value) {\n    if (typeof value === \"bigint\")\n        return value.toString() + \"n\";\n    if (typeof value === \"string\")\n        return `\"${value}\"`;\n    return `${value}`;\n}\nexport function optionalKeys(shape) {\n    return Object.keys(shape).filter((k) => {\n        return shape[k]._zod.optin === \"optional\" && shape[k]._zod.optout === \"optional\";\n    });\n}\nexport const NUMBER_FORMAT_RANGES = {\n    safeint: [Number.MIN_SAFE_INTEGER, Number.MAX_SAFE_INTEGER],\n    int32: [-2147483648, 2147483647],\n    uint32: [0, 4294967295],\n    float32: [-3.4028234663852886e38, 3.4028234663852886e38],\n    float64: [-Number.MAX_VALUE, Number.MAX_VALUE],\n};\nexport const BIGINT_FORMAT_RANGES = {\n    int64: [/* @__PURE__*/ BigInt(\"-9223372036854775808\"), /* @__PURE__*/ BigInt(\"9223372036854775807\")],\n    uint64: [/* @__PURE__*/ BigInt(0), /* @__PURE__*/ BigInt(\"18446744073709551615\")],\n};\nexport function pick(schema, mask) {\n    const newShape = {};\n    const currDef = schema._zod.def; //.shape;\n    for (const key in mask) {\n        if (!(key in currDef.shape)) {\n            throw new Error(`Unrecognized key: \"${key}\"`);\n        }\n        if (!mask[key])\n            continue;\n        // pick key\n        newShape[key] = currDef.shape[key];\n    }\n    return clone(schema, {\n        ...schema._zod.def,\n        shape: newShape,\n        checks: [],\n    });\n}\nexport function omit(schema, mask) {\n    const newShape = { ...schema._zod.def.shape };\n    const currDef = schema._zod.def; //.shape;\n    for (const key in mask) {\n        if (!(key in currDef.shape)) {\n            throw new Error(`Unrecognized key: \"${key}\"`);\n        }\n        if (!mask[key])\n            continue;\n        delete newShape[key];\n    }\n    return clone(schema, {\n        ...schema._zod.def,\n        shape: newShape,\n        checks: [],\n    });\n}\nexport function extend(schema, shape) {\n    const def = {\n        ...schema._zod.def,\n        get shape() {\n            const _shape = { ...schema._zod.def.shape, ...shape };\n            assignProp(this, \"shape\", _shape); // self-caching\n            return _shape;\n        },\n        checks: [], // delete existing checks\n    };\n    return clone(schema, def);\n}\nexport function merge(a, b) {\n    return clone(a, {\n        ...a._zod.def,\n        get shape() {\n            const _shape = { ...a._zod.def.shape, ...b._zod.def.shape };\n            assignProp(this, \"shape\", _shape); // self-caching\n            return _shape;\n        },\n        catchall: b._zod.def.catchall,\n        checks: [], // delete existing checks\n    });\n}\nexport function partial(Class, schema, mask) {\n    const oldShape = schema._zod.def.shape;\n    const shape = { ...oldShape };\n    if (mask) {\n        for (const key in mask) {\n            if (!(key in oldShape)) {\n                throw new Error(`Unrecognized key: \"${key}\"`);\n            }\n            if (!mask[key])\n                continue;\n            shape[key] = Class\n                ? new Class({\n                    type: \"optional\",\n                    innerType: oldShape[key],\n                })\n                : oldShape[key];\n        }\n    }\n    else {\n        for (const key in oldShape) {\n            shape[key] = Class\n                ? new Class({\n                    type: \"optional\",\n                    innerType: oldShape[key],\n                })\n                : oldShape[key];\n        }\n    }\n    return clone(schema, {\n        ...schema._zod.def,\n        shape,\n        checks: [],\n    });\n}\nexport function required(Class, schema, mask) {\n    const oldShape = schema._zod.def.shape;\n    const shape = { ...oldShape };\n    if (mask) {\n        for (const key in mask) {\n            if (!(key in shape)) {\n                throw new Error(`Unrecognized key: \"${key}\"`);\n            }\n            if (!mask[key])\n                continue;\n            // overwrite with non-optional\n            shape[key] = new Class({\n                type: \"nonoptional\",\n                innerType: oldShape[key],\n            });\n        }\n    }\n    else {\n        for (const key in oldShape) {\n            // overwrite with non-optional\n            shape[key] = new Class({\n                type: \"nonoptional\",\n                innerType: oldShape[key],\n            });\n        }\n    }\n    return clone(schema, {\n        ...schema._zod.def,\n        shape,\n        // optional: [],\n        checks: [],\n    });\n}\nexport function aborted(x, startIndex = 0) {\n    for (let i = startIndex; i < x.issues.length; i++) {\n        if (x.issues[i]?.continue !== true)\n            return true;\n    }\n    return false;\n}\nexport function prefixIssues(path, issues) {\n    return issues.map((iss) => {\n        var _a;\n        (_a = iss).path ?? (_a.path = []);\n        iss.path.unshift(path);\n        return iss;\n    });\n}\nexport function unwrapMessage(message) {\n    return typeof message === \"string\" ? message : message?.message;\n}\nexport function finalizeIssue(iss, ctx, config) {\n    const full = { ...iss, path: iss.path ?? [] };\n    // for backwards compatibility\n    if (!iss.message) {\n        const message = unwrapMessage(iss.inst?._zod.def?.error?.(iss)) ??\n            unwrapMessage(ctx?.error?.(iss)) ??\n            unwrapMessage(config.customError?.(iss)) ??\n            unwrapMessage(config.localeError?.(iss)) ??\n            \"Invalid input\";\n        full.message = message;\n    }\n    // delete (full as any).def;\n    delete full.inst;\n    delete full.continue;\n    if (!ctx?.reportInput) {\n        delete full.input;\n    }\n    return full;\n}\nexport function getSizableOrigin(input) {\n    if (input instanceof Set)\n        return \"set\";\n    if (input instanceof Map)\n        return \"map\";\n    if (input instanceof File)\n        return \"file\";\n    return \"unknown\";\n}\nexport function getLengthableOrigin(input) {\n    if (Array.isArray(input))\n        return \"array\";\n    if (typeof input === \"string\")\n        return \"string\";\n    return \"unknown\";\n}\nexport function issue(...args) {\n    const [iss, input, inst] = args;\n    if (typeof iss === \"string\") {\n        return {\n            message: iss,\n            code: \"custom\",\n            input,\n            inst,\n        };\n    }\n    return { ...iss };\n}\nexport function cleanEnum(obj) {\n    return Object.entries(obj)\n        .filter(([k, _]) => {\n        // return true if NaN, meaning it's not a number, thus a string key\n        return Number.isNaN(Number.parseInt(k, 10));\n    })\n        .map((el) => el[1]);\n}\n// instanceof\nexport class Class {\n    constructor(..._args) { }\n}\n", "import { $constructor } from \"./core.js\";\nimport * as util from \"./util.js\";\nconst initializer = (inst, def) => {\n    inst.name = \"$ZodError\";\n    Object.defineProperty(inst, \"_zod\", {\n        value: inst._zod,\n        enumerable: false,\n    });\n    Object.defineProperty(inst, \"issues\", {\n        value: def,\n        enumerable: false,\n    });\n    Object.defineProperty(inst, \"message\", {\n        get() {\n            return JSON.stringify(def, util.jsonStringifyReplacer, 2);\n        },\n        enumerable: true,\n        // configurable: false,\n    });\n};\nexport const $ZodError = $constructor(\"$ZodError\", initializer);\nexport const $ZodRealError = $constructor(\"$ZodError\", initializer, { Parent: Error });\nexport function flattenError(error, mapper = (issue) => issue.message) {\n    const fieldErrors = {};\n    const formErrors = [];\n    for (const sub of error.issues) {\n        if (sub.path.length > 0) {\n            fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];\n            fieldErrors[sub.path[0]].push(mapper(sub));\n        }\n        else {\n            formErrors.push(mapper(sub));\n        }\n    }\n    return { formErrors, fieldErrors };\n}\nexport function formatError(error, _mapper) {\n    const mapper = _mapper ||\n        function (issue) {\n            return issue.message;\n        };\n    const fieldErrors = { _errors: [] };\n    const processError = (error) => {\n        for (const issue of error.issues) {\n            if (issue.code === \"invalid_union\" && issue.errors.length) {\n                issue.errors.map((issues) => processError({ issues }));\n            }\n            else if (issue.code === \"invalid_key\") {\n                processError({ issues: issue.issues });\n            }\n            else if (issue.code === \"invalid_element\") {\n                processError({ issues: issue.issues });\n            }\n            else if (issue.path.length === 0) {\n                fieldErrors._errors.push(mapper(issue));\n            }\n            else {\n                let curr = fieldErrors;\n                let i = 0;\n                while (i < issue.path.length) {\n                    const el = issue.path[i];\n                    const terminal = i === issue.path.length - 1;\n                    if (!terminal) {\n                        curr[el] = curr[el] || { _errors: [] };\n                    }\n                    else {\n                        curr[el] = curr[el] || { _errors: [] };\n                        curr[el]._errors.push(mapper(issue));\n                    }\n                    curr = curr[el];\n                    i++;\n                }\n            }\n        }\n    };\n    processError(error);\n    return fieldErrors;\n}\nexport function treeifyError(error, _mapper) {\n    const mapper = _mapper ||\n        function (issue) {\n            return issue.message;\n        };\n    const result = { errors: [] };\n    const processError = (error, path = []) => {\n        var _a, _b;\n        for (const issue of error.issues) {\n            if (issue.code === \"invalid_union\" && issue.errors.length) {\n                // regular union error\n                issue.errors.map((issues) => processError({ issues }, issue.path));\n            }\n            else if (issue.code === \"invalid_key\") {\n                processError({ issues: issue.issues }, issue.path);\n            }\n            else if (issue.code === \"invalid_element\") {\n                processError({ issues: issue.issues }, issue.path);\n            }\n            else {\n                const fullpath = [...path, ...issue.path];\n                if (fullpath.length === 0) {\n                    result.errors.push(mapper(issue));\n                    continue;\n                }\n                let curr = result;\n                let i = 0;\n                while (i < fullpath.length) {\n                    const el = fullpath[i];\n                    const terminal = i === fullpath.length - 1;\n                    if (typeof el === \"string\") {\n                        curr.properties ?? (curr.properties = {});\n                        (_a = curr.properties)[el] ?? (_a[el] = { errors: [] });\n                        curr = curr.properties[el];\n                    }\n                    else {\n                        curr.items ?? (curr.items = []);\n                        (_b = curr.items)[el] ?? (_b[el] = { errors: [] });\n                        curr = curr.items[el];\n                    }\n                    if (terminal) {\n                        curr.errors.push(mapper(issue));\n                    }\n                    i++;\n                }\n            }\n        }\n    };\n    processError(error);\n    return result;\n}\n/** Format a ZodError as a human-readable string in the following form.\n *\n * From\n *\n * ```ts\n * ZodError {\n *   issues: [\n *     {\n *       expected: 'string',\n *       code: 'invalid_type',\n *       path: [ 'username' ],\n *       message: 'Invalid input: expected string'\n *     },\n *     {\n *       expected: 'number',\n *       code: 'invalid_type',\n *       path: [ 'favoriteNumbers', 1 ],\n *       message: 'Invalid input: expected number'\n *     }\n *   ];\n * }\n * ```\n *\n * to\n *\n * ```\n * username\n *   ✖ Expected number, received string at \"username\n * favoriteNumbers[0]\n *   ✖ Invalid input: expected number\n * ```\n */\nexport function toDotPath(path) {\n    const segs = [];\n    for (const seg of path) {\n        if (typeof seg === \"number\")\n            segs.push(`[${seg}]`);\n        else if (typeof seg === \"symbol\")\n            segs.push(`[${JSON.stringify(String(seg))}]`);\n        else if (/[^\\w$]/.test(seg))\n            segs.push(`[${JSON.stringify(seg)}]`);\n        else {\n            if (segs.length)\n                segs.push(\".\");\n            segs.push(seg);\n        }\n    }\n    return segs.join(\"\");\n}\nexport function prettifyError(error) {\n    const lines = [];\n    // sort by path length\n    const issues = [...error.issues].sort((a, b) => a.path.length - b.path.length);\n    // Process each issue\n    for (const issue of issues) {\n        lines.push(`✖ ${issue.message}`);\n        if (issue.path?.length)\n            lines.push(`  → at ${toDotPath(issue.path)}`);\n    }\n    // Convert Map to formatted string\n    return lines.join(\"\\n\");\n}\n", "import * as core from \"./core.js\";\nimport * as errors from \"./errors.js\";\nimport * as util from \"./util.js\";\nexport const _parse = (_Err) => (schema, value, _ctx, _params) => {\n    const ctx = _ctx ? Object.assign(_ctx, { async: false }) : { async: false };\n    const result = schema._zod.run({ value, issues: [] }, ctx);\n    if (result instanceof Promise) {\n        throw new core.$ZodAsyncError();\n    }\n    if (result.issues.length) {\n        const e = new (_params?.Err ?? _Err)(result.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config())));\n        util.captureStackTrace(e, _params?.callee);\n        throw e;\n    }\n    return result.value;\n};\nexport const parse = /* @__PURE__*/ _parse(errors.$ZodRealError);\nexport const _parseAsync = (_Err) => async (schema, value, _ctx, params) => {\n    const ctx = _ctx ? Object.assign(_ctx, { async: true }) : { async: true };\n    let result = schema._zod.run({ value, issues: [] }, ctx);\n    if (result instanceof Promise)\n        result = await result;\n    if (result.issues.length) {\n        const e = new (params?.Err ?? _Err)(result.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config())));\n        util.captureStackTrace(e, params?.callee);\n        throw e;\n    }\n    return result.value;\n};\nexport const parseAsync = /* @__PURE__*/ _parseAsync(errors.$ZodRealError);\nexport const _safeParse = (_Err) => (schema, value, _ctx) => {\n    const ctx = _ctx ? { ..._ctx, async: false } : { async: false };\n    const result = schema._zod.run({ value, issues: [] }, ctx);\n    if (result instanceof Promise) {\n        throw new core.$ZodAsyncError();\n    }\n    return result.issues.length\n        ? {\n            success: false,\n            error: new (_Err ?? errors.$ZodError)(result.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config()))),\n        }\n        : { success: true, data: result.value };\n};\nexport const safeParse = /* @__PURE__*/ _safeParse(errors.$ZodRealError);\nexport const _safeParseAsync = (_Err) => async (schema, value, _ctx) => {\n    const ctx = _ctx ? Object.assign(_ctx, { async: true }) : { async: true };\n    let result = schema._zod.run({ value, issues: [] }, ctx);\n    if (result instanceof Promise)\n        result = await result;\n    return result.issues.length\n        ? {\n            success: false,\n            error: new _Err(result.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config()))),\n        }\n        : { success: true, data: result.value };\n};\nexport const safeParseAsync = /* @__PURE__*/ _safeParseAsync(errors.$ZodRealError);\n", "export const cuid = /^[cC][^\\s-]{8,}$/;\nexport const cuid2 = /^[0-9a-z]+$/;\nexport const ulid = /^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/;\nexport const xid = /^[0-9a-vA-V]{20}$/;\nexport const ksuid = /^[A-Za-z0-9]{27}$/;\nexport const nanoid = /^[a-zA-Z0-9_-]{21}$/;\n/** ISO 8601-1 duration regex. Does not support the 8601-2 extensions like negative durations or fractional/negative components. */\nexport const duration = /^P(?:(\\d+W)|(?!.*W)(?=\\d|T\\d)(\\d+Y)?(\\d+M)?(\\d+D)?(T(?=\\d)(\\d+H)?(\\d+M)?(\\d+([.,]\\d+)?S)?)?)$/;\n/** Implements ISO 8601-2 extensions like explicit +- prefixes, mixing weeks with other units, and fractional/negative components. */\nexport const extendedDuration = /^[-+]?P(?!$)(?:(?:[-+]?\\d+Y)|(?:[-+]?\\d+[.,]\\d+Y$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:(?:[-+]?\\d+W)|(?:[-+]?\\d+[.,]\\d+W$))?(?:(?:[-+]?\\d+D)|(?:[-+]?\\d+[.,]\\d+D$))?(?:T(?=[\\d+-])(?:(?:[-+]?\\d+H)|(?:[-+]?\\d+[.,]\\d+H$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:[-+]?\\d+(?:[.,]\\d+)?S)?)??$/;\n/** A regex for any UUID-like identifier: 8-4-4-4-12 hex pattern */\nexport const guid = /^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/;\n/** Returns a regex for validating an RFC 4122 UUID.\n *\n * @param version Optionally specify a version 1-8. If no version is specified, all versions are supported. */\nexport const uuid = (version) => {\n    if (!version)\n        return /^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/;\n    return new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${version}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`);\n};\nexport const uuid4 = /*@__PURE__*/ uuid(4);\nexport const uuid6 = /*@__PURE__*/ uuid(6);\nexport const uuid7 = /*@__PURE__*/ uuid(7);\n/** Practical email validation */\nexport const email = /^(?!\\.)(?!.*\\.\\.)([A-Za-z0-9_'+\\-\\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\\-]*\\.)+[A-Za-z]{2,}$/;\n/** Equivalent to the HTML5 input[type=email] validation implemented by browsers. Source: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/email */\nexport const html5Email = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n/** The classic emailregex.com regex for RFC 5322-compliant emails */\nexport const rfc5322Email = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n/** A loose regex that allows Unicode characters, enforces length limits, and that's about it. */\nexport const unicodeEmail = /^[^\\s@\"]{1,64}@[^\\s@]{1,255}$/u;\nexport const browserEmail = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n// from https://thekevinscott.com/emojis-in-javascript/#writing-a-regular-expression\nexport const _emoji = `^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$`;\nexport function emoji() {\n    return new RegExp(_emoji, \"u\");\n}\nexport const ipv4 = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/;\nexport const ipv6 = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/;\nexport const cidrv4 = /^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/([0-9]|[1-2][0-9]|3[0-2])$/;\nexport const cidrv6 = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/;\n// https://stackoverflow.com/questions/7860392/determine-if-string-is-in-base64-using-javascript\nexport const base64 = /^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/;\nexport const base64url = /^[A-Za-z0-9_-]*$/;\n// based on https://stackoverflow.com/questions/106179/regular-expression-to-match-dns-hostname-or-ip-address\n// export const hostname: RegExp =\n//   /^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)+([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9])$/;\nexport const hostname = /^([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+$/;\nexport const domain = /^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}$/;\n// https://blog.stevenlevithan.com/archives/validate-phone-number#r4-3 (regex sans spaces)\nexport const e164 = /^\\+(?:[0-9]){6,14}[0-9]$/;\n// const dateSource = `((\\\\d\\\\d[2468][048]|\\\\d\\\\d[13579][26]|\\\\d\\\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\\\d|30)|(02)-(0[1-9]|1\\\\d|2[0-8])))`;\nconst dateSource = `(?:(?:\\\\d\\\\d[2468][048]|\\\\d\\\\d[13579][26]|\\\\d\\\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\\\d|30)|(?:02)-(?:0[1-9]|1\\\\d|2[0-8])))`;\nexport const date = /*@__PURE__*/ new RegExp(`^${dateSource}$`);\nfunction timeSource(args) {\n    const hhmm = `(?:[01]\\\\d|2[0-3]):[0-5]\\\\d`;\n    const regex = typeof args.precision === \"number\"\n        ? args.precision === -1\n            ? `${hhmm}`\n            : args.precision === 0\n                ? `${hhmm}:[0-5]\\\\d`\n                : `${hhmm}:[0-5]\\\\d\\\\.\\\\d{${args.precision}}`\n        : `${hhmm}(?::[0-5]\\\\d(?:\\\\.\\\\d+)?)?`;\n    return regex;\n}\nexport function time(args) {\n    return new RegExp(`^${timeSource(args)}$`);\n}\n// Adapted from https://stackoverflow.com/a/3143231\nexport function datetime(args) {\n    const time = timeSource({ precision: args.precision });\n    const opts = [\"Z\"];\n    if (args.local)\n        opts.push(\"\");\n    if (args.offset)\n        opts.push(`([+-]\\\\d{2}:\\\\d{2})`);\n    const timeRegex = `${time}(?:${opts.join(\"|\")})`;\n    return new RegExp(`^${dateSource}T(?:${timeRegex})$`);\n}\nexport const string = (params) => {\n    const regex = params ? `[\\\\s\\\\S]{${params?.minimum ?? 0},${params?.maximum ?? \"\"}}` : `[\\\\s\\\\S]*`;\n    return new RegExp(`^${regex}$`);\n};\nexport const bigint = /^\\d+n?$/;\nexport const integer = /^\\d+$/;\nexport const number = /^-?\\d+(?:\\.\\d+)?/i;\nexport const boolean = /true|false/i;\nconst _null = /null/i;\nexport { _null as null };\nconst _undefined = /undefined/i;\nexport { _undefined as undefined };\n// regex for string with no uppercase letters\nexport const lowercase = /^[^A-Z]*$/;\n// regex for string with no lowercase letters\nexport const uppercase = /^[^a-z]*$/;\n", "// import { $ZodType } from \"./schemas.js\";\nimport * as core from \"./core.js\";\nimport * as regexes from \"./regexes.js\";\nimport * as util from \"./util.js\";\nexport const $ZodCheck = /*@__PURE__*/ core.$constructor(\"$ZodCheck\", (inst, def) => {\n    var _a;\n    inst._zod ?? (inst._zod = {});\n    inst._zod.def = def;\n    (_a = inst._zod).onattach ?? (_a.onattach = []);\n});\nconst numericOriginMap = {\n    number: \"number\",\n    bigint: \"bigint\",\n    object: \"date\",\n};\nexport const $ZodCheckLessThan = /*@__PURE__*/ core.$constructor(\"$ZodCheckLessThan\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    const origin = numericOriginMap[typeof def.value];\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        const curr = (def.inclusive ? bag.maximum : bag.exclusiveMaximum) ?? Number.POSITIVE_INFINITY;\n        if (def.value < curr) {\n            if (def.inclusive)\n                bag.maximum = def.value;\n            else\n                bag.exclusiveMaximum = def.value;\n        }\n    });\n    inst._zod.check = (payload) => {\n        if (def.inclusive ? payload.value <= def.value : payload.value < def.value) {\n            return;\n        }\n        payload.issues.push({\n            origin,\n            code: \"too_big\",\n            maximum: def.value,\n            input: payload.value,\n            inclusive: def.inclusive,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckGreaterThan = /*@__PURE__*/ core.$constructor(\"$ZodCheckGreaterThan\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    const origin = numericOriginMap[typeof def.value];\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        const curr = (def.inclusive ? bag.minimum : bag.exclusiveMinimum) ?? Number.NEGATIVE_INFINITY;\n        if (def.value > curr) {\n            if (def.inclusive)\n                bag.minimum = def.value;\n            else\n                bag.exclusiveMinimum = def.value;\n        }\n    });\n    inst._zod.check = (payload) => {\n        if (def.inclusive ? payload.value >= def.value : payload.value > def.value) {\n            return;\n        }\n        payload.issues.push({\n            origin: origin,\n            code: \"too_small\",\n            minimum: def.value,\n            input: payload.value,\n            inclusive: def.inclusive,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckMultipleOf = \n/*@__PURE__*/ core.$constructor(\"$ZodCheckMultipleOf\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.onattach.push((inst) => {\n        var _a;\n        (_a = inst._zod.bag).multipleOf ?? (_a.multipleOf = def.value);\n    });\n    inst._zod.check = (payload) => {\n        if (typeof payload.value !== typeof def.value)\n            throw new Error(\"Cannot mix number and bigint in multiple_of check.\");\n        const isMultiple = typeof payload.value === \"bigint\"\n            ? payload.value % def.value === BigInt(0)\n            : util.floatSafeRemainder(payload.value, def.value) === 0;\n        if (isMultiple)\n            return;\n        payload.issues.push({\n            origin: typeof payload.value,\n            code: \"not_multiple_of\",\n            divisor: def.value,\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckNumberFormat = /*@__PURE__*/ core.$constructor(\"$ZodCheckNumberFormat\", (inst, def) => {\n    $ZodCheck.init(inst, def); // no format checks\n    def.format = def.format || \"float64\";\n    const isInt = def.format?.includes(\"int\");\n    const origin = isInt ? \"int\" : \"number\";\n    const [minimum, maximum] = util.NUMBER_FORMAT_RANGES[def.format];\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.format = def.format;\n        bag.minimum = minimum;\n        bag.maximum = maximum;\n        if (isInt)\n            bag.pattern = regexes.integer;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        if (isInt) {\n            if (!Number.isInteger(input)) {\n                // invalid_format issue\n                // payload.issues.push({\n                //   expected: def.format,\n                //   format: def.format,\n                //   code: \"invalid_format\",\n                //   input,\n                //   inst,\n                // });\n                // invalid_type issue\n                payload.issues.push({\n                    expected: origin,\n                    format: def.format,\n                    code: \"invalid_type\",\n                    input,\n                    inst,\n                });\n                return;\n                // not_multiple_of issue\n                // payload.issues.push({\n                //   code: \"not_multiple_of\",\n                //   origin: \"number\",\n                //   input,\n                //   inst,\n                //   divisor: 1,\n                // });\n            }\n            if (!Number.isSafeInteger(input)) {\n                if (input > 0) {\n                    // too_big\n                    payload.issues.push({\n                        input,\n                        code: \"too_big\",\n                        maximum: Number.MAX_SAFE_INTEGER,\n                        note: \"Integers must be within the safe integer range.\",\n                        inst,\n                        origin,\n                        continue: !def.abort,\n                    });\n                }\n                else {\n                    // too_small\n                    payload.issues.push({\n                        input,\n                        code: \"too_small\",\n                        minimum: Number.MIN_SAFE_INTEGER,\n                        note: \"Integers must be within the safe integer range.\",\n                        inst,\n                        origin,\n                        continue: !def.abort,\n                    });\n                }\n                return;\n            }\n        }\n        if (input < minimum) {\n            payload.issues.push({\n                origin: \"number\",\n                input,\n                code: \"too_small\",\n                minimum,\n                inclusive: true,\n                inst,\n                continue: !def.abort,\n            });\n        }\n        if (input > maximum) {\n            payload.issues.push({\n                origin: \"number\",\n                input,\n                code: \"too_big\",\n                maximum,\n                inst,\n            });\n        }\n    };\n});\nexport const $ZodCheckBigIntFormat = /*@__PURE__*/ core.$constructor(\"$ZodCheckBigIntFormat\", (inst, def) => {\n    $ZodCheck.init(inst, def); // no format checks\n    const [minimum, maximum] = util.BIGINT_FORMAT_RANGES[def.format];\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.format = def.format;\n        bag.minimum = minimum;\n        bag.maximum = maximum;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        if (input < minimum) {\n            payload.issues.push({\n                origin: \"bigint\",\n                input,\n                code: \"too_small\",\n                minimum: minimum,\n                inclusive: true,\n                inst,\n                continue: !def.abort,\n            });\n        }\n        if (input > maximum) {\n            payload.issues.push({\n                origin: \"bigint\",\n                input,\n                code: \"too_big\",\n                maximum,\n                inst,\n            });\n        }\n    };\n});\nexport const $ZodCheckMaxSize = /*@__PURE__*/ core.$constructor(\"$ZodCheckMaxSize\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.when = (payload) => {\n        const val = payload.value;\n        return !util.nullish(val) && val.size !== undefined;\n    };\n    inst._zod.onattach.push((inst) => {\n        const curr = (inst._zod.bag.maximum ?? Number.POSITIVE_INFINITY);\n        if (def.maximum < curr)\n            inst._zod.bag.maximum = def.maximum;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        const size = input.size;\n        if (size <= def.maximum)\n            return;\n        payload.issues.push({\n            origin: util.getSizableOrigin(input),\n            code: \"too_big\",\n            maximum: def.maximum,\n            input,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckMinSize = /*@__PURE__*/ core.$constructor(\"$ZodCheckMinSize\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.when = (payload) => {\n        const val = payload.value;\n        return !util.nullish(val) && val.size !== undefined;\n    };\n    inst._zod.onattach.push((inst) => {\n        const curr = (inst._zod.bag.minimum ?? Number.NEGATIVE_INFINITY);\n        if (def.minimum > curr)\n            inst._zod.bag.minimum = def.minimum;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        const size = input.size;\n        if (size >= def.minimum)\n            return;\n        payload.issues.push({\n            origin: util.getSizableOrigin(input),\n            code: \"too_small\",\n            minimum: def.minimum,\n            input,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckSizeEquals = /*@__PURE__*/ core.$constructor(\"$ZodCheckSizeEquals\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.when = (payload) => {\n        const val = payload.value;\n        return !util.nullish(val) && val.size !== undefined;\n    };\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.minimum = def.size;\n        bag.maximum = def.size;\n        bag.size = def.size;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        const size = input.size;\n        if (size === def.size)\n            return;\n        const tooBig = size > def.size;\n        payload.issues.push({\n            origin: util.getSizableOrigin(input),\n            ...(tooBig ? { code: \"too_big\", maximum: def.size } : { code: \"too_small\", minimum: def.size }),\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckMaxLength = /*@__PURE__*/ core.$constructor(\"$ZodCheckMaxLength\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.when = (payload) => {\n        const val = payload.value;\n        return !util.nullish(val) && val.length !== undefined;\n    };\n    inst._zod.onattach.push((inst) => {\n        const curr = (inst._zod.bag.maximum ?? Number.POSITIVE_INFINITY);\n        if (def.maximum < curr)\n            inst._zod.bag.maximum = def.maximum;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        const length = input.length;\n        if (length <= def.maximum)\n            return;\n        const origin = util.getLengthableOrigin(input);\n        payload.issues.push({\n            origin,\n            code: \"too_big\",\n            maximum: def.maximum,\n            inclusive: true,\n            input,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckMinLength = /*@__PURE__*/ core.$constructor(\"$ZodCheckMinLength\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.when = (payload) => {\n        const val = payload.value;\n        return !util.nullish(val) && val.length !== undefined;\n    };\n    inst._zod.onattach.push((inst) => {\n        const curr = (inst._zod.bag.minimum ?? Number.NEGATIVE_INFINITY);\n        if (def.minimum > curr)\n            inst._zod.bag.minimum = def.minimum;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        const length = input.length;\n        if (length >= def.minimum)\n            return;\n        const origin = util.getLengthableOrigin(input);\n        payload.issues.push({\n            origin,\n            code: \"too_small\",\n            minimum: def.minimum,\n            inclusive: true,\n            input,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckLengthEquals = /*@__PURE__*/ core.$constructor(\"$ZodCheckLengthEquals\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.when = (payload) => {\n        const val = payload.value;\n        return !util.nullish(val) && val.length !== undefined;\n    };\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.minimum = def.length;\n        bag.maximum = def.length;\n        bag.length = def.length;\n    });\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        const length = input.length;\n        if (length === def.length)\n            return;\n        const origin = util.getLengthableOrigin(input);\n        const tooBig = length > def.length;\n        payload.issues.push({\n            origin,\n            ...(tooBig\n                ? { code: \"too_big\", maximum: def.length, exact: true }\n                : { code: \"too_small\", minimum: def.length, exact: true }),\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckStringFormat = /*@__PURE__*/ core.$constructor(\"$ZodCheckStringFormat\", (inst, def) => {\n    var _a, _b;\n    $ZodCheck.init(inst, def);\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.format = def.format;\n        if (def.pattern) {\n            bag.patterns ?? (bag.patterns = new Set());\n            bag.patterns.add(def.pattern);\n        }\n    });\n    if (def.pattern)\n        (_a = inst._zod).check ?? (_a.check = (payload) => {\n            def.pattern.lastIndex = 0;\n            if (def.pattern.test(payload.value))\n                return;\n            payload.issues.push({\n                origin: \"string\",\n                code: \"invalid_format\",\n                format: def.format,\n                input: payload.value,\n                ...(def.pattern ? { pattern: def.pattern.toString() } : {}),\n                inst,\n                continue: !def.abort,\n            });\n        });\n    else\n        (_b = inst._zod).check ?? (_b.check = () => { });\n});\nexport const $ZodCheckRegex = /*@__PURE__*/ core.$constructor(\"$ZodCheckRegex\", (inst, def) => {\n    $ZodCheckStringFormat.init(inst, def);\n    inst._zod.check = (payload) => {\n        def.pattern.lastIndex = 0;\n        if (def.pattern.test(payload.value))\n            return;\n        payload.issues.push({\n            origin: \"string\",\n            code: \"invalid_format\",\n            format: \"regex\",\n            input: payload.value,\n            pattern: def.pattern.toString(),\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckLowerCase = /*@__PURE__*/ core.$constructor(\"$ZodCheckLowerCase\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.lowercase);\n    $ZodCheckStringFormat.init(inst, def);\n});\nexport const $ZodCheckUpperCase = /*@__PURE__*/ core.$constructor(\"$ZodCheckUpperCase\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.uppercase);\n    $ZodCheckStringFormat.init(inst, def);\n});\nexport const $ZodCheckIncludes = /*@__PURE__*/ core.$constructor(\"$ZodCheckIncludes\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    const escapedRegex = util.escapeRegex(def.includes);\n    const pattern = new RegExp(typeof def.position === \"number\" ? `^.{${def.position}}${escapedRegex}` : escapedRegex);\n    def.pattern = pattern;\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.patterns ?? (bag.patterns = new Set());\n        bag.patterns.add(pattern);\n    });\n    inst._zod.check = (payload) => {\n        if (payload.value.includes(def.includes, def.position))\n            return;\n        payload.issues.push({\n            origin: \"string\",\n            code: \"invalid_format\",\n            format: \"includes\",\n            includes: def.includes,\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckStartsWith = /*@__PURE__*/ core.$constructor(\"$ZodCheckStartsWith\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    const pattern = new RegExp(`^${util.escapeRegex(def.prefix)}.*`);\n    def.pattern ?? (def.pattern = pattern);\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.patterns ?? (bag.patterns = new Set());\n        bag.patterns.add(pattern);\n    });\n    inst._zod.check = (payload) => {\n        if (payload.value.startsWith(def.prefix))\n            return;\n        payload.issues.push({\n            origin: \"string\",\n            code: \"invalid_format\",\n            format: \"starts_with\",\n            prefix: def.prefix,\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCheckEndsWith = /*@__PURE__*/ core.$constructor(\"$ZodCheckEndsWith\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    const pattern = new RegExp(`.*${util.escapeRegex(def.suffix)}$`);\n    def.pattern ?? (def.pattern = pattern);\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.patterns ?? (bag.patterns = new Set());\n        bag.patterns.add(pattern);\n    });\n    inst._zod.check = (payload) => {\n        if (payload.value.endsWith(def.suffix))\n            return;\n        payload.issues.push({\n            origin: \"string\",\n            code: \"invalid_format\",\n            format: \"ends_with\",\n            suffix: def.suffix,\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\n///////////////////////////////////\n/////    $ZodCheckProperty    /////\n///////////////////////////////////\nfunction handleCheckPropertyResult(result, payload, property) {\n    if (result.issues.length) {\n        payload.issues.push(...util.prefixIssues(property, result.issues));\n    }\n}\nexport const $ZodCheckProperty = /*@__PURE__*/ core.$constructor(\"$ZodCheckProperty\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.check = (payload) => {\n        const result = def.schema._zod.run({\n            value: payload.value[def.property],\n            issues: [],\n        }, {});\n        if (result instanceof Promise) {\n            return result.then((result) => handleCheckPropertyResult(result, payload, def.property));\n        }\n        handleCheckPropertyResult(result, payload, def.property);\n        return;\n    };\n});\nexport const $ZodCheckMimeType = /*@__PURE__*/ core.$constructor(\"$ZodCheckMimeType\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    const mimeSet = new Set(def.mime);\n    inst._zod.onattach.push((inst) => {\n        inst._zod.bag.mime = def.mime;\n    });\n    inst._zod.check = (payload) => {\n        if (mimeSet.has(payload.value.type))\n            return;\n        payload.issues.push({\n            code: \"invalid_value\",\n            values: def.mime,\n            input: payload.value.type,\n            inst,\n        });\n    };\n});\nexport const $ZodCheckOverwrite = /*@__PURE__*/ core.$constructor(\"$ZodCheckOverwrite\", (inst, def) => {\n    $ZodCheck.init(inst, def);\n    inst._zod.check = (payload) => {\n        payload.value = def.tx(payload.value);\n    };\n});\n", "export class Doc {\n    constructor(args = []) {\n        this.content = [];\n        this.indent = 0;\n        if (this)\n            this.args = args;\n    }\n    indented(fn) {\n        this.indent += 1;\n        fn(this);\n        this.indent -= 1;\n    }\n    write(arg) {\n        if (typeof arg === \"function\") {\n            arg(this, { execution: \"sync\" });\n            arg(this, { execution: \"async\" });\n            return;\n        }\n        const content = arg;\n        const lines = content.split(\"\\n\").filter((x) => x);\n        const minIndent = Math.min(...lines.map((x) => x.length - x.trimStart().length));\n        const dedented = lines.map((x) => x.slice(minIndent)).map((x) => \" \".repeat(this.indent * 2) + x);\n        for (const line of dedented) {\n            this.content.push(line);\n        }\n    }\n    compile() {\n        const F = Function;\n        const args = this?.args;\n        const content = this?.content ?? [``];\n        const lines = [...content.map((x) => `  ${x}`)];\n        // console.log(lines.join(\"\\n\"));\n        return new F(...args, lines.join(\"\\n\"));\n    }\n}\n", "export const version = {\n    major: 4,\n    minor: 0,\n    patch: 0,\n};\n", "import * as checks from \"./checks.js\";\nimport * as core from \"./core.js\";\nimport { Doc } from \"./doc.js\";\nimport { safeParse, safeParseAsync } from \"./parse.js\";\nimport * as regexes from \"./regexes.js\";\nimport * as util from \"./util.js\";\nimport { version } from \"./versions.js\";\nexport const $ZodType = /*@__PURE__*/ core.$constructor(\"$ZodType\", (inst, def) => {\n    var _a;\n    inst ?? (inst = {});\n    // avoids issues with using Math.random() in Next.js caching\n    util.defineLazy(inst._zod, \"id\", () => def.type + \"_\" + util.randomString(10));\n    inst._zod.def = def; // set _def property\n    inst._zod.bag = inst._zod.bag || {}; // initialize _bag object\n    inst._zod.version = version;\n    const checks = [...(inst._zod.def.checks ?? [])];\n    // if inst is itself a checks.$ZodCheck, run it as a check\n    if (inst._zod.traits.has(\"$ZodCheck\")) {\n        checks.unshift(inst);\n    }\n    //\n    for (const ch of checks) {\n        for (const fn of ch._zod.onattach) {\n            fn(inst);\n        }\n    }\n    if (checks.length === 0) {\n        // deferred initializer\n        // inst._zod.parse is not yet defined\n        (_a = inst._zod).deferred ?? (_a.deferred = []);\n        inst._zod.deferred?.push(() => {\n            inst._zod.run = inst._zod.parse;\n        });\n    }\n    else {\n        const runChecks = (payload, checks, ctx) => {\n            let isAborted = util.aborted(payload);\n            let asyncResult;\n            for (const ch of checks) {\n                if (ch._zod.when) {\n                    const shouldRun = ch._zod.when(payload);\n                    if (!shouldRun)\n                        continue;\n                }\n                else if (isAborted) {\n                    continue;\n                }\n                const currLen = payload.issues.length;\n                const _ = ch._zod.check(payload);\n                if (_ instanceof Promise && ctx?.async === false) {\n                    throw new core.$ZodAsyncError();\n                }\n                if (asyncResult || _ instanceof Promise) {\n                    asyncResult = (asyncResult ?? Promise.resolve()).then(async () => {\n                        await _;\n                        const nextLen = payload.issues.length;\n                        if (nextLen === currLen)\n                            return;\n                        if (!isAborted)\n                            isAborted = util.aborted(payload, currLen);\n                    });\n                }\n                else {\n                    const nextLen = payload.issues.length;\n                    if (nextLen === currLen)\n                        continue;\n                    if (!isAborted)\n                        isAborted = util.aborted(payload, currLen);\n                }\n            }\n            if (asyncResult) {\n                return asyncResult.then(() => {\n                    return payload;\n                });\n            }\n            return payload;\n        };\n        inst._zod.run = (payload, ctx) => {\n            const result = inst._zod.parse(payload, ctx);\n            if (result instanceof Promise) {\n                if (ctx.async === false)\n                    throw new core.$ZodAsyncError();\n                return result.then((result) => runChecks(result, checks, ctx));\n            }\n            return runChecks(result, checks, ctx);\n        };\n    }\n    inst[\"~standard\"] = {\n        validate: (value) => {\n            try {\n                const r = safeParse(inst, value);\n                return r.success ? { value: r.data } : { issues: r.error?.issues };\n            }\n            catch (_) {\n                return safeParseAsync(inst, value).then((r) => (r.success ? { value: r.data } : { issues: r.error?.issues }));\n            }\n        },\n        vendor: \"zod\",\n        version: 1,\n    };\n});\nexport { clone } from \"./util.js\";\nexport const $ZodString = /*@__PURE__*/ core.$constructor(\"$ZodString\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.pattern = [...(inst?._zod.bag?.patterns ?? [])].pop() ?? regexes.string(inst._zod.bag);\n    inst._zod.parse = (payload, _) => {\n        if (def.coerce)\n            try {\n                payload.value = String(payload.value);\n            }\n            catch (_) { }\n        if (typeof payload.value === \"string\")\n            return payload;\n        payload.issues.push({\n            expected: \"string\",\n            code: \"invalid_type\",\n            input: payload.value,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodStringFormat = /*@__PURE__*/ core.$constructor(\"$ZodStringFormat\", (inst, def) => {\n    // check initialization must come first\n    checks.$ZodCheckStringFormat.init(inst, def);\n    $ZodString.init(inst, def);\n});\nexport const $ZodGUID = /*@__PURE__*/ core.$constructor(\"$ZodGUID\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.guid);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodUUID = /*@__PURE__*/ core.$constructor(\"$ZodUUID\", (inst, def) => {\n    if (def.version) {\n        const versionMap = {\n            v1: 1,\n            v2: 2,\n            v3: 3,\n            v4: 4,\n            v5: 5,\n            v6: 6,\n            v7: 7,\n            v8: 8,\n        };\n        const v = versionMap[def.version];\n        if (v === undefined)\n            throw new Error(`Invalid UUID version: \"${def.version}\"`);\n        def.pattern ?? (def.pattern = regexes.uuid(v));\n    }\n    else\n        def.pattern ?? (def.pattern = regexes.uuid());\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodEmail = /*@__PURE__*/ core.$constructor(\"$ZodEmail\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.email);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodURL = /*@__PURE__*/ core.$constructor(\"$ZodURL\", (inst, def) => {\n    $ZodStringFormat.init(inst, def);\n    inst._zod.check = (payload) => {\n        try {\n            const url = new URL(payload.value);\n            if (def.hostname) {\n                def.hostname.lastIndex = 0;\n                if (!def.hostname.test(url.hostname)) {\n                    payload.issues.push({\n                        code: \"invalid_format\",\n                        format: \"url\",\n                        note: \"Invalid hostname\",\n                        pattern: regexes.hostname.source,\n                        input: payload.value,\n                        inst,\n                        continue: !def.abort,\n                    });\n                }\n            }\n            if (def.protocol) {\n                def.protocol.lastIndex = 0;\n                if (!def.protocol.test(url.protocol.endsWith(\":\") ? url.protocol.slice(0, -1) : url.protocol)) {\n                    payload.issues.push({\n                        code: \"invalid_format\",\n                        format: \"url\",\n                        note: \"Invalid protocol\",\n                        pattern: def.protocol.source,\n                        input: payload.value,\n                        inst,\n                        continue: !def.abort,\n                    });\n                }\n            }\n            return;\n        }\n        catch (_) {\n            payload.issues.push({\n                code: \"invalid_format\",\n                format: \"url\",\n                input: payload.value,\n                inst,\n                continue: !def.abort,\n            });\n        }\n    };\n});\nexport const $ZodEmoji = /*@__PURE__*/ core.$constructor(\"$ZodEmoji\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.emoji());\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodNanoID = /*@__PURE__*/ core.$constructor(\"$ZodNanoID\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.nanoid);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodCUID = /*@__PURE__*/ core.$constructor(\"$ZodCUID\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.cuid);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodCUID2 = /*@__PURE__*/ core.$constructor(\"$ZodCUID2\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.cuid2);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodULID = /*@__PURE__*/ core.$constructor(\"$ZodULID\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.ulid);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodXID = /*@__PURE__*/ core.$constructor(\"$ZodXID\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.xid);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodKSUID = /*@__PURE__*/ core.$constructor(\"$ZodKSUID\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.ksuid);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodISODateTime = /*@__PURE__*/ core.$constructor(\"$ZodISODateTime\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.datetime(def));\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodISODate = /*@__PURE__*/ core.$constructor(\"$ZodISODate\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.date);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodISOTime = /*@__PURE__*/ core.$constructor(\"$ZodISOTime\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.time(def));\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodISODuration = /*@__PURE__*/ core.$constructor(\"$ZodISODuration\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.duration);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodIPv4 = /*@__PURE__*/ core.$constructor(\"$ZodIPv4\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.ipv4);\n    $ZodStringFormat.init(inst, def);\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.format = `ipv4`;\n    });\n});\nexport const $ZodIPv6 = /*@__PURE__*/ core.$constructor(\"$ZodIPv6\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.ipv6);\n    $ZodStringFormat.init(inst, def);\n    inst._zod.onattach.push((inst) => {\n        const bag = inst._zod.bag;\n        bag.format = `ipv6`;\n    });\n    inst._zod.check = (payload) => {\n        try {\n            new URL(`http://[${payload.value}]`);\n            // return;\n        }\n        catch {\n            payload.issues.push({\n                code: \"invalid_format\",\n                format: \"ipv6\",\n                input: payload.value,\n                inst,\n                continue: !def.abort,\n            });\n        }\n    };\n});\nexport const $ZodCIDRv4 = /*@__PURE__*/ core.$constructor(\"$ZodCIDRv4\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.cidrv4);\n    $ZodStringFormat.init(inst, def);\n});\nexport const $ZodCIDRv6 = /*@__PURE__*/ core.$constructor(\"$ZodCIDRv6\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.cidrv6); // not used for validation\n    $ZodStringFormat.init(inst, def);\n    inst._zod.check = (payload) => {\n        const [address, prefix] = payload.value.split(\"/\");\n        try {\n            if (!prefix)\n                throw new Error();\n            const prefixNum = Number(prefix);\n            if (`${prefixNum}` !== prefix)\n                throw new Error();\n            if (prefixNum < 0 || prefixNum > 128)\n                throw new Error();\n            new URL(`http://[${address}]`);\n        }\n        catch {\n            payload.issues.push({\n                code: \"invalid_format\",\n                format: \"cidrv6\",\n                input: payload.value,\n                inst,\n                continue: !def.abort,\n            });\n        }\n    };\n});\n//////////////////////////////   ZodBase64   //////////////////////////////\nexport function isValidBase64(data) {\n    if (data === \"\")\n        return true;\n    if (data.length % 4 !== 0)\n        return false;\n    try {\n        atob(data);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nexport const $ZodBase64 = /*@__PURE__*/ core.$constructor(\"$ZodBase64\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.base64);\n    $ZodStringFormat.init(inst, def);\n    inst._zod.onattach.push((inst) => {\n        inst._zod.bag.contentEncoding = \"base64\";\n    });\n    inst._zod.check = (payload) => {\n        if (isValidBase64(payload.value))\n            return;\n        payload.issues.push({\n            code: \"invalid_format\",\n            format: \"base64\",\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\n//////////////////////////////   ZodBase64   //////////////////////////////\nexport function isValidBase64URL(data) {\n    if (!regexes.base64url.test(data))\n        return false;\n    const base64 = data.replace(/[-_]/g, (c) => (c === \"-\" ? \"+\" : \"/\"));\n    const padded = base64.padEnd(Math.ceil(base64.length / 4) * 4, \"=\");\n    return isValidBase64(padded);\n}\nexport const $ZodBase64URL = /*@__PURE__*/ core.$constructor(\"$ZodBase64URL\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.base64url);\n    $ZodStringFormat.init(inst, def);\n    inst._zod.onattach.push((inst) => {\n        inst._zod.bag.contentEncoding = \"base64url\";\n    });\n    inst._zod.check = (payload) => {\n        if (isValidBase64URL(payload.value))\n            return;\n        payload.issues.push({\n            code: \"invalid_format\",\n            format: \"base64url\",\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodE164 = /*@__PURE__*/ core.$constructor(\"$ZodE164\", (inst, def) => {\n    def.pattern ?? (def.pattern = regexes.e164);\n    $ZodStringFormat.init(inst, def);\n});\n//////////////////////////////   ZodJWT   //////////////////////////////\nexport function isValidJWT(token, algorithm = null) {\n    try {\n        const tokensParts = token.split(\".\");\n        if (tokensParts.length !== 3)\n            return false;\n        const [header] = tokensParts;\n        if (!header)\n            return false;\n        const parsedHeader = JSON.parse(atob(header));\n        if (\"typ\" in parsedHeader && parsedHeader?.typ !== \"JWT\")\n            return false;\n        if (!parsedHeader.alg)\n            return false;\n        if (algorithm && (!(\"alg\" in parsedHeader) || parsedHeader.alg !== algorithm))\n            return false;\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nexport const $ZodJWT = /*@__PURE__*/ core.$constructor(\"$ZodJWT\", (inst, def) => {\n    $ZodStringFormat.init(inst, def);\n    inst._zod.check = (payload) => {\n        if (isValidJWT(payload.value, def.alg))\n            return;\n        payload.issues.push({\n            code: \"invalid_format\",\n            format: \"jwt\",\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodCustomStringFormat = /*@__PURE__*/ core.$constructor(\"$ZodCustomStringFormat\", (inst, def) => {\n    $ZodStringFormat.init(inst, def);\n    inst._zod.check = (payload) => {\n        if (def.fn(payload.value))\n            return;\n        payload.issues.push({\n            code: \"invalid_format\",\n            format: def.format,\n            input: payload.value,\n            inst,\n            continue: !def.abort,\n        });\n    };\n});\nexport const $ZodNumber = /*@__PURE__*/ core.$constructor(\"$ZodNumber\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.pattern = inst._zod.bag.pattern ?? regexes.number;\n    inst._zod.parse = (payload, _ctx) => {\n        if (def.coerce)\n            try {\n                payload.value = Number(payload.value);\n            }\n            catch (_) { }\n        const input = payload.value;\n        if (typeof input === \"number\" && !Number.isNaN(input) && Number.isFinite(input)) {\n            return payload;\n        }\n        const received = typeof input === \"number\"\n            ? Number.isNaN(input)\n                ? \"NaN\"\n                : !Number.isFinite(input)\n                    ? \"Infinity\"\n                    : undefined\n            : undefined;\n        payload.issues.push({\n            expected: \"number\",\n            code: \"invalid_type\",\n            input,\n            inst,\n            ...(received ? { received } : {}),\n        });\n        return payload;\n    };\n});\nexport const $ZodNumberFormat = /*@__PURE__*/ core.$constructor(\"$ZodNumber\", (inst, def) => {\n    checks.$ZodCheckNumberFormat.init(inst, def);\n    $ZodNumber.init(inst, def); // no format checksp\n});\nexport const $ZodBoolean = /*@__PURE__*/ core.$constructor(\"$ZodBoolean\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.pattern = regexes.boolean;\n    inst._zod.parse = (payload, _ctx) => {\n        if (def.coerce)\n            try {\n                payload.value = Boolean(payload.value);\n            }\n            catch (_) { }\n        const input = payload.value;\n        if (typeof input === \"boolean\")\n            return payload;\n        payload.issues.push({\n            expected: \"boolean\",\n            code: \"invalid_type\",\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodBigInt = /*@__PURE__*/ core.$constructor(\"$ZodBigInt\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.pattern = regexes.bigint;\n    inst._zod.parse = (payload, _ctx) => {\n        if (def.coerce)\n            try {\n                payload.value = BigInt(payload.value);\n            }\n            catch (_) { }\n        const { value: input } = payload;\n        if (typeof input === \"bigint\")\n            return payload;\n        payload.issues.push({\n            expected: \"bigint\",\n            code: \"invalid_type\",\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodBigIntFormat = /*@__PURE__*/ core.$constructor(\"$ZodBigInt\", (inst, def) => {\n    checks.$ZodCheckBigIntFormat.init(inst, def);\n    $ZodBigInt.init(inst, def); // no format checks\n});\nexport const $ZodSymbol = /*@__PURE__*/ core.$constructor(\"$ZodSymbol\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _ctx) => {\n        const { value: input } = payload;\n        if (typeof input === \"symbol\")\n            return payload;\n        payload.issues.push({\n            expected: \"symbol\",\n            code: \"invalid_type\",\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodUndefined = /*@__PURE__*/ core.$constructor(\"$ZodUndefined\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.pattern = regexes.undefined;\n    inst._zod.values = new Set([undefined]);\n    inst._zod.parse = (payload, _ctx) => {\n        const { value: input } = payload;\n        if (typeof input === \"undefined\")\n            return payload;\n        payload.issues.push({\n            expected: \"undefined\",\n            code: \"invalid_type\",\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodNull = /*@__PURE__*/ core.$constructor(\"$ZodNull\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.pattern = regexes.null;\n    inst._zod.values = new Set([null]);\n    inst._zod.parse = (payload, _ctx) => {\n        const { value: input } = payload;\n        if (input === null)\n            return payload;\n        payload.issues.push({\n            expected: \"null\",\n            code: \"invalid_type\",\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodAny = /*@__PURE__*/ core.$constructor(\"$ZodAny\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload) => payload;\n});\nexport const $ZodUnknown = /*@__PURE__*/ core.$constructor(\"$ZodUnknown\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload) => payload;\n});\nexport const $ZodNever = /*@__PURE__*/ core.$constructor(\"$ZodNever\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _ctx) => {\n        payload.issues.push({\n            expected: \"never\",\n            code: \"invalid_type\",\n            input: payload.value,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodVoid = /*@__PURE__*/ core.$constructor(\"$ZodVoid\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _ctx) => {\n        const { value: input } = payload;\n        if (typeof input === \"undefined\")\n            return payload;\n        payload.issues.push({\n            expected: \"void\",\n            code: \"invalid_type\",\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodDate = /*@__PURE__*/ core.$constructor(\"$ZodDate\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _ctx) => {\n        if (def.coerce) {\n            try {\n                payload.value = new Date(payload.value);\n            }\n            catch (_err) { }\n        }\n        const input = payload.value;\n        const isDate = input instanceof Date;\n        const isValidDate = isDate && !Number.isNaN(input.getTime());\n        if (isValidDate)\n            return payload;\n        payload.issues.push({\n            expected: \"date\",\n            code: \"invalid_type\",\n            input,\n            ...(isDate ? { received: \"Invalid Date\" } : {}),\n            inst,\n        });\n        return payload;\n    };\n});\nfunction handleArrayResult(result, final, index) {\n    if (result.issues.length) {\n        final.issues.push(...util.prefixIssues(index, result.issues));\n    }\n    final.value[index] = result.value;\n}\nexport const $ZodArray = /*@__PURE__*/ core.$constructor(\"$ZodArray\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, ctx) => {\n        const input = payload.value;\n        if (!Array.isArray(input)) {\n            payload.issues.push({\n                expected: \"array\",\n                code: \"invalid_type\",\n                input,\n                inst,\n            });\n            return payload;\n        }\n        payload.value = Array(input.length);\n        const proms = [];\n        for (let i = 0; i < input.length; i++) {\n            const item = input[i];\n            const result = def.element._zod.run({\n                value: item,\n                issues: [],\n            }, ctx);\n            if (result instanceof Promise) {\n                proms.push(result.then((result) => handleArrayResult(result, payload, i)));\n            }\n            else {\n                handleArrayResult(result, payload, i);\n            }\n        }\n        if (proms.length) {\n            return Promise.all(proms).then(() => payload);\n        }\n        return payload; //handleArrayResultsAsync(parseResults, final);\n    };\n});\nfunction handleObjectResult(result, final, key) {\n    // if(isOptional)\n    if (result.issues.length) {\n        final.issues.push(...util.prefixIssues(key, result.issues));\n    }\n    final.value[key] = result.value;\n}\nfunction handleOptionalObjectResult(result, final, key, input) {\n    if (result.issues.length) {\n        // validation failed against value schema\n        if (input[key] === undefined) {\n            // if input was undefined, ignore the error\n            if (key in input) {\n                final.value[key] = undefined;\n            }\n            else {\n                final.value[key] = result.value;\n            }\n        }\n        else {\n            final.issues.push(...util.prefixIssues(key, result.issues));\n        }\n    }\n    else if (result.value === undefined) {\n        // validation returned `undefined`\n        if (key in input)\n            final.value[key] = undefined;\n    }\n    else {\n        // non-undefined value\n        final.value[key] = result.value;\n    }\n}\nexport const $ZodObject = /*@__PURE__*/ core.$constructor(\"$ZodObject\", (inst, def) => {\n    // requires cast because technically $ZodObject doesn't extend\n    $ZodType.init(inst, def);\n    const _normalized = util.cached(() => {\n        const keys = Object.keys(def.shape);\n        for (const k of keys) {\n            if (!(def.shape[k] instanceof $ZodType)) {\n                throw new Error(`Invalid element at key \"${k}\": expected a Zod schema`);\n            }\n        }\n        const okeys = util.optionalKeys(def.shape);\n        return {\n            shape: def.shape,\n            keys,\n            keySet: new Set(keys),\n            numKeys: keys.length,\n            optionalKeys: new Set(okeys),\n        };\n    });\n    util.defineLazy(inst._zod, \"propValues\", () => {\n        const shape = def.shape;\n        const propValues = {};\n        for (const key in shape) {\n            const field = shape[key]._zod;\n            if (field.values) {\n                propValues[key] ?? (propValues[key] = new Set());\n                for (const v of field.values)\n                    propValues[key].add(v);\n            }\n        }\n        return propValues;\n    });\n    const generateFastpass = (shape) => {\n        const doc = new Doc([\"shape\", \"payload\", \"ctx\"]);\n        const { keys, optionalKeys } = _normalized.value;\n        const parseStr = (key) => {\n            const k = util.esc(key);\n            return `shape[${k}]._zod.run({ value: input[${k}], issues: [] }, ctx)`;\n        };\n        doc.write(`const input = payload.value;`);\n        const ids = Object.create(null);\n        for (const key of keys) {\n            ids[key] = util.randomString(15);\n        }\n        // A: preserve key order {\n        doc.write(`const newResult = {}`);\n        for (const key of keys) {\n            if (optionalKeys.has(key)) {\n                const id = ids[key];\n                doc.write(`const ${id} = ${parseStr(key)};`);\n                const k = util.esc(key);\n                doc.write(`\n        if (${id}.issues.length) {\n          if (input[${k}] === undefined) {\n            if (${k} in input) {\n              newResult[${k}] = undefined;\n            }\n          } else {\n            payload.issues = payload.issues.concat(\n              ${id}.issues.map((iss) => ({\n                ...iss,\n                path: iss.path ? [${k}, ...iss.path] : [${k}],\n              }))\n            );\n          }\n        } else if (${id}.value === undefined) {\n          if (${k} in input) newResult[${k}] = undefined;\n        } else {\n          newResult[${k}] = ${id}.value;\n        }\n        `);\n            }\n            else {\n                const id = ids[key];\n                //  const id = ids[key];\n                doc.write(`const ${id} = ${parseStr(key)};`);\n                doc.write(`\n          if (${id}.issues.length) payload.issues = payload.issues.concat(${id}.issues.map(iss => ({\n            ...iss,\n            path: iss.path ? [${util.esc(key)}, ...iss.path] : [${util.esc(key)}]\n          })));`);\n                doc.write(`newResult[${util.esc(key)}] = ${id}.value`);\n            }\n        }\n        doc.write(`payload.value = newResult;`);\n        doc.write(`return payload;`);\n        const fn = doc.compile();\n        return (payload, ctx) => fn(shape, payload, ctx);\n    };\n    let fastpass;\n    const isObject = util.isObject;\n    const jit = !core.globalConfig.jitless;\n    const allowsEval = util.allowsEval;\n    const fastEnabled = jit && allowsEval.value; // && !def.catchall;\n    const { catchall } = def;\n    let value;\n    inst._zod.parse = (payload, ctx) => {\n        value ?? (value = _normalized.value);\n        const input = payload.value;\n        if (!isObject(input)) {\n            payload.issues.push({\n                expected: \"object\",\n                code: \"invalid_type\",\n                input,\n                inst,\n            });\n            return payload;\n        }\n        const proms = [];\n        if (jit && fastEnabled && ctx?.async === false && ctx.jitless !== true) {\n            // always synchronous\n            if (!fastpass)\n                fastpass = generateFastpass(def.shape);\n            payload = fastpass(payload, ctx);\n        }\n        else {\n            payload.value = {};\n            const shape = value.shape;\n            for (const key of value.keys) {\n                const el = shape[key];\n                // do not add omitted optional keys\n                // if (!(key in input)) {\n                //   if (optionalKeys.has(key)) continue;\n                //   payload.issues.push({\n                //     code: \"invalid_type\",\n                //     path: [key],\n                //     expected: \"nonoptional\",\n                //     note: `Missing required key: \"${key}\"`,\n                //     input,\n                //     inst,\n                //   });\n                // }\n                const r = el._zod.run({ value: input[key], issues: [] }, ctx);\n                const isOptional = el._zod.optin === \"optional\" && el._zod.optout === \"optional\";\n                if (r instanceof Promise) {\n                    proms.push(r.then((r) => isOptional ? handleOptionalObjectResult(r, payload, key, input) : handleObjectResult(r, payload, key)));\n                }\n                else if (isOptional) {\n                    handleOptionalObjectResult(r, payload, key, input);\n                }\n                else {\n                    handleObjectResult(r, payload, key);\n                }\n            }\n        }\n        if (!catchall) {\n            // return payload;\n            return proms.length ? Promise.all(proms).then(() => payload) : payload;\n        }\n        const unrecognized = [];\n        // iterate over input keys\n        const keySet = value.keySet;\n        const _catchall = catchall._zod;\n        const t = _catchall.def.type;\n        for (const key of Object.keys(input)) {\n            if (keySet.has(key))\n                continue;\n            if (t === \"never\") {\n                unrecognized.push(key);\n                continue;\n            }\n            const r = _catchall.run({ value: input[key], issues: [] }, ctx);\n            if (r instanceof Promise) {\n                proms.push(r.then((r) => handleObjectResult(r, payload, key)));\n            }\n            else {\n                handleObjectResult(r, payload, key);\n            }\n        }\n        if (unrecognized.length) {\n            payload.issues.push({\n                code: \"unrecognized_keys\",\n                keys: unrecognized,\n                input,\n                inst,\n            });\n        }\n        if (!proms.length)\n            return payload;\n        return Promise.all(proms).then(() => {\n            return payload;\n        });\n    };\n});\nfunction handleUnionResults(results, final, inst, ctx) {\n    for (const result of results) {\n        if (result.issues.length === 0) {\n            final.value = result.value;\n            return final;\n        }\n    }\n    final.issues.push({\n        code: \"invalid_union\",\n        input: final.value,\n        inst,\n        errors: results.map((result) => result.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config()))),\n    });\n    return final;\n}\nexport const $ZodUnion = /*@__PURE__*/ core.$constructor(\"$ZodUnion\", (inst, def) => {\n    $ZodType.init(inst, def);\n    util.defineLazy(inst._zod, \"values\", () => {\n        if (def.options.every((o) => o._zod.values)) {\n            return new Set(def.options.flatMap((option) => Array.from(option._zod.values)));\n        }\n        return undefined;\n    });\n    util.defineLazy(inst._zod, \"pattern\", () => {\n        if (def.options.every((o) => o._zod.pattern)) {\n            const patterns = def.options.map((o) => o._zod.pattern);\n            return new RegExp(`^(${patterns.map((p) => util.cleanRegex(p.source)).join(\"|\")})$`);\n        }\n        return undefined;\n    });\n    inst._zod.parse = (payload, ctx) => {\n        let async = false;\n        const results = [];\n        for (const option of def.options) {\n            const result = option._zod.run({\n                value: payload.value,\n                issues: [],\n            }, ctx);\n            if (result instanceof Promise) {\n                results.push(result);\n                async = true;\n            }\n            else {\n                if (result.issues.length === 0)\n                    return result;\n                results.push(result);\n            }\n        }\n        if (!async)\n            return handleUnionResults(results, payload, inst, ctx);\n        return Promise.all(results).then((results) => {\n            return handleUnionResults(results, payload, inst, ctx);\n        });\n    };\n});\nexport const $ZodDiscriminatedUnion = \n/*@__PURE__*/\ncore.$constructor(\"$ZodDiscriminatedUnion\", (inst, def) => {\n    $ZodUnion.init(inst, def);\n    const _super = inst._zod.parse;\n    util.defineLazy(inst._zod, \"propValues\", () => {\n        const propValues = {};\n        for (const option of def.options) {\n            const pv = option._zod.propValues;\n            if (!pv || Object.keys(pv).length === 0)\n                throw new Error(`Invalid discriminated union option at index \"${def.options.indexOf(option)}\"`);\n            for (const [k, v] of Object.entries(pv)) {\n                if (!propValues[k])\n                    propValues[k] = new Set();\n                for (const val of v) {\n                    propValues[k].add(val);\n                }\n            }\n        }\n        return propValues;\n    });\n    const disc = util.cached(() => {\n        const opts = def.options;\n        const map = new Map();\n        for (const o of opts) {\n            const values = o._zod.propValues[def.discriminator];\n            if (!values || values.size === 0)\n                throw new Error(`Invalid discriminated union option at index \"${def.options.indexOf(o)}\"`);\n            for (const v of values) {\n                if (map.has(v)) {\n                    throw new Error(`Duplicate discriminator value \"${String(v)}\"`);\n                }\n                map.set(v, o);\n            }\n        }\n        return map;\n    });\n    inst._zod.parse = (payload, ctx) => {\n        const input = payload.value;\n        if (!util.isObject(input)) {\n            payload.issues.push({\n                code: \"invalid_type\",\n                expected: \"object\",\n                input,\n                inst,\n            });\n            return payload;\n        }\n        const opt = disc.value.get(input?.[def.discriminator]);\n        if (opt) {\n            return opt._zod.run(payload, ctx);\n        }\n        if (def.unionFallback) {\n            return _super(payload, ctx);\n        }\n        // no matching discriminator\n        payload.issues.push({\n            code: \"invalid_union\",\n            errors: [],\n            note: \"No matching discriminator\",\n            input,\n            path: [def.discriminator],\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodIntersection = /*@__PURE__*/ core.$constructor(\"$ZodIntersection\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, ctx) => {\n        const { value: input } = payload;\n        const left = def.left._zod.run({ value: input, issues: [] }, ctx);\n        const right = def.right._zod.run({ value: input, issues: [] }, ctx);\n        const async = left instanceof Promise || right instanceof Promise;\n        if (async) {\n            return Promise.all([left, right]).then(([left, right]) => {\n                return handleIntersectionResults(payload, left, right);\n            });\n        }\n        return handleIntersectionResults(payload, left, right);\n    };\n});\nfunction mergeValues(a, b) {\n    // const aType = parse.t(a);\n    // const bType = parse.t(b);\n    if (a === b) {\n        return { valid: true, data: a };\n    }\n    if (a instanceof Date && b instanceof Date && +a === +b) {\n        return { valid: true, data: a };\n    }\n    if (util.isPlainObject(a) && util.isPlainObject(b)) {\n        const bKeys = Object.keys(b);\n        const sharedKeys = Object.keys(a).filter((key) => bKeys.indexOf(key) !== -1);\n        const newObj = { ...a, ...b };\n        for (const key of sharedKeys) {\n            const sharedValue = mergeValues(a[key], b[key]);\n            if (!sharedValue.valid) {\n                return {\n                    valid: false,\n                    mergeErrorPath: [key, ...sharedValue.mergeErrorPath],\n                };\n            }\n            newObj[key] = sharedValue.data;\n        }\n        return { valid: true, data: newObj };\n    }\n    if (Array.isArray(a) && Array.isArray(b)) {\n        if (a.length !== b.length) {\n            return { valid: false, mergeErrorPath: [] };\n        }\n        const newArray = [];\n        for (let index = 0; index < a.length; index++) {\n            const itemA = a[index];\n            const itemB = b[index];\n            const sharedValue = mergeValues(itemA, itemB);\n            if (!sharedValue.valid) {\n                return {\n                    valid: false,\n                    mergeErrorPath: [index, ...sharedValue.mergeErrorPath],\n                };\n            }\n            newArray.push(sharedValue.data);\n        }\n        return { valid: true, data: newArray };\n    }\n    return { valid: false, mergeErrorPath: [] };\n}\nfunction handleIntersectionResults(result, left, right) {\n    if (left.issues.length) {\n        result.issues.push(...left.issues);\n    }\n    if (right.issues.length) {\n        result.issues.push(...right.issues);\n    }\n    if (util.aborted(result))\n        return result;\n    const merged = mergeValues(left.value, right.value);\n    if (!merged.valid) {\n        throw new Error(`Unmergable intersection. Error path: ` + `${JSON.stringify(merged.mergeErrorPath)}`);\n    }\n    result.value = merged.data;\n    return result;\n}\nexport const $ZodTuple = /*@__PURE__*/ core.$constructor(\"$ZodTuple\", (inst, def) => {\n    $ZodType.init(inst, def);\n    const items = def.items;\n    const optStart = items.length - [...items].reverse().findIndex((item) => item._zod.optin !== \"optional\");\n    inst._zod.parse = (payload, ctx) => {\n        const input = payload.value;\n        if (!Array.isArray(input)) {\n            payload.issues.push({\n                input,\n                inst,\n                expected: \"tuple\",\n                code: \"invalid_type\",\n            });\n            return payload;\n        }\n        payload.value = [];\n        const proms = [];\n        if (!def.rest) {\n            const tooBig = input.length > items.length;\n            const tooSmall = input.length < optStart - 1;\n            if (tooBig || tooSmall) {\n                payload.issues.push({\n                    input,\n                    inst,\n                    origin: \"array\",\n                    ...(tooBig ? { code: \"too_big\", maximum: items.length } : { code: \"too_small\", minimum: items.length }),\n                });\n                return payload;\n            }\n        }\n        let i = -1;\n        for (const item of items) {\n            i++;\n            if (i >= input.length)\n                if (i >= optStart)\n                    continue;\n            const result = item._zod.run({\n                value: input[i],\n                issues: [],\n            }, ctx);\n            if (result instanceof Promise) {\n                proms.push(result.then((result) => handleTupleResult(result, payload, i)));\n            }\n            else {\n                handleTupleResult(result, payload, i);\n            }\n        }\n        if (def.rest) {\n            const rest = input.slice(items.length);\n            for (const el of rest) {\n                i++;\n                const result = def.rest._zod.run({\n                    value: el,\n                    issues: [],\n                }, ctx);\n                if (result instanceof Promise) {\n                    proms.push(result.then((result) => handleTupleResult(result, payload, i)));\n                }\n                else {\n                    handleTupleResult(result, payload, i);\n                }\n            }\n        }\n        if (proms.length)\n            return Promise.all(proms).then(() => payload);\n        return payload;\n    };\n});\nfunction handleTupleResult(result, final, index) {\n    if (result.issues.length) {\n        final.issues.push(...util.prefixIssues(index, result.issues));\n    }\n    final.value[index] = result.value;\n}\nexport const $ZodRecord = /*@__PURE__*/ core.$constructor(\"$ZodRecord\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, ctx) => {\n        const input = payload.value;\n        if (!util.isPlainObject(input)) {\n            payload.issues.push({\n                expected: \"record\",\n                code: \"invalid_type\",\n                input,\n                inst,\n            });\n            return payload;\n        }\n        const proms = [];\n        if (def.keyType._zod.values) {\n            const values = def.keyType._zod.values;\n            payload.value = {};\n            for (const key of values) {\n                if (typeof key === \"string\" || typeof key === \"number\" || typeof key === \"symbol\") {\n                    const result = def.valueType._zod.run({ value: input[key], issues: [] }, ctx);\n                    if (result instanceof Promise) {\n                        proms.push(result.then((result) => {\n                            if (result.issues.length) {\n                                payload.issues.push(...util.prefixIssues(key, result.issues));\n                            }\n                            payload.value[key] = result.value;\n                        }));\n                    }\n                    else {\n                        if (result.issues.length) {\n                            payload.issues.push(...util.prefixIssues(key, result.issues));\n                        }\n                        payload.value[key] = result.value;\n                    }\n                }\n            }\n            let unrecognized;\n            for (const key in input) {\n                if (!values.has(key)) {\n                    unrecognized = unrecognized ?? [];\n                    unrecognized.push(key);\n                }\n            }\n            if (unrecognized && unrecognized.length > 0) {\n                payload.issues.push({\n                    code: \"unrecognized_keys\",\n                    input,\n                    inst,\n                    keys: unrecognized,\n                });\n            }\n        }\n        else {\n            payload.value = {};\n            for (const key of Reflect.ownKeys(input)) {\n                if (key === \"__proto__\")\n                    continue;\n                const keyResult = def.keyType._zod.run({ value: key, issues: [] }, ctx);\n                if (keyResult instanceof Promise) {\n                    throw new Error(\"Async schemas not supported in object keys currently\");\n                }\n                if (keyResult.issues.length) {\n                    payload.issues.push({\n                        origin: \"record\",\n                        code: \"invalid_key\",\n                        issues: keyResult.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config())),\n                        input: key,\n                        path: [key],\n                        inst,\n                    });\n                    payload.value[keyResult.value] = keyResult.value;\n                    continue;\n                }\n                const result = def.valueType._zod.run({ value: input[key], issues: [] }, ctx);\n                if (result instanceof Promise) {\n                    proms.push(result.then((result) => {\n                        if (result.issues.length) {\n                            payload.issues.push(...util.prefixIssues(key, result.issues));\n                        }\n                        payload.value[keyResult.value] = result.value;\n                    }));\n                }\n                else {\n                    if (result.issues.length) {\n                        payload.issues.push(...util.prefixIssues(key, result.issues));\n                    }\n                    payload.value[keyResult.value] = result.value;\n                }\n            }\n        }\n        if (proms.length) {\n            return Promise.all(proms).then(() => payload);\n        }\n        return payload;\n    };\n});\nexport const $ZodMap = /*@__PURE__*/ core.$constructor(\"$ZodMap\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, ctx) => {\n        const input = payload.value;\n        if (!(input instanceof Map)) {\n            payload.issues.push({\n                expected: \"map\",\n                code: \"invalid_type\",\n                input,\n                inst,\n            });\n            return payload;\n        }\n        const proms = [];\n        payload.value = new Map();\n        for (const [key, value] of input) {\n            const keyResult = def.keyType._zod.run({ value: key, issues: [] }, ctx);\n            const valueResult = def.valueType._zod.run({ value: value, issues: [] }, ctx);\n            if (keyResult instanceof Promise || valueResult instanceof Promise) {\n                proms.push(Promise.all([keyResult, valueResult]).then(([keyResult, valueResult]) => {\n                    handleMapResult(keyResult, valueResult, payload, key, input, inst, ctx);\n                }));\n            }\n            else {\n                handleMapResult(keyResult, valueResult, payload, key, input, inst, ctx);\n            }\n        }\n        if (proms.length)\n            return Promise.all(proms).then(() => payload);\n        return payload;\n    };\n});\nfunction handleMapResult(keyResult, valueResult, final, key, input, inst, ctx) {\n    if (keyResult.issues.length) {\n        if (util.propertyKeyTypes.has(typeof key)) {\n            final.issues.push(...util.prefixIssues(key, keyResult.issues));\n        }\n        else {\n            final.issues.push({\n                origin: \"map\",\n                code: \"invalid_key\",\n                input,\n                inst,\n                issues: keyResult.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config())),\n            });\n        }\n    }\n    if (valueResult.issues.length) {\n        if (util.propertyKeyTypes.has(typeof key)) {\n            final.issues.push(...util.prefixIssues(key, valueResult.issues));\n        }\n        else {\n            final.issues.push({\n                origin: \"map\",\n                code: \"invalid_element\",\n                input,\n                inst,\n                key: key,\n                issues: valueResult.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config())),\n            });\n        }\n    }\n    final.value.set(keyResult.value, valueResult.value);\n}\nexport const $ZodSet = /*@__PURE__*/ core.$constructor(\"$ZodSet\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, ctx) => {\n        const input = payload.value;\n        if (!(input instanceof Set)) {\n            payload.issues.push({\n                input,\n                inst,\n                expected: \"set\",\n                code: \"invalid_type\",\n            });\n            return payload;\n        }\n        const proms = [];\n        payload.value = new Set();\n        for (const item of input) {\n            const result = def.valueType._zod.run({ value: item, issues: [] }, ctx);\n            if (result instanceof Promise) {\n                proms.push(result.then((result) => handleSetResult(result, payload)));\n            }\n            else\n                handleSetResult(result, payload);\n        }\n        if (proms.length)\n            return Promise.all(proms).then(() => payload);\n        return payload;\n    };\n});\nfunction handleSetResult(result, final) {\n    if (result.issues.length) {\n        final.issues.push(...result.issues);\n    }\n    final.value.add(result.value);\n}\nexport const $ZodEnum = /*@__PURE__*/ core.$constructor(\"$ZodEnum\", (inst, def) => {\n    $ZodType.init(inst, def);\n    const values = util.getEnumValues(def.entries);\n    inst._zod.values = new Set(values);\n    inst._zod.pattern = new RegExp(`^(${values\n        .filter((k) => util.propertyKeyTypes.has(typeof k))\n        .map((o) => (typeof o === \"string\" ? util.escapeRegex(o) : o.toString()))\n        .join(\"|\")})$`);\n    inst._zod.parse = (payload, _ctx) => {\n        const input = payload.value;\n        if (inst._zod.values.has(input)) {\n            return payload;\n        }\n        payload.issues.push({\n            code: \"invalid_value\",\n            values,\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodLiteral = /*@__PURE__*/ core.$constructor(\"$ZodLiteral\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.values = new Set(def.values);\n    inst._zod.pattern = new RegExp(`^(${def.values\n        .map((o) => (typeof o === \"string\" ? util.escapeRegex(o) : o ? o.toString() : String(o)))\n        .join(\"|\")})$`);\n    inst._zod.parse = (payload, _ctx) => {\n        const input = payload.value;\n        if (inst._zod.values.has(input)) {\n            return payload;\n        }\n        payload.issues.push({\n            code: \"invalid_value\",\n            values: def.values,\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodFile = /*@__PURE__*/ core.$constructor(\"$ZodFile\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _ctx) => {\n        const input = payload.value;\n        if (input instanceof File)\n            return payload;\n        payload.issues.push({\n            expected: \"file\",\n            code: \"invalid_type\",\n            input,\n            inst,\n        });\n        return payload;\n    };\n});\nexport const $ZodTransform = /*@__PURE__*/ core.$constructor(\"$ZodTransform\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _ctx) => {\n        const _out = def.transform(payload.value, payload);\n        if (_ctx.async) {\n            const output = _out instanceof Promise ? _out : Promise.resolve(_out);\n            return output.then((output) => {\n                payload.value = output;\n                return payload;\n            });\n        }\n        if (_out instanceof Promise) {\n            throw new core.$ZodAsyncError();\n        }\n        payload.value = _out;\n        return payload;\n    };\n});\nexport const $ZodOptional = /*@__PURE__*/ core.$constructor(\"$ZodOptional\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.optin = \"optional\";\n    inst._zod.optout = \"optional\";\n    util.defineLazy(inst._zod, \"values\", () => {\n        return def.innerType._zod.values ? new Set([...def.innerType._zod.values, undefined]) : undefined;\n    });\n    util.defineLazy(inst._zod, \"pattern\", () => {\n        const pattern = def.innerType._zod.pattern;\n        return pattern ? new RegExp(`^(${util.cleanRegex(pattern.source)})?$`) : undefined;\n    });\n    inst._zod.parse = (payload, ctx) => {\n        if (payload.value === undefined) {\n            return payload;\n        }\n        return def.innerType._zod.run(payload, ctx);\n    };\n});\nexport const $ZodNullable = /*@__PURE__*/ core.$constructor(\"$ZodNullable\", (inst, def) => {\n    $ZodType.init(inst, def);\n    util.defineLazy(inst._zod, \"optin\", () => def.innerType._zod.optin);\n    util.defineLazy(inst._zod, \"optout\", () => def.innerType._zod.optout);\n    util.defineLazy(inst._zod, \"pattern\", () => {\n        const pattern = def.innerType._zod.pattern;\n        return pattern ? new RegExp(`^(${util.cleanRegex(pattern.source)}|null)$`) : undefined;\n    });\n    util.defineLazy(inst._zod, \"values\", () => {\n        return def.innerType._zod.values ? new Set([...def.innerType._zod.values, null]) : undefined;\n    });\n    inst._zod.parse = (payload, ctx) => {\n        if (payload.value === null)\n            return payload;\n        return def.innerType._zod.run(payload, ctx);\n    };\n});\nexport const $ZodDefault = /*@__PURE__*/ core.$constructor(\"$ZodDefault\", (inst, def) => {\n    $ZodType.init(inst, def);\n    // inst._zod.qin = \"true\";\n    inst._zod.optin = \"optional\";\n    util.defineLazy(inst._zod, \"values\", () => def.innerType._zod.values);\n    inst._zod.parse = (payload, ctx) => {\n        if (payload.value === undefined) {\n            payload.value = def.defaultValue;\n            /**\n             * $ZodDefault always returns the default value immediately.\n             * It doesn't pass the default value into the validator (\"prefault\"). There's no reason to pass the default value through validation. The validity of the default is enforced by TypeScript statically. Otherwise, it's the responsibility of the user to ensure the default is valid. In the case of pipes with divergent in/out types, you can specify the default on the `in` schema of your ZodPipe to set a \"prefault\" for the pipe.   */\n            return payload;\n        }\n        const result = def.innerType._zod.run(payload, ctx);\n        if (result instanceof Promise) {\n            return result.then((result) => handleDefaultResult(result, def));\n        }\n        return handleDefaultResult(result, def);\n    };\n});\nfunction handleDefaultResult(payload, def) {\n    if (payload.value === undefined) {\n        payload.value = def.defaultValue;\n    }\n    return payload;\n}\nexport const $ZodPrefault = /*@__PURE__*/ core.$constructor(\"$ZodPrefault\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.optin = \"optional\";\n    util.defineLazy(inst._zod, \"values\", () => def.innerType._zod.values);\n    inst._zod.parse = (payload, ctx) => {\n        if (payload.value === undefined) {\n            payload.value = def.defaultValue;\n        }\n        return def.innerType._zod.run(payload, ctx);\n    };\n});\nexport const $ZodNonOptional = /*@__PURE__*/ core.$constructor(\"$ZodNonOptional\", (inst, def) => {\n    $ZodType.init(inst, def);\n    util.defineLazy(inst._zod, \"values\", () => {\n        const v = def.innerType._zod.values;\n        return v ? new Set([...v].filter((x) => x !== undefined)) : undefined;\n    });\n    inst._zod.parse = (payload, ctx) => {\n        const result = def.innerType._zod.run(payload, ctx);\n        if (result instanceof Promise) {\n            return result.then((result) => handleNonOptionalResult(result, inst));\n        }\n        return handleNonOptionalResult(result, inst);\n    };\n});\nfunction handleNonOptionalResult(payload, inst) {\n    if (!payload.issues.length && payload.value === undefined) {\n        payload.issues.push({\n            code: \"invalid_type\",\n            expected: \"nonoptional\",\n            input: payload.value,\n            inst,\n        });\n    }\n    return payload;\n}\nexport const $ZodSuccess = /*@__PURE__*/ core.$constructor(\"$ZodSuccess\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, ctx) => {\n        const result = def.innerType._zod.run(payload, ctx);\n        if (result instanceof Promise) {\n            return result.then((result) => {\n                payload.value = result.issues.length === 0;\n                return payload;\n            });\n        }\n        payload.value = result.issues.length === 0;\n        return payload;\n    };\n});\nexport const $ZodCatch = /*@__PURE__*/ core.$constructor(\"$ZodCatch\", (inst, def) => {\n    $ZodType.init(inst, def);\n    util.defineLazy(inst._zod, \"optin\", () => def.innerType._zod.optin);\n    util.defineLazy(inst._zod, \"optout\", () => def.innerType._zod.optout);\n    util.defineLazy(inst._zod, \"values\", () => def.innerType._zod.values);\n    inst._zod.parse = (payload, ctx) => {\n        const result = def.innerType._zod.run(payload, ctx);\n        if (result instanceof Promise) {\n            return result.then((result) => {\n                payload.value = result.value;\n                if (result.issues.length) {\n                    payload.value = def.catchValue({\n                        ...payload,\n                        error: {\n                            issues: result.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config())),\n                        },\n                        input: payload.value,\n                    });\n                    payload.issues = [];\n                }\n                return payload;\n            });\n        }\n        payload.value = result.value;\n        if (result.issues.length) {\n            payload.value = def.catchValue({\n                ...payload,\n                error: {\n                    issues: result.issues.map((iss) => util.finalizeIssue(iss, ctx, core.config())),\n                },\n                input: payload.value,\n            });\n            payload.issues = [];\n        }\n        return payload;\n    };\n});\nexport const $ZodNaN = /*@__PURE__*/ core.$constructor(\"$ZodNaN\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _ctx) => {\n        if (typeof payload.value !== \"number\" || !Number.isNaN(payload.value)) {\n            payload.issues.push({\n                input: payload.value,\n                inst,\n                expected: \"nan\",\n                code: \"invalid_type\",\n            });\n            return payload;\n        }\n        return payload;\n    };\n});\nexport const $ZodPipe = /*@__PURE__*/ core.$constructor(\"$ZodPipe\", (inst, def) => {\n    $ZodType.init(inst, def);\n    util.defineLazy(inst._zod, \"values\", () => def.in._zod.values);\n    util.defineLazy(inst._zod, \"optin\", () => def.in._zod.optin);\n    util.defineLazy(inst._zod, \"optout\", () => def.out._zod.optout);\n    inst._zod.parse = (payload, ctx) => {\n        const left = def.in._zod.run(payload, ctx);\n        if (left instanceof Promise) {\n            return left.then((left) => handlePipeResult(left, def, ctx));\n        }\n        return handlePipeResult(left, def, ctx);\n    };\n});\nfunction handlePipeResult(left, def, ctx) {\n    if (util.aborted(left)) {\n        return left;\n    }\n    return def.out._zod.run({ value: left.value, issues: left.issues }, ctx);\n}\nexport const $ZodReadonly = /*@__PURE__*/ core.$constructor(\"$ZodReadonly\", (inst, def) => {\n    $ZodType.init(inst, def);\n    util.defineLazy(inst._zod, \"propValues\", () => def.innerType._zod.propValues);\n    util.defineLazy(inst._zod, \"optin\", () => def.innerType._zod.optin);\n    util.defineLazy(inst._zod, \"optout\", () => def.innerType._zod.optout);\n    inst._zod.parse = (payload, ctx) => {\n        const result = def.innerType._zod.run(payload, ctx);\n        if (result instanceof Promise) {\n            return result.then(handleReadonlyResult);\n        }\n        return handleReadonlyResult(result);\n    };\n});\nfunction handleReadonlyResult(payload) {\n    payload.value = Object.freeze(payload.value);\n    return payload;\n}\nexport const $ZodTemplateLiteral = /*@__PURE__*/ core.$constructor(\"$ZodTemplateLiteral\", (inst, def) => {\n    $ZodType.init(inst, def);\n    const regexParts = [];\n    for (const part of def.parts) {\n        if (part instanceof $ZodType) {\n            if (!part._zod.pattern) {\n                // if (!source)\n                throw new Error(`Invalid template literal part, no pattern found: ${[...part._zod.traits].shift()}`);\n            }\n            const source = part._zod.pattern instanceof RegExp ? part._zod.pattern.source : part._zod.pattern;\n            if (!source)\n                throw new Error(`Invalid template literal part: ${part._zod.traits}`);\n            const start = source.startsWith(\"^\") ? 1 : 0;\n            const end = source.endsWith(\"$\") ? source.length - 1 : source.length;\n            regexParts.push(source.slice(start, end));\n        }\n        else if (part === null || util.primitiveTypes.has(typeof part)) {\n            regexParts.push(util.escapeRegex(`${part}`));\n        }\n        else {\n            throw new Error(`Invalid template literal part: ${part}`);\n        }\n    }\n    inst._zod.pattern = new RegExp(`^${regexParts.join(\"\")}$`);\n    inst._zod.parse = (payload, _ctx) => {\n        if (typeof payload.value !== \"string\") {\n            payload.issues.push({\n                input: payload.value,\n                inst,\n                expected: \"template_literal\",\n                code: \"invalid_type\",\n            });\n            return payload;\n        }\n        inst._zod.pattern.lastIndex = 0;\n        if (!inst._zod.pattern.test(payload.value)) {\n            payload.issues.push({\n                input: payload.value,\n                inst,\n                code: \"invalid_format\",\n                format: \"template_literal\",\n                pattern: inst._zod.pattern.source,\n            });\n            return payload;\n        }\n        return payload;\n    };\n});\nexport const $ZodPromise = /*@__PURE__*/ core.$constructor(\"$ZodPromise\", (inst, def) => {\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, ctx) => {\n        return Promise.resolve(payload.value).then((inner) => def.innerType._zod.run({ value: inner, issues: [] }, ctx));\n    };\n});\nexport const $ZodLazy = /*@__PURE__*/ core.$constructor(\"$ZodLazy\", (inst, def) => {\n    $ZodType.init(inst, def);\n    util.defineLazy(inst._zod, \"innerType\", () => def.getter());\n    util.defineLazy(inst._zod, \"pattern\", () => inst._zod.innerType._zod.pattern);\n    util.defineLazy(inst._zod, \"propValues\", () => inst._zod.innerType._zod.propValues);\n    util.defineLazy(inst._zod, \"optin\", () => inst._zod.innerType._zod.optin);\n    util.defineLazy(inst._zod, \"optout\", () => inst._zod.innerType._zod.optout);\n    inst._zod.parse = (payload, ctx) => {\n        const inner = inst._zod.innerType;\n        return inner._zod.run(payload, ctx);\n    };\n});\nexport const $ZodCustom = /*@__PURE__*/ core.$constructor(\"$ZodCustom\", (inst, def) => {\n    checks.$ZodCheck.init(inst, def);\n    $ZodType.init(inst, def);\n    inst._zod.parse = (payload, _) => {\n        return payload;\n    };\n    inst._zod.check = (payload) => {\n        const input = payload.value;\n        const r = def.fn(input);\n        if (r instanceof Promise) {\n            return r.then((r) => handleRefineResult(r, payload, input, inst));\n        }\n        handleRefineResult(r, payload, input, inst);\n        return;\n    };\n});\nfunction handleRefineResult(result, payload, input, inst) {\n    if (!result) {\n        const _iss = {\n            code: \"custom\",\n            input,\n            inst, // incorporates params.error into issue reporting\n            path: [...(inst._zod.def.path ?? [])], // incorporates params.error into issue reporting\n            continue: !inst._zod.def.abort,\n            // params: inst._zod.def.params,\n        };\n        if (inst._zod.def.params)\n            _iss.params = inst._zod.def.params;\n        payload.issues.push(util.issue(_iss));\n    }\n}\n", "export const $output = Symbol(\"ZodOutput\");\nexport const $input = Symbol(\"ZodInput\");\nexport class $ZodRegistry {\n    constructor() {\n        this._map = new WeakMap();\n        this._idmap = new Map();\n    }\n    add(schema, ..._meta) {\n        const meta = _meta[0];\n        this._map.set(schema, meta);\n        if (meta && typeof meta === \"object\" && \"id\" in meta) {\n            if (this._idmap.has(meta.id)) {\n                throw new Error(`ID ${meta.id} already exists in the registry`);\n            }\n            this._idmap.set(meta.id, schema);\n        }\n        return this;\n    }\n    remove(schema) {\n        this._map.delete(schema);\n        return this;\n    }\n    get(schema) {\n        // return this._map.get(schema) as any;\n        // inherit metadata\n        const p = schema._zod.parent;\n        if (p) {\n            const pm = { ...(this.get(p) ?? {}) };\n            delete pm.id; // do not inherit id\n            return { ...pm, ...this._map.get(schema) };\n        }\n        return this._map.get(schema);\n    }\n    has(schema) {\n        return this._map.has(schema);\n    }\n}\n// registries\nexport function registry() {\n    return new $ZodRegistry();\n}\nexport const globalRegistry = /*@__PURE__*/ registry();\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  ResolverError,\n  ResolverSuccess,\n  appendErrors,\n} from 'react-hook-form';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4/core';\n\nconst isZod3Error = (error: any): error is z3.ZodError => {\n  return Array.isArray(error?.issues);\n};\nconst isZod3Schema = (schema: any): schema is z3.ZodSchema => {\n  return (\n    '_def' in schema &&\n    typeof schema._def === 'object' &&\n    'typeName' in schema._def\n  );\n};\nconst isZod4Error = (error: any): error is z4.$ZodError => {\n  // instanceof is safe in Zod 4 (uses Symbol.hasInstance)\n  return error instanceof z4.$ZodError;\n};\nconst isZod4Schema = (schema: any): schema is z4.$ZodType => {\n  return '_zod' in schema && typeof schema._zod === 'object';\n};\n\nfunction parseZod3Issues(\n  zodErrors: z3.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\nfunction parseZod4Issues(\n  zodErrors: z4.$ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  // const _zodErrors = zodErrors as z4.$ZodISsue; //\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if (error.code === 'invalid_union') {\n        const unionError = error.errors[0][0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if (error.code === 'invalid_union') {\n      error.errors.forEach((unionError) =>\n        unionError.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\ntype RawResolverOptions = {\n  mode?: 'async' | 'sync';\n  raw: true;\n};\ntype NonRawResolverOptions = {\n  mode?: 'async' | 'sync';\n  raw?: false;\n};\n\n// minimal interfaces to avoid asssignability issues between versions\ninterface Zod3Type<O = unknown, I = unknown> {\n  _output: O;\n  _input: I;\n  _def: {\n    typeName: string;\n  };\n}\n\n// some type magic to make versions pre-3.25.0 still work\ntype IsUnresolved<T> = PropertyKey extends keyof T ? true : false;\ntype UnresolvedFallback<T, Fallback> = IsUnresolved<typeof z3> extends true\n  ? Fallback\n  : T;\ntype FallbackIssue = {\n  code: string;\n  message: string;\n  path: (string | number)[];\n};\ntype Zod3ParseParams = UnresolvedFallback<\n  z3.ParseParams,\n  // fallback if user is on <3.25.0\n  {\n    path?: (string | number)[];\n    errorMap?: (\n      iss: FallbackIssue,\n      ctx: {\n        defaultError: string;\n        data: any;\n      },\n    ) => { message: string };\n    async?: boolean;\n  }\n>;\ntype Zod4ParseParams = UnresolvedFallback<\n  z4.ParseContext<z4.$ZodIssue>,\n  // fallback if user is on <3.25.0\n  {\n    readonly error?: (\n      iss: FallbackIssue,\n    ) => null | undefined | string | { message: string };\n    readonly reportInput?: boolean;\n    readonly jitless?: boolean;\n  }\n>;\n\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: Zod3Type<Output, Input>,\n  schemaOptions?: Zod3ParseParams,\n  resolverOptions?: NonRawResolverOptions,\n): Resolver<Input, Context, Output>;\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: Zod3Type<Output, Input>,\n  schemaOptions: Zod3ParseParams | undefined,\n  resolverOptions: RawResolverOptions,\n): Resolver<Input, Context, Input>;\n// the Zod 4 overloads need to be generic for complicated reasons\nexport function zodResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n  T extends z4.$ZodType<Output, Input> = z4.$ZodType<Output, Input>,\n>(\n  schema: T,\n  schemaOptions?: Zod4ParseParams, // already partial\n  resolverOptions?: NonRawResolverOptions,\n): Resolver<z4.input<T>, Context, z4.output<T>>;\nexport function zodResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n  T extends z4.$ZodType<Output, Input> = z4.$ZodType<Output, Input>,\n>(\n  schema: z4.$ZodType<Output, Input>,\n  schemaOptions: Zod4ParseParams | undefined, // already partial\n  resolverOptions: RawResolverOptions,\n): Resolver<z4.input<T>, Context, z4.input<T>>;\n/**\n * Creates a resolver function for react-hook-form that validates form data using a Zod schema\n * @param {z3.ZodSchema<Input>} schema - The Zod schema used to validate the form data\n * @param {Partial<z3.ParseParams>} [schemaOptions] - Optional configuration options for Zod parsing\n * @param {Object} [resolverOptions] - Optional resolver-specific configuration\n * @param {('async'|'sync')} [resolverOptions.mode='async'] - Validation mode. Use 'sync' for synchronous validation\n * @param {boolean} [resolverOptions.raw=false] - If true, returns the raw form values instead of the parsed data\n * @returns {Resolver<z3.output<typeof schema>>} A resolver function compatible with react-hook-form\n * @throws {Error} Throws if validation fails with a non-Zod error\n * @example\n * const schema = z3.object({\n *   name: z3.string().min(2),\n *   age: z3.number().min(18)\n * });\n *\n * useForm({\n *   resolver: zodResolver(schema)\n * });\n */\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: object,\n  schemaOptions?: object,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  if (isZod3Schema(schema)) {\n    return async (values: Input, _, options) => {\n      try {\n        const data = await schema[\n          resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n        ](values, schemaOptions);\n\n        options.shouldUseNativeValidation &&\n          validateFieldsNatively({}, options);\n\n        return {\n          errors: {} as FieldErrors,\n          values: resolverOptions.raw ? Object.assign({}, values) : data,\n        } satisfies ResolverSuccess<Output | Input>;\n      } catch (error) {\n        if (isZod3Error(error)) {\n          return {\n            values: {},\n            errors: toNestErrors(\n              parseZod3Issues(\n                error.errors,\n                !options.shouldUseNativeValidation &&\n                  options.criteriaMode === 'all',\n              ),\n              options,\n            ),\n          } satisfies ResolverError<Input>;\n        }\n\n        throw error;\n      }\n    };\n  }\n\n  if (isZod4Schema(schema)) {\n    return async (values: Input, _, options) => {\n      try {\n        const parseFn =\n          resolverOptions.mode === 'sync' ? z4.parse : z4.parseAsync;\n        const data: any = await parseFn(schema, values, schemaOptions);\n\n        options.shouldUseNativeValidation &&\n          validateFieldsNatively({}, options);\n\n        return {\n          errors: {} as FieldErrors,\n          values: resolverOptions.raw ? Object.assign({}, values) : data,\n        } satisfies ResolverSuccess<Output | Input>;\n      } catch (error) {\n        if (isZod4Error(error)) {\n          return {\n            values: {},\n            errors: toNestErrors(\n              parseZod4Issues(\n                error.issues,\n                !options.shouldUseNativeValidation &&\n                  options.criteriaMode === 'all',\n              ),\n              options,\n            ),\n          } satisfies ResolverError<Input>;\n        }\n\n        throw error;\n      }\n    };\n  }\n\n  throw new Error('Invalid input: not a Zod schema');\n}\n"], "mappings": ";;;;;;;;;AASA,IAAMA,IAAoBA,CACxBC,IACAC,IACAC,OAAAA;AAEA,MAAIF,MAAO,oBAAoBA,IAAK;AAClC,UAAMG,KAAQC,IAAIF,IAAQD,EAAAA;AAC1BD,IAAAA,GAAID,kBAAmBI,MAASA,GAAME,WAAY,EAAA,GAElDL,GAAIM,eAAAA;EACN;AAAA;AAVF,IAcaC,IAAyBA,CACpCL,GACAM,OAAAA;AAEA,aAAWP,MAAaO,GAAQC,QAAQ;AACtC,UAAMC,KAAQF,GAAQC,OAAOR,EAAAA;AACzBS,IAAAA,MAASA,GAAMV,OAAO,oBAAoBU,GAAMV,MAClDD,EAAkBW,GAAMV,KAAKC,IAAWC,CAAAA,IAC/BQ,MAASA,GAAMC,QACxBD,GAAMC,KAAKC,QAASZ,CAAAA,OAClBD,EAAkBC,IAAKC,IAAWC,CAAAA,CAAAA;EAGxC;AAAA;AA3BF,ICEaW,IAAeA,CAC1BX,IACAM,OAAAA;AAEAA,EAAAA,GAAQM,6BAA6BP,EAAuBL,IAAQM,EAAAA;AAEpE,QAAMO,KAAc,CAAA;AACpB,aAAWC,MAAQd,IAAQ;AACzB,UAAMQ,IAAQN,IAAII,GAAQC,QAAQO,EAAAA,GAC5Bb,IAAQc,OAAOC,OAAOhB,GAAOc,EAAAA,KAAS,CAAA,GAAI,EAC9ChB,KAAKU,KAASA,EAAMV,IAAAA,CAAAA;AAGtB,QAAImB,EAAmBX,GAAQY,SAASH,OAAOI,KAAKnB,EAAAA,GAASc,EAAAA,GAAO;AAClE,YAAMM,KAAmBL,OAAOC,OAAO,CAAA,GAAId,IAAIW,IAAaC,EAAAA,CAAAA;AAE5DO,UAAID,IAAkB,QAAQnB,CAAAA,GAC9BoB,IAAIR,IAAaC,IAAMM,EAAAA;IACzB,MACEC,KAAIR,IAAaC,IAAMb,CAAAA;EAE3B;AAEA,SAAOY;AAAAA;ADzBT,IC4BMI,IAAqBA,CACzBC,GACAI,OAAAA;AAEA,QAAMR,KAAOS,EAAeD,EAAAA;AAC5B,SAAOJ,EAAMM,KAAMC,CAAAA,OAAMF,EAAeE,EAAAA,EAAGC,MAAM,IAAIZ,EAAAA,SAAAA,CAAAA;AAAc;AAUrE,SAASS,EAAeI,GAAAA;AACtB,SAAOA,EAAMC,QAAQ,UAAU,EAAA;AACjC;;;ACtDgC,SAAS,aAAa,MAAMC,cAAa,QAAQ;AAC7E,WAAS,KAAK,MAAM,KAAK;AACrB,QAAI;AACJ,WAAO,eAAe,MAAM,QAAQ;AAAA,MAChC,OAAO,KAAK,QAAQ,CAAC;AAAA,MACrB,YAAY;AAAA,IAChB,CAAC;AACD,KAAC,KAAK,KAAK,MAAM,WAAW,GAAG,SAAS,oBAAI,IAAI;AAChD,SAAK,KAAK,OAAO,IAAI,IAAI;AACzB,IAAAA,aAAY,MAAM,GAAG;AAErB,eAAW,KAAK,EAAE,WAAW;AACzB,UAAI,EAAE,KAAK;AACP,eAAO,eAAe,MAAM,GAAG,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,IAC3E;AACA,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,MAAM;AAAA,EACpB;AAEA,QAAM,SAAS,QAAQ,UAAU;AAAA,EACjC,MAAM,mBAAmB,OAAO;AAAA,EAChC;AACA,SAAO,eAAe,YAAY,QAAQ,EAAE,OAAO,KAAK,CAAC;AACzD,WAAS,EAAE,KAAK;AACZ,QAAI;AACJ,UAAM,OAAO,QAAQ,SAAS,IAAI,WAAW,IAAI;AACjD,SAAK,MAAM,GAAG;AACd,KAAC,KAAK,KAAK,MAAM,aAAa,GAAG,WAAW,CAAC;AAC7C,eAAW,MAAM,KAAK,KAAK,UAAU;AACjC,SAAG;AAAA,IACP;AACA,WAAO;AAAA,EACX;AACA,SAAO,eAAe,GAAG,QAAQ,EAAE,OAAO,KAAK,CAAC;AAChD,SAAO,eAAe,GAAG,OAAO,aAAa;AAAA,IACzC,OAAO,CAAC,SAAS;AACb,UAAI,QAAQ,UAAU,gBAAgB,OAAO;AACzC,eAAO;AACX,aAAO,MAAM,MAAM,QAAQ,IAAI,IAAI;AAAA,IACvC;AAAA,EACJ,CAAC;AACD,SAAO,eAAe,GAAG,QAAQ,EAAE,OAAO,KAAK,CAAC;AAChD,SAAO;AACX;AAEO,IAAM,SAAS,OAAO,WAAW;AACjC,IAAM,iBAAN,cAA6B,MAAM;AAAA,EACtC,cAAc;AACV,UAAM,0EAA0E;AAAA,EACpF;AACJ;AACO,IAAM,eAAe,CAAC;AACtB,SAAS,OAAO,WAAW;AAC9B,MAAI;AACA,WAAO,OAAO,cAAc,SAAS;AACzC,SAAO;AACX;;;AC5CO,SAAS,cAAc,SAAS;AACnC,QAAM,gBAAgB,OAAO,OAAO,OAAO,EAAE,OAAO,CAAC,MAAM,OAAO,MAAM,QAAQ;AAChF,QAAM,SAAS,OAAO,QAAQ,OAAO,EAChC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,cAAc,QAAQ,CAAC,CAAC,MAAM,EAAE,EACnD,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;AACtB,SAAO;AACX;AAIO,SAAS,sBAAsB,GAAG,OAAO;AAC5C,MAAI,OAAO,UAAU;AACjB,WAAO,MAAM,SAAS;AAC1B,SAAO;AACX;AACO,SAAS,OAAO,QAAQ;AAC3B,QAAMC,OAAM;AACZ,SAAO;AAAA,IACH,IAAI,QAAQ;AACR,UAAI,CAACA,MAAK;AACN,cAAM,QAAQ,OAAO;AACrB,eAAO,eAAe,MAAM,SAAS,EAAE,MAAM,CAAC;AAC9C,eAAO;AAAA,MACX;AACA,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC9C;AAAA,EACJ;AACJ;AACO,SAAS,QAAQ,OAAO;AAC3B,SAAO,UAAU,QAAQ,UAAU;AACvC;AACO,SAAS,WAAW,QAAQ;AAC/B,QAAM,QAAQ,OAAO,WAAW,GAAG,IAAI,IAAI;AAC3C,QAAM,MAAM,OAAO,SAAS,GAAG,IAAI,OAAO,SAAS,IAAI,OAAO;AAC9D,SAAO,OAAO,MAAM,OAAO,GAAG;AAClC;AACO,SAAS,mBAAmB,KAAK,MAAM;AAC1C,QAAM,eAAe,IAAI,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI;AACzD,QAAM,gBAAgB,KAAK,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI;AAC3D,QAAM,WAAW,cAAc,eAAe,cAAc;AAC5D,QAAM,SAAS,OAAO,SAAS,IAAI,QAAQ,QAAQ,EAAE,QAAQ,KAAK,EAAE,CAAC;AACrE,QAAM,UAAU,OAAO,SAAS,KAAK,QAAQ,QAAQ,EAAE,QAAQ,KAAK,EAAE,CAAC;AACvE,SAAQ,SAAS,UAAW,MAAM;AACtC;AACO,SAAS,WAAW,QAAQ,KAAK,QAAQ;AAC5C,QAAMA,OAAM;AACZ,SAAO,eAAe,QAAQ,KAAK;AAAA,IAC/B,MAAM;AACF,UAAI,CAACA,MAAK;AACN,cAAM,QAAQ,OAAO;AACrB,eAAO,GAAG,IAAI;AACd,eAAO;AAAA,MACX;AACA,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC9C;AAAA,IACA,IAAI,GAAG;AACH,aAAO,eAAe,QAAQ,KAAK;AAAA,QAC/B,OAAO;AAAA;AAAA,MAEX,CAAC;AAAA,IAEL;AAAA,IACA,cAAc;AAAA,EAClB,CAAC;AACL;AAyBO,SAAS,aAAa,SAAS,IAAI;AACtC,QAAM,QAAQ;AACd,MAAI,MAAM;AACV,WAASC,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC7B,WAAO,MAAM,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACzD;AACA,SAAO;AACX;AACO,SAAS,IAAI,KAAK;AACrB,SAAO,KAAK,UAAU,GAAG;AAC7B;AACO,IAAM,oBAAoB,MAAM,oBACjC,MAAM,oBACN,IAAI,UAAU;AAAE;AACf,SAAS,SAAS,MAAM;AAC3B,SAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,CAAC,MAAM,QAAQ,IAAI;AAC3E;AACO,IAAM,aAAa,OAAO,MAAM;AACnC,MAAI,OAAO,cAAc,eAAe,WAAW,WAAW,SAAS,YAAY,GAAG;AAClF,WAAO;AAAA,EACX;AACA,MAAI;AACA,UAAM,IAAI;AACV,QAAI,EAAE,EAAE;AACR,WAAO;AAAA,EACX,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACJ,CAAC;AACM,SAAS,cAAcC,IAAG;AAC7B,MAAI,SAASA,EAAC,MAAM;AAChB,WAAO;AAEX,QAAM,OAAOA,GAAE;AACf,MAAI,SAAS;AACT,WAAO;AAEX,QAAM,OAAO,KAAK;AAClB,MAAI,SAAS,IAAI,MAAM;AACnB,WAAO;AAEX,MAAI,OAAO,UAAU,eAAe,KAAK,MAAM,eAAe,MAAM,OAAO;AACvE,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAsDO,IAAM,mBAAmB,oBAAI,IAAI,CAAC,UAAU,UAAU,QAAQ,CAAC;AAC/D,IAAM,iBAAiB,oBAAI,IAAI,CAAC,UAAU,UAAU,UAAU,WAAW,UAAU,WAAW,CAAC;AAC/F,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,QAAQ,uBAAuB,MAAM;AACpD;AAgEO,SAAS,aAAa,OAAO;AAChC,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,MAAM;AACpC,WAAO,MAAM,CAAC,EAAE,KAAK,UAAU,cAAc,MAAM,CAAC,EAAE,KAAK,WAAW;AAAA,EAC1E,CAAC;AACL;AACO,IAAM,uBAAuB;AAAA,EAChC,SAAS,CAAC,OAAO,kBAAkB,OAAO,gBAAgB;AAAA,EAC1D,OAAO,CAAC,aAAa,UAAU;AAAA,EAC/B,QAAQ,CAAC,GAAG,UAAU;AAAA,EACtB,SAAS,CAAC,uBAAwB,oBAAqB;AAAA,EACvD,SAAS,CAAC,CAAC,OAAO,WAAW,OAAO,SAAS;AACjD;AACO,IAAM,uBAAuB;AAAA,EAChC,OAAO,CAAgB,OAAO,sBAAsB,GAAkB,OAAO,qBAAqB,CAAC;AAAA,EACnG,QAAQ,CAAgB,OAAO,CAAC,GAAkB,OAAO,sBAAsB,CAAC;AACpF;AA+HO,SAAS,QAAQ,GAAG,aAAa,GAAG;AACvC,WAASC,KAAI,YAAYA,KAAI,EAAE,OAAO,QAAQA,MAAK;AAC/C,QAAI,EAAE,OAAOA,EAAC,GAAG,aAAa;AAC1B,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACO,SAAS,aAAa,MAAM,QAAQ;AACvC,SAAO,OAAO,IAAI,CAAC,QAAQ;AACvB,QAAI;AACJ,KAAC,KAAK,KAAK,SAAS,GAAG,OAAO,CAAC;AAC/B,QAAI,KAAK,QAAQ,IAAI;AACrB,WAAO;AAAA,EACX,CAAC;AACL;AACO,SAAS,cAAc,SAAS;AACnC,SAAO,OAAO,YAAY,WAAW,UAAU,SAAS;AAC5D;AACO,SAAS,cAAc,KAAK,KAAKC,SAAQ;AAC5C,QAAM,OAAO,EAAE,GAAG,KAAK,MAAM,IAAI,QAAQ,CAAC,EAAE;AAE5C,MAAI,CAAC,IAAI,SAAS;AACd,UAAM,UAAU,cAAc,IAAI,MAAM,KAAK,KAAK,QAAQ,GAAG,CAAC,KAC1D,cAAc,KAAK,QAAQ,GAAG,CAAC,KAC/B,cAAcA,QAAO,cAAc,GAAG,CAAC,KACvC,cAAcA,QAAO,cAAc,GAAG,CAAC,KACvC;AACJ,SAAK,UAAU;AAAA,EACnB;AAEA,SAAO,KAAK;AACZ,SAAO,KAAK;AACZ,MAAI,CAAC,KAAK,aAAa;AACnB,WAAO,KAAK;AAAA,EAChB;AACA,SAAO;AACX;AACO,SAAS,iBAAiB,OAAO;AACpC,MAAI,iBAAiB;AACjB,WAAO;AACX,MAAI,iBAAiB;AACjB,WAAO;AACX,MAAI,iBAAiB;AACjB,WAAO;AACX,SAAO;AACX;AACO,SAAS,oBAAoB,OAAO;AACvC,MAAI,MAAM,QAAQ,KAAK;AACnB,WAAO;AACX,MAAI,OAAO,UAAU;AACjB,WAAO;AACX,SAAO;AACX;AACO,SAAS,SAAS,MAAM;AAC3B,QAAM,CAAC,KAAK,OAAO,IAAI,IAAI;AAC3B,MAAI,OAAO,QAAQ,UAAU;AACzB,WAAO;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,GAAG,IAAI;AACpB;;;ACzdA,IAAM,cAAc,CAAC,MAAM,QAAQ;AAC/B,OAAK,OAAO;AACZ,SAAO,eAAe,MAAM,QAAQ;AAAA,IAChC,OAAO,KAAK;AAAA,IACZ,YAAY;AAAA,EAChB,CAAC;AACD,SAAO,eAAe,MAAM,UAAU;AAAA,IAClC,OAAO;AAAA,IACP,YAAY;AAAA,EAChB,CAAC;AACD,SAAO,eAAe,MAAM,WAAW;AAAA,IACnC,MAAM;AACF,aAAO,KAAK,UAAU,KAAU,uBAAuB,CAAC;AAAA,IAC5D;AAAA,IACA,YAAY;AAAA;AAAA,EAEhB,CAAC;AACL;AACO,IAAM,YAAY,aAAa,aAAa,WAAW;AACvD,IAAM,gBAAgB,aAAa,aAAa,aAAa,EAAE,QAAQ,MAAM,CAAC;;;AClB9E,IAAM,SAAS,CAAC,SAAS,CAAC,QAAQ,OAAO,MAAM,YAAY;AAC9D,QAAM,MAAM,OAAO,OAAO,OAAO,MAAM,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE,OAAO,MAAM;AAC1E,QAAM,SAAS,OAAO,KAAK,IAAI,EAAE,OAAO,QAAQ,CAAC,EAAE,GAAG,GAAG;AACzD,MAAI,kBAAkB,SAAS;AAC3B,UAAM,IAAS,eAAe;AAAA,EAClC;AACA,MAAI,OAAO,OAAO,QAAQ;AACtB,UAAM,IAAI,KAAK,SAAS,OAAO,MAAM,OAAO,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC,CAAC;AAC5G,IAAK,kBAAkB,GAAG,SAAS,MAAM;AACzC,UAAM;AAAA,EACV;AACA,SAAO,OAAO;AAClB;AACO,IAAM,QAAuB,OAAc,aAAa;AACxD,IAAM,cAAc,CAAC,SAAS,OAAO,QAAQ,OAAO,MAAM,WAAW;AACxE,QAAM,MAAM,OAAO,OAAO,OAAO,MAAM,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,KAAK;AACxE,MAAI,SAAS,OAAO,KAAK,IAAI,EAAE,OAAO,QAAQ,CAAC,EAAE,GAAG,GAAG;AACvD,MAAI,kBAAkB;AAClB,aAAS,MAAM;AACnB,MAAI,OAAO,OAAO,QAAQ;AACtB,UAAM,IAAI,KAAK,QAAQ,OAAO,MAAM,OAAO,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC,CAAC;AAC3G,IAAK,kBAAkB,GAAG,QAAQ,MAAM;AACxC,UAAM;AAAA,EACV;AACA,SAAO,OAAO;AAClB;AACO,IAAM,aAA4B,YAAmB,aAAa;AAClE,IAAM,aAAa,CAAC,SAAS,CAAC,QAAQ,OAAO,SAAS;AACzD,QAAM,MAAM,OAAO,EAAE,GAAG,MAAM,OAAO,MAAM,IAAI,EAAE,OAAO,MAAM;AAC9D,QAAM,SAAS,OAAO,KAAK,IAAI,EAAE,OAAO,QAAQ,CAAC,EAAE,GAAG,GAAG;AACzD,MAAI,kBAAkB,SAAS;AAC3B,UAAM,IAAS,eAAe;AAAA,EAClC;AACA,SAAO,OAAO,OAAO,SACf;AAAA,IACE,SAAS;AAAA,IACT,OAAO,KAAK,QAAe,WAAW,OAAO,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC,CAAC;AAAA,EACjH,IACE,EAAE,SAAS,MAAM,MAAM,OAAO,MAAM;AAC9C;AACO,IAAM,YAA2B,WAAkB,aAAa;AAChE,IAAM,kBAAkB,CAAC,SAAS,OAAO,QAAQ,OAAO,SAAS;AACpE,QAAM,MAAM,OAAO,OAAO,OAAO,MAAM,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,KAAK;AACxE,MAAI,SAAS,OAAO,KAAK,IAAI,EAAE,OAAO,QAAQ,CAAC,EAAE,GAAG,GAAG;AACvD,MAAI,kBAAkB;AAClB,aAAS,MAAM;AACnB,SAAO,OAAO,OAAO,SACf;AAAA,IACE,SAAS;AAAA,IACT,OAAO,IAAI,KAAK,OAAO,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC,CAAC;AAAA,EAC3F,IACE,EAAE,SAAS,MAAM,MAAM,OAAO,MAAM;AAC9C;AACO,IAAM,iBAAgC,gBAAuB,aAAa;;;ACxD1E,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,SAAS;AAEf,IAAM,WAAW;AAIjB,IAAM,OAAO;AAIb,IAAM,OAAO,CAACC,aAAY;AAC7B,MAAI,CAACA;AACD,WAAO;AACX,SAAO,IAAI,OAAO,mCAAmCA,QAAO,yDAAyD;AACzH;AACO,IAAM,QAAsB,KAAK,CAAC;AAClC,IAAM,QAAsB,KAAK,CAAC;AAClC,IAAM,QAAsB,KAAK,CAAC;AAElC,IAAM,QAAQ;AASd,IAAM,SAAS;AACf,SAAS,QAAQ;AACpB,SAAO,IAAI,OAAO,QAAQ,GAAG;AACjC;AACO,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAM,SAAS;AAEf,IAAM,SAAS;AACf,IAAM,YAAY;AAIlB,IAAM,WAAW;AAGjB,IAAM,OAAO;AAEpB,IAAM,aAAa;AACZ,IAAM,OAAqB,IAAI,OAAO,IAAI,UAAU,GAAG;AAC9D,SAAS,WAAW,MAAM;AACtB,QAAM,OAAO;AACb,QAAM,QAAQ,OAAO,KAAK,cAAc,WAClC,KAAK,cAAc,KACf,GAAG,IAAI,KACP,KAAK,cAAc,IACf,GAAG,IAAI,cACP,GAAG,IAAI,mBAAmB,KAAK,SAAS,MAChD,GAAG,IAAI;AACb,SAAO;AACX;AACO,SAAS,KAAK,MAAM;AACvB,SAAO,IAAI,OAAO,IAAI,WAAW,IAAI,CAAC,GAAG;AAC7C;AAEO,SAAS,SAAS,MAAM;AAC3B,QAAMC,QAAO,WAAW,EAAE,WAAW,KAAK,UAAU,CAAC;AACrD,QAAM,OAAO,CAAC,GAAG;AACjB,MAAI,KAAK;AACL,SAAK,KAAK,EAAE;AAChB,MAAI,KAAK;AACL,SAAK,KAAK,qBAAqB;AACnC,QAAM,YAAY,GAAGA,KAAI,MAAM,KAAK,KAAK,GAAG,CAAC;AAC7C,SAAO,IAAI,OAAO,IAAI,UAAU,OAAO,SAAS,IAAI;AACxD;AACO,IAAM,SAAS,CAAC,WAAW;AAC9B,QAAM,QAAQ,SAAS,YAAY,QAAQ,WAAW,CAAC,IAAI,QAAQ,WAAW,EAAE,MAAM;AACtF,SAAO,IAAI,OAAO,IAAI,KAAK,GAAG;AAClC;AACO,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,UAAU;AACvB,IAAM,QAAQ;AAEd,IAAM,aAAa;AAGZ,IAAM,YAAY;AAElB,IAAM,YAAY;;;AC1FlB,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,MAAI;AACJ,OAAK,SAAS,KAAK,OAAO,CAAC;AAC3B,OAAK,KAAK,MAAM;AAChB,GAAC,KAAK,KAAK,MAAM,aAAa,GAAG,WAAW,CAAC;AACjD,CAAC;AACD,IAAM,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACZ;AACO,IAAM,oBAAuC,aAAa,qBAAqB,CAAC,MAAM,QAAQ;AACjG,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,SAAS,iBAAiB,OAAO,IAAI,KAAK;AAChD,OAAK,KAAK,SAAS,KAAK,CAACC,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,UAAM,QAAQ,IAAI,YAAY,IAAI,UAAU,IAAI,qBAAqB,OAAO;AAC5E,QAAI,IAAI,QAAQ,MAAM;AAClB,UAAI,IAAI;AACJ,YAAI,UAAU,IAAI;AAAA;AAElB,YAAI,mBAAmB,IAAI;AAAA,IACnC;AAAA,EACJ,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,IAAI,YAAY,QAAQ,SAAS,IAAI,QAAQ,QAAQ,QAAQ,IAAI,OAAO;AACxE;AAAA,IACJ;AACA,YAAQ,OAAO,KAAK;AAAA,MAChB;AAAA,MACA,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb,OAAO,QAAQ;AAAA,MACf,WAAW,IAAI;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,uBAA0C,aAAa,wBAAwB,CAAC,MAAM,QAAQ;AACvG,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,SAAS,iBAAiB,OAAO,IAAI,KAAK;AAChD,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,UAAM,QAAQ,IAAI,YAAY,IAAI,UAAU,IAAI,qBAAqB,OAAO;AAC5E,QAAI,IAAI,QAAQ,MAAM;AAClB,UAAI,IAAI;AACJ,YAAI,UAAU,IAAI;AAAA;AAElB,YAAI,mBAAmB,IAAI;AAAA,IACnC;AAAA,EACJ,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,IAAI,YAAY,QAAQ,SAAS,IAAI,QAAQ,QAAQ,QAAQ,IAAI,OAAO;AACxE;AAAA,IACJ;AACA,YAAQ,OAAO,KAAK;AAAA,MAChB;AAAA,MACA,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb,OAAO,QAAQ;AAAA,MACf,WAAW,IAAI;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,sBACM,aAAa,uBAAuB,CAAC,MAAM,QAAQ;AAClE,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,QAAI;AACJ,KAAC,KAAKA,MAAK,KAAK,KAAK,eAAe,GAAG,aAAa,IAAI;AAAA,EAC5D,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,OAAO,QAAQ,UAAU,OAAO,IAAI;AACpC,YAAM,IAAI,MAAM,oDAAoD;AACxE,UAAM,aAAa,OAAO,QAAQ,UAAU,WACtC,QAAQ,QAAQ,IAAI,UAAU,OAAO,CAAC,IACjC,mBAAmB,QAAQ,OAAO,IAAI,KAAK,MAAM;AAC5D,QAAI;AACA;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAQ,OAAO,QAAQ;AAAA,MACvB,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,wBAA2C,aAAa,yBAAyB,CAAC,MAAM,QAAQ;AACzG,YAAU,KAAK,MAAM,GAAG;AACxB,MAAI,SAAS,IAAI,UAAU;AAC3B,QAAM,QAAQ,IAAI,QAAQ,SAAS,KAAK;AACxC,QAAM,SAAS,QAAQ,QAAQ;AAC/B,QAAM,CAAC,SAAS,OAAO,IAAS,qBAAqB,IAAI,MAAM;AAC/D,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,SAAS,IAAI;AACjB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI;AACA,UAAI,UAAkB;AAAA,EAC9B,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,UAAU,KAAK,GAAG;AAU1B,gBAAQ,OAAO,KAAK;AAAA,UAChB,UAAU;AAAA,UACV,QAAQ,IAAI;AAAA,UACZ,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACJ,CAAC;AACD;AAAA,MASJ;AACA,UAAI,CAAC,OAAO,cAAc,KAAK,GAAG;AAC9B,YAAI,QAAQ,GAAG;AAEX,kBAAQ,OAAO,KAAK;AAAA,YAChB;AAAA,YACA,MAAM;AAAA,YACN,SAAS,OAAO;AAAA,YAChB,MAAM;AAAA,YACN;AAAA,YACA;AAAA,YACA,UAAU,CAAC,IAAI;AAAA,UACnB,CAAC;AAAA,QACL,OACK;AAED,kBAAQ,OAAO,KAAK;AAAA,YAChB;AAAA,YACA,MAAM;AAAA,YACN,SAAS,OAAO;AAAA,YAChB,MAAM;AAAA,YACN;AAAA,YACA;AAAA,YACA,UAAU,CAAC,IAAI;AAAA,UACnB,CAAC;AAAA,QACL;AACA;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,QAAQ,SAAS;AACjB,cAAQ,OAAO,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA,UAAU,CAAC,IAAI;AAAA,MACnB,CAAC;AAAA,IACL;AACA,QAAI,QAAQ,SAAS;AACjB,cAAQ,OAAO,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;AACM,IAAM,wBAA2C,aAAa,yBAAyB,CAAC,MAAM,QAAQ;AACzG,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,CAAC,SAAS,OAAO,IAAS,qBAAqB,IAAI,MAAM;AAC/D,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,SAAS,IAAI;AACjB,QAAI,UAAU;AACd,QAAI,UAAU;AAAA,EAClB,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,QAAI,QAAQ,SAAS;AACjB,cAAQ,OAAO,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA,UAAU,CAAC,IAAI;AAAA,MACnB,CAAC;AAAA,IACL;AACA,QAAI,QAAQ,SAAS;AACjB,cAAQ,OAAO,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;AACM,IAAM,mBAAsC,aAAa,oBAAoB,CAAC,MAAM,QAAQ;AAC/F,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,OAAO,CAAC,YAAY;AAC1B,UAAM,MAAM,QAAQ;AACpB,WAAO,CAAM,QAAQ,GAAG,KAAK,IAAI,SAAS;AAAA,EAC9C;AACA,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,OAAQA,MAAK,KAAK,IAAI,WAAW,OAAO;AAC9C,QAAI,IAAI,UAAU;AACd,MAAAA,MAAK,KAAK,IAAI,UAAU,IAAI;AAAA,EACpC,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,UAAM,OAAO,MAAM;AACnB,QAAI,QAAQ,IAAI;AACZ;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAa,iBAAiB,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,mBAAsC,aAAa,oBAAoB,CAAC,MAAM,QAAQ;AAC/F,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,OAAO,CAAC,YAAY;AAC1B,UAAM,MAAM,QAAQ;AACpB,WAAO,CAAM,QAAQ,GAAG,KAAK,IAAI,SAAS;AAAA,EAC9C;AACA,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,OAAQA,MAAK,KAAK,IAAI,WAAW,OAAO;AAC9C,QAAI,IAAI,UAAU;AACd,MAAAA,MAAK,KAAK,IAAI,UAAU,IAAI;AAAA,EACpC,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,UAAM,OAAO,MAAM;AACnB,QAAI,QAAQ,IAAI;AACZ;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAa,iBAAiB,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,sBAAyC,aAAa,uBAAuB,CAAC,MAAM,QAAQ;AACrG,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,OAAO,CAAC,YAAY;AAC1B,UAAM,MAAM,QAAQ;AACpB,WAAO,CAAM,QAAQ,GAAG,KAAK,IAAI,SAAS;AAAA,EAC9C;AACA,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,UAAU,IAAI;AAClB,QAAI,UAAU,IAAI;AAClB,QAAI,OAAO,IAAI;AAAA,EACnB,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,UAAM,OAAO,MAAM;AACnB,QAAI,SAAS,IAAI;AACb;AACJ,UAAM,SAAS,OAAO,IAAI;AAC1B,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAa,iBAAiB,KAAK;AAAA,MACnC,GAAI,SAAS,EAAE,MAAM,WAAW,SAAS,IAAI,KAAK,IAAI,EAAE,MAAM,aAAa,SAAS,IAAI,KAAK;AAAA,MAC7F,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,qBAAwC,aAAa,sBAAsB,CAAC,MAAM,QAAQ;AACnG,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,OAAO,CAAC,YAAY;AAC1B,UAAM,MAAM,QAAQ;AACpB,WAAO,CAAM,QAAQ,GAAG,KAAK,IAAI,WAAW;AAAA,EAChD;AACA,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,OAAQA,MAAK,KAAK,IAAI,WAAW,OAAO;AAC9C,QAAI,IAAI,UAAU;AACd,MAAAA,MAAK,KAAK,IAAI,UAAU,IAAI;AAAA,EACpC,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAS,MAAM;AACrB,QAAI,UAAU,IAAI;AACd;AACJ,UAAM,SAAc,oBAAoB,KAAK;AAC7C,YAAQ,OAAO,KAAK;AAAA,MAChB;AAAA,MACA,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,qBAAwC,aAAa,sBAAsB,CAAC,MAAM,QAAQ;AACnG,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,OAAO,CAAC,YAAY;AAC1B,UAAM,MAAM,QAAQ;AACpB,WAAO,CAAM,QAAQ,GAAG,KAAK,IAAI,WAAW;AAAA,EAChD;AACA,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,OAAQA,MAAK,KAAK,IAAI,WAAW,OAAO;AAC9C,QAAI,IAAI,UAAU;AACd,MAAAA,MAAK,KAAK,IAAI,UAAU,IAAI;AAAA,EACpC,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAS,MAAM;AACrB,QAAI,UAAU,IAAI;AACd;AACJ,UAAM,SAAc,oBAAoB,KAAK;AAC7C,YAAQ,OAAO,KAAK;AAAA,MAChB;AAAA,MACA,MAAM;AAAA,MACN,SAAS,IAAI;AAAA,MACb,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,wBAA2C,aAAa,yBAAyB,CAAC,MAAM,QAAQ;AACzG,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,OAAO,CAAC,YAAY;AAC1B,UAAM,MAAM,QAAQ;AACpB,WAAO,CAAM,QAAQ,GAAG,KAAK,IAAI,WAAW;AAAA,EAChD;AACA,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,UAAU,IAAI;AAClB,QAAI,UAAU,IAAI;AAClB,QAAI,SAAS,IAAI;AAAA,EACrB,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAS,MAAM;AACrB,QAAI,WAAW,IAAI;AACf;AACJ,UAAM,SAAc,oBAAoB,KAAK;AAC7C,UAAM,SAAS,SAAS,IAAI;AAC5B,YAAQ,OAAO,KAAK;AAAA,MAChB;AAAA,MACA,GAAI,SACE,EAAE,MAAM,WAAW,SAAS,IAAI,QAAQ,OAAO,KAAK,IACpD,EAAE,MAAM,aAAa,SAAS,IAAI,QAAQ,OAAO,KAAK;AAAA,MAC5D,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,wBAA2C,aAAa,yBAAyB,CAAC,MAAM,QAAQ;AACzG,MAAI,IAAI;AACR,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,SAAS,IAAI;AACjB,QAAI,IAAI,SAAS;AACb,UAAI,aAAa,IAAI,WAAW,oBAAI,IAAI;AACxC,UAAI,SAAS,IAAI,IAAI,OAAO;AAAA,IAChC;AAAA,EACJ,CAAC;AACD,MAAI,IAAI;AACJ,KAAC,KAAK,KAAK,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY;AAC/C,UAAI,QAAQ,YAAY;AACxB,UAAI,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAC9B;AACJ,cAAQ,OAAO,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ,IAAI;AAAA,QACZ,OAAO,QAAQ;AAAA,QACf,GAAI,IAAI,UAAU,EAAE,SAAS,IAAI,QAAQ,SAAS,EAAE,IAAI,CAAC;AAAA,QACzD;AAAA,QACA,UAAU,CAAC,IAAI;AAAA,MACnB,CAAC;AAAA,IACL;AAAA;AAEA,KAAC,KAAK,KAAK,MAAM,UAAU,GAAG,QAAQ,MAAM;AAAA,IAAE;AACtD,CAAC;AACM,IAAM,iBAAoC,aAAa,kBAAkB,CAAC,MAAM,QAAQ;AAC3F,wBAAsB,KAAK,MAAM,GAAG;AACpC,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,QAAQ,YAAY;AACxB,QAAI,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAC9B;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO,QAAQ;AAAA,MACf,SAAS,IAAI,QAAQ,SAAS;AAAA,MAC9B;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,qBAAwC,aAAa,sBAAsB,CAAC,MAAM,QAAQ;AACnG,MAAI,YAAY,IAAI,UAAkB;AACtC,wBAAsB,KAAK,MAAM,GAAG;AACxC,CAAC;AACM,IAAM,qBAAwC,aAAa,sBAAsB,CAAC,MAAM,QAAQ;AACnG,MAAI,YAAY,IAAI,UAAkB;AACtC,wBAAsB,KAAK,MAAM,GAAG;AACxC,CAAC;AACM,IAAM,oBAAuC,aAAa,qBAAqB,CAAC,MAAM,QAAQ;AACjG,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,eAAoB,YAAY,IAAI,QAAQ;AAClD,QAAM,UAAU,IAAI,OAAO,OAAO,IAAI,aAAa,WAAW,MAAM,IAAI,QAAQ,IAAI,YAAY,KAAK,YAAY;AACjH,MAAI,UAAU;AACd,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,aAAa,IAAI,WAAW,oBAAI,IAAI;AACxC,QAAI,SAAS,IAAI,OAAO;AAAA,EAC5B,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,QAAQ,MAAM,SAAS,IAAI,UAAU,IAAI,QAAQ;AACjD;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU,IAAI;AAAA,MACd,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,sBAAyC,aAAa,uBAAuB,CAAC,MAAM,QAAQ;AACrG,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,UAAU,IAAI,OAAO,IAAS,YAAY,IAAI,MAAM,CAAC,IAAI;AAC/D,MAAI,YAAY,IAAI,UAAU;AAC9B,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,aAAa,IAAI,WAAW,oBAAI,IAAI;AACxC,QAAI,SAAS,IAAI,OAAO;AAAA,EAC5B,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,QAAQ,MAAM,WAAW,IAAI,MAAM;AACnC;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ,IAAI;AAAA,MACZ,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,oBAAuC,aAAa,qBAAqB,CAAC,MAAM,QAAQ;AACjG,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,UAAU,IAAI,OAAO,KAAU,YAAY,IAAI,MAAM,CAAC,GAAG;AAC/D,MAAI,YAAY,IAAI,UAAU;AAC9B,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,aAAa,IAAI,WAAW,oBAAI,IAAI;AACxC,QAAI,SAAS,IAAI,OAAO;AAAA,EAC5B,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,QAAQ,MAAM,SAAS,IAAI,MAAM;AACjC;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ,IAAI;AAAA,MACZ,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AAID,SAAS,0BAA0B,QAAQ,SAAS,UAAU;AAC1D,MAAI,OAAO,OAAO,QAAQ;AACtB,YAAQ,OAAO,KAAK,GAAQ,aAAa,UAAU,OAAO,MAAM,CAAC;AAAA,EACrE;AACJ;AACO,IAAM,oBAAuC,aAAa,qBAAqB,CAAC,MAAM,QAAQ;AACjG,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,SAAS,IAAI,OAAO,KAAK,IAAI;AAAA,MAC/B,OAAO,QAAQ,MAAM,IAAI,QAAQ;AAAA,MACjC,QAAQ,CAAC;AAAA,IACb,GAAG,CAAC,CAAC;AACL,QAAI,kBAAkB,SAAS;AAC3B,aAAO,OAAO,KAAK,CAACC,YAAW,0BAA0BA,SAAQ,SAAS,IAAI,QAAQ,CAAC;AAAA,IAC3F;AACA,8BAA0B,QAAQ,SAAS,IAAI,QAAQ;AACvD;AAAA,EACJ;AACJ,CAAC;AACM,IAAM,oBAAuC,aAAa,qBAAqB,CAAC,MAAM,QAAQ;AACjG,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,UAAU,IAAI,IAAI,IAAI,IAAI;AAChC,OAAK,KAAK,SAAS,KAAK,CAACD,UAAS;AAC9B,IAAAA,MAAK,KAAK,IAAI,OAAO,IAAI;AAAA,EAC7B,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,QAAQ,IAAI,QAAQ,MAAM,IAAI;AAC9B;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,QAAQ,IAAI;AAAA,MACZ,OAAO,QAAQ,MAAM;AAAA,MACrB;AAAA,IACJ,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,qBAAwC,aAAa,sBAAsB,CAAC,MAAM,QAAQ;AACnG,YAAU,KAAK,MAAM,GAAG;AACxB,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,YAAQ,QAAQ,IAAI,GAAG,QAAQ,KAAK;AAAA,EACxC;AACJ,CAAC;;;AC5iBM,IAAM,MAAN,MAAU;AAAA,EACb,YAAY,OAAO,CAAC,GAAG;AACnB,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS;AACd,QAAI;AACA,WAAK,OAAO;AAAA,EACpB;AAAA,EACA,SAAS,IAAI;AACT,SAAK,UAAU;AACf,OAAG,IAAI;AACP,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,OAAO,QAAQ,YAAY;AAC3B,UAAI,MAAM,EAAE,WAAW,OAAO,CAAC;AAC/B,UAAI,MAAM,EAAE,WAAW,QAAQ,CAAC;AAChC;AAAA,IACJ;AACA,UAAM,UAAU;AAChB,UAAM,QAAQ,QAAQ,MAAM,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC;AACjD,UAAM,YAAY,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC;AAC/E,UAAM,WAAW,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC,EAAE,IAAI,CAAC,MAAM,IAAI,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC;AAChG,eAAW,QAAQ,UAAU;AACzB,WAAK,QAAQ,KAAK,IAAI;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,UAAU;AACN,UAAM,IAAI;AACV,UAAM,OAAO,MAAM;AACnB,UAAM,UAAU,MAAM,WAAW,CAAC,EAAE;AACpC,UAAM,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;AAE9C,WAAO,IAAI,EAAE,GAAG,MAAM,MAAM,KAAK,IAAI,CAAC;AAAA,EAC1C;AACJ;;;AClCO,IAAM,UAAU;AAAA,EACnB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACX;;;ACGO,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI;AACJ,WAAS,OAAO,CAAC;AAEjB,EAAK,WAAW,KAAK,MAAM,MAAM,MAAM,IAAI,OAAO,MAAW,aAAa,EAAE,CAAC;AAC7E,OAAK,KAAK,MAAM;AAChB,OAAK,KAAK,MAAM,KAAK,KAAK,OAAO,CAAC;AAClC,OAAK,KAAK,UAAU;AACpB,QAAM,SAAS,CAAC,GAAI,KAAK,KAAK,IAAI,UAAU,CAAC,CAAE;AAE/C,MAAI,KAAK,KAAK,OAAO,IAAI,WAAW,GAAG;AACnC,WAAO,QAAQ,IAAI;AAAA,EACvB;AAEA,aAAW,MAAM,QAAQ;AACrB,eAAW,MAAM,GAAG,KAAK,UAAU;AAC/B,SAAG,IAAI;AAAA,IACX;AAAA,EACJ;AACA,MAAI,OAAO,WAAW,GAAG;AAGrB,KAAC,KAAK,KAAK,MAAM,aAAa,GAAG,WAAW,CAAC;AAC7C,SAAK,KAAK,UAAU,KAAK,MAAM;AAC3B,WAAK,KAAK,MAAM,KAAK,KAAK;AAAA,IAC9B,CAAC;AAAA,EACL,OACK;AACD,UAAM,YAAY,CAAC,SAASE,SAAQ,QAAQ;AACxC,UAAI,YAAiB,QAAQ,OAAO;AACpC,UAAI;AACJ,iBAAW,MAAMA,SAAQ;AACrB,YAAI,GAAG,KAAK,MAAM;AACd,gBAAM,YAAY,GAAG,KAAK,KAAK,OAAO;AACtC,cAAI,CAAC;AACD;AAAA,QACR,WACS,WAAW;AAChB;AAAA,QACJ;AACA,cAAM,UAAU,QAAQ,OAAO;AAC/B,cAAM,IAAI,GAAG,KAAK,MAAM,OAAO;AAC/B,YAAI,aAAa,WAAW,KAAK,UAAU,OAAO;AAC9C,gBAAM,IAAS,eAAe;AAAA,QAClC;AACA,YAAI,eAAe,aAAa,SAAS;AACrC,yBAAe,eAAe,QAAQ,QAAQ,GAAG,KAAK,YAAY;AAC9D,kBAAM;AACN,kBAAM,UAAU,QAAQ,OAAO;AAC/B,gBAAI,YAAY;AACZ;AACJ,gBAAI,CAAC;AACD,0BAAiB,QAAQ,SAAS,OAAO;AAAA,UACjD,CAAC;AAAA,QACL,OACK;AACD,gBAAM,UAAU,QAAQ,OAAO;AAC/B,cAAI,YAAY;AACZ;AACJ,cAAI,CAAC;AACD,wBAAiB,QAAQ,SAAS,OAAO;AAAA,QACjD;AAAA,MACJ;AACA,UAAI,aAAa;AACb,eAAO,YAAY,KAAK,MAAM;AAC1B,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AACA,SAAK,KAAK,MAAM,CAAC,SAAS,QAAQ;AAC9B,YAAM,SAAS,KAAK,KAAK,MAAM,SAAS,GAAG;AAC3C,UAAI,kBAAkB,SAAS;AAC3B,YAAI,IAAI,UAAU;AACd,gBAAM,IAAS,eAAe;AAClC,eAAO,OAAO,KAAK,CAACC,YAAW,UAAUA,SAAQ,QAAQ,GAAG,CAAC;AAAA,MACjE;AACA,aAAO,UAAU,QAAQ,QAAQ,GAAG;AAAA,IACxC;AAAA,EACJ;AACA,OAAK,WAAW,IAAI;AAAA,IAChB,UAAU,CAAC,UAAU;AACjB,UAAI;AACA,cAAMC,KAAI,UAAU,MAAM,KAAK;AAC/B,eAAOA,GAAE,UAAU,EAAE,OAAOA,GAAE,KAAK,IAAI,EAAE,QAAQA,GAAE,OAAO,OAAO;AAAA,MACrE,SACO,GAAG;AACN,eAAO,eAAe,MAAM,KAAK,EAAE,KAAK,CAACA,OAAOA,GAAE,UAAU,EAAE,OAAOA,GAAE,KAAK,IAAI,EAAE,QAAQA,GAAE,OAAO,OAAO,CAAE;AAAA,MAChH;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AACJ,CAAC;AAEM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,UAAU,CAAC,GAAI,MAAM,KAAK,KAAK,YAAY,CAAC,CAAE,EAAE,IAAI,KAAa,OAAO,KAAK,KAAK,GAAG;AAC/F,OAAK,KAAK,QAAQ,CAAC,SAAS,MAAM;AAC9B,QAAI,IAAI;AACJ,UAAI;AACA,gBAAQ,QAAQ,OAAO,QAAQ,KAAK;AAAA,MACxC,SACOC,IAAG;AAAA,MAAE;AAChB,QAAI,OAAO,QAAQ,UAAU;AACzB,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO,QAAQ;AAAA,MACf;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,mBAAsC,aAAa,oBAAoB,CAAC,MAAM,QAAQ;AAE/F,EAAO,sBAAsB,KAAK,MAAM,GAAG;AAC3C,aAAW,KAAK,MAAM,GAAG;AAC7B,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI,IAAI,SAAS;AACb,UAAM,aAAa;AAAA,MACf,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACR;AACA,UAAM,IAAI,WAAW,IAAI,OAAO;AAChC,QAAI,MAAM;AACN,YAAM,IAAI,MAAM,0BAA0B,IAAI,OAAO,GAAG;AAC5D,QAAI,YAAY,IAAI,UAAkB,KAAK,CAAC;AAAA,EAChD;AAEI,QAAI,YAAY,IAAI,UAAkB,KAAK;AAC/C,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,UAA6B,aAAa,WAAW,CAAC,MAAM,QAAQ;AAC7E,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI;AACA,YAAM,MAAM,IAAI,IAAI,QAAQ,KAAK;AACjC,UAAI,IAAI,UAAU;AACd,YAAI,SAAS,YAAY;AACzB,YAAI,CAAC,IAAI,SAAS,KAAK,IAAI,QAAQ,GAAG;AAClC,kBAAQ,OAAO,KAAK;AAAA,YAChB,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,SAAiB,SAAS;AAAA,YAC1B,OAAO,QAAQ;AAAA,YACf;AAAA,YACA,UAAU,CAAC,IAAI;AAAA,UACnB,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,IAAI,UAAU;AACd,YAAI,SAAS,YAAY;AACzB,YAAI,CAAC,IAAI,SAAS,KAAK,IAAI,SAAS,SAAS,GAAG,IAAI,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,IAAI,QAAQ,GAAG;AAC3F,kBAAQ,OAAO,KAAK;AAAA,YAChB,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,SAAS,IAAI,SAAS;AAAA,YACtB,OAAO,QAAQ;AAAA,YACf;AAAA,YACA,UAAU,CAAC,IAAI;AAAA,UACnB,CAAC;AAAA,QACL;AAAA,MACJ;AACA;AAAA,IACJ,SACO,GAAG;AACN,cAAQ,OAAO,KAAK;AAAA,QAChB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO,QAAQ;AAAA,QACf;AAAA,QACA,UAAU,CAAC,IAAI;AAAA,MACnB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;AACM,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,MAAI,YAAY,IAAI,UAAkB,MAAM;AAC5C,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,UAA6B,aAAa,WAAW,CAAC,MAAM,QAAQ;AAC7E,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,kBAAqC,aAAa,mBAAmB,CAAC,MAAM,QAAQ;AAC7F,MAAI,YAAY,IAAI,UAAkB,SAAS,GAAG;AAClD,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,MAAI,YAAY,IAAI,UAAkB,KAAK,GAAG;AAC9C,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,kBAAqC,aAAa,mBAAmB,CAAC,MAAM,QAAQ;AAC7F,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,SAAS,KAAK,CAACC,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,SAAS;AAAA,EACjB,CAAC;AACL,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,UAAM,MAAMA,MAAK,KAAK;AACtB,QAAI,SAAS;AAAA,EACjB,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI;AACA,UAAI,IAAI,WAAW,QAAQ,KAAK,GAAG;AAAA,IAEvC,QACM;AACF,cAAQ,OAAO,KAAK;AAAA,QAChB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO,QAAQ;AAAA,QACf;AAAA,QACA,UAAU,CAAC,IAAI;AAAA,MACnB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;AACM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AACM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,CAAC,SAAS,MAAM,IAAI,QAAQ,MAAM,MAAM,GAAG;AACjD,QAAI;AACA,UAAI,CAAC;AACD,cAAM,IAAI,MAAM;AACpB,YAAM,YAAY,OAAO,MAAM;AAC/B,UAAI,GAAG,SAAS,OAAO;AACnB,cAAM,IAAI,MAAM;AACpB,UAAI,YAAY,KAAK,YAAY;AAC7B,cAAM,IAAI,MAAM;AACpB,UAAI,IAAI,WAAW,OAAO,GAAG;AAAA,IACjC,QACM;AACF,cAAQ,OAAO,KAAK;AAAA,QAChB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO,QAAQ;AAAA,QACf;AAAA,QACA,UAAU,CAAC,IAAI;AAAA,MACnB,CAAC;AAAA,IACL;AAAA,EACJ;AACJ,CAAC;AAEM,SAAS,cAAc,MAAM;AAChC,MAAI,SAAS;AACT,WAAO;AACX,MAAI,KAAK,SAAS,MAAM;AACpB,WAAO;AACX,MAAI;AACA,SAAK,IAAI;AACT,WAAO;AAAA,EACX,QACM;AACF,WAAO;AAAA,EACX;AACJ;AACO,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,SAAS,KAAK,CAACA,UAAS;AAC9B,IAAAA,MAAK,KAAK,IAAI,kBAAkB;AAAA,EACpC,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,cAAc,QAAQ,KAAK;AAC3B;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AAEM,SAAS,iBAAiB,MAAM;AACnC,MAAI,CAAS,UAAU,KAAK,IAAI;AAC5B,WAAO;AACX,QAAMC,UAAS,KAAK,QAAQ,SAAS,CAAC,MAAO,MAAM,MAAM,MAAM,GAAI;AACnE,QAAM,SAASA,QAAO,OAAO,KAAK,KAAKA,QAAO,SAAS,CAAC,IAAI,GAAG,GAAG;AAClE,SAAO,cAAc,MAAM;AAC/B;AACO,IAAM,gBAAmC,aAAa,iBAAiB,CAAC,MAAM,QAAQ;AACzF,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,SAAS,KAAK,CAACD,UAAS;AAC9B,IAAAA,MAAK,KAAK,IAAI,kBAAkB;AAAA,EACpC,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,iBAAiB,QAAQ,KAAK;AAC9B;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,MAAI,YAAY,IAAI,UAAkB;AACtC,mBAAiB,KAAK,MAAM,GAAG;AACnC,CAAC;AAEM,SAAS,WAAW,OAAO,YAAY,MAAM;AAChD,MAAI;AACA,UAAM,cAAc,MAAM,MAAM,GAAG;AACnC,QAAI,YAAY,WAAW;AACvB,aAAO;AACX,UAAM,CAAC,MAAM,IAAI;AACjB,QAAI,CAAC;AACD,aAAO;AACX,UAAM,eAAe,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5C,QAAI,SAAS,gBAAgB,cAAc,QAAQ;AAC/C,aAAO;AACX,QAAI,CAAC,aAAa;AACd,aAAO;AACX,QAAI,cAAc,EAAE,SAAS,iBAAiB,aAAa,QAAQ;AAC/D,aAAO;AACX,WAAO;AAAA,EACX,QACM;AACF,WAAO;AAAA,EACX;AACJ;AACO,IAAM,UAA6B,aAAa,WAAW,CAAC,MAAM,QAAQ;AAC7E,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,WAAW,QAAQ,OAAO,IAAI,GAAG;AACjC;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,yBAA4C,aAAa,0BAA0B,CAAC,MAAM,QAAQ;AAC3G,mBAAiB,KAAK,MAAM,GAAG;AAC/B,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,QAAI,IAAI,GAAG,QAAQ,KAAK;AACpB;AACJ,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,QAAQ,IAAI;AAAA,MACZ,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,UAAU,CAAC,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,UAAU,KAAK,KAAK,IAAI,WAAmB;AACrD,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,QAAI,IAAI;AACJ,UAAI;AACA,gBAAQ,QAAQ,OAAO,QAAQ,KAAK;AAAA,MACxC,SACO,GAAG;AAAA,MAAE;AAChB,UAAM,QAAQ,QAAQ;AACtB,QAAI,OAAO,UAAU,YAAY,CAAC,OAAO,MAAM,KAAK,KAAK,OAAO,SAAS,KAAK,GAAG;AAC7E,aAAO;AAAA,IACX;AACA,UAAM,WAAW,OAAO,UAAU,WAC5B,OAAO,MAAM,KAAK,IACd,QACA,CAAC,OAAO,SAAS,KAAK,IAClB,aACA,SACR;AACN,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,GAAI,WAAW,EAAE,SAAS,IAAI,CAAC;AAAA,IACnC,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,mBAAsC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACzF,EAAO,sBAAsB,KAAK,MAAM,GAAG;AAC3C,aAAW,KAAK,MAAM,GAAG;AAC7B,CAAC;AACM,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,UAAkB;AAC5B,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,QAAI,IAAI;AACJ,UAAI;AACA,gBAAQ,QAAQ,QAAQ,QAAQ,KAAK;AAAA,MACzC,SACO,GAAG;AAAA,MAAE;AAChB,UAAM,QAAQ,QAAQ;AACtB,QAAI,OAAO,UAAU;AACjB,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,UAAkB;AAC5B,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,QAAI,IAAI;AACJ,UAAI;AACA,gBAAQ,QAAQ,OAAO,QAAQ,KAAK;AAAA,MACxC,SACO,GAAG;AAAA,MAAE;AAChB,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,QAAI,OAAO,UAAU;AACjB,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,mBAAsC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACzF,EAAO,sBAAsB,KAAK,MAAM,GAAG;AAC3C,aAAW,KAAK,MAAM,GAAG;AAC7B,CAAC;AACM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,QAAI,OAAO,UAAU;AACjB,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,gBAAmC,aAAa,iBAAiB,CAAC,MAAM,QAAQ;AACzF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,UAAkB;AAC5B,OAAK,KAAK,SAAS,oBAAI,IAAI,CAAC,MAAS,CAAC;AACtC,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,QAAI,OAAO,UAAU;AACjB,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,UAAkB;AAC5B,OAAK,KAAK,SAAS,oBAAI,IAAI,CAAC,IAAI,CAAC;AACjC,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,QAAI,UAAU;AACV,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,UAA6B,aAAa,WAAW,CAAC,MAAM,QAAQ;AAC7E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,YAAY;AACnC,CAAC;AACM,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,YAAY;AACnC,CAAC;AACM,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO,QAAQ;AAAA,MACf;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,QAAI,OAAO,UAAU;AACjB,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,QAAI,IAAI,QAAQ;AACZ,UAAI;AACA,gBAAQ,QAAQ,IAAI,KAAK,QAAQ,KAAK;AAAA,MAC1C,SACO,MAAM;AAAA,MAAE;AAAA,IACnB;AACA,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAS,iBAAiB;AAChC,UAAM,cAAc,UAAU,CAAC,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC3D,QAAI;AACA,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA,GAAI,SAAS,EAAE,UAAU,eAAe,IAAI,CAAC;AAAA,MAC7C;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACD,SAAS,kBAAkB,QAAQ,OAAO,OAAO;AAC7C,MAAI,OAAO,OAAO,QAAQ;AACtB,UAAM,OAAO,KAAK,GAAQ,aAAa,OAAO,OAAO,MAAM,CAAC;AAAA,EAChE;AACA,QAAM,MAAM,KAAK,IAAI,OAAO;AAChC;AACO,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,cAAQ,OAAO,KAAK;AAAA,QAChB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,YAAQ,QAAQ,MAAM,MAAM,MAAM;AAClC,UAAM,QAAQ,CAAC;AACf,aAASE,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACnC,YAAM,OAAO,MAAMA,EAAC;AACpB,YAAM,SAAS,IAAI,QAAQ,KAAK,IAAI;AAAA,QAChC,OAAO;AAAA,QACP,QAAQ,CAAC;AAAA,MACb,GAAG,GAAG;AACN,UAAI,kBAAkB,SAAS;AAC3B,cAAM,KAAK,OAAO,KAAK,CAACL,YAAW,kBAAkBA,SAAQ,SAASK,EAAC,CAAC,CAAC;AAAA,MAC7E,OACK;AACD,0BAAkB,QAAQ,SAASA,EAAC;AAAA,MACxC;AAAA,IACJ;AACA,QAAI,MAAM,QAAQ;AACd,aAAO,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM,OAAO;AAAA,IAChD;AACA,WAAO;AAAA,EACX;AACJ,CAAC;AACD,SAAS,mBAAmB,QAAQ,OAAO,KAAK;AAE5C,MAAI,OAAO,OAAO,QAAQ;AACtB,UAAM,OAAO,KAAK,GAAQ,aAAa,KAAK,OAAO,MAAM,CAAC;AAAA,EAC9D;AACA,QAAM,MAAM,GAAG,IAAI,OAAO;AAC9B;AACA,SAAS,2BAA2B,QAAQ,OAAO,KAAK,OAAO;AAC3D,MAAI,OAAO,OAAO,QAAQ;AAEtB,QAAI,MAAM,GAAG,MAAM,QAAW;AAE1B,UAAI,OAAO,OAAO;AACd,cAAM,MAAM,GAAG,IAAI;AAAA,MACvB,OACK;AACD,cAAM,MAAM,GAAG,IAAI,OAAO;AAAA,MAC9B;AAAA,IACJ,OACK;AACD,YAAM,OAAO,KAAK,GAAQ,aAAa,KAAK,OAAO,MAAM,CAAC;AAAA,IAC9D;AAAA,EACJ,WACS,OAAO,UAAU,QAAW;AAEjC,QAAI,OAAO;AACP,YAAM,MAAM,GAAG,IAAI;AAAA,EAC3B,OACK;AAED,UAAM,MAAM,GAAG,IAAI,OAAO;AAAA,EAC9B;AACJ;AACO,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AAEnF,WAAS,KAAK,MAAM,GAAG;AACvB,QAAM,cAAmB,OAAO,MAAM;AAClC,UAAM,OAAO,OAAO,KAAK,IAAI,KAAK;AAClC,eAAW,KAAK,MAAM;AAClB,UAAI,EAAE,IAAI,MAAM,CAAC,aAAa,WAAW;AACrC,cAAM,IAAI,MAAM,2BAA2B,CAAC,0BAA0B;AAAA,MAC1E;AAAA,IACJ;AACA,UAAM,QAAa,aAAa,IAAI,KAAK;AACzC,WAAO;AAAA,MACH,OAAO,IAAI;AAAA,MACX;AAAA,MACA,QAAQ,IAAI,IAAI,IAAI;AAAA,MACpB,SAAS,KAAK;AAAA,MACd,cAAc,IAAI,IAAI,KAAK;AAAA,IAC/B;AAAA,EACJ,CAAC;AACD,EAAK,WAAW,KAAK,MAAM,cAAc,MAAM;AAC3C,UAAM,QAAQ,IAAI;AAClB,UAAM,aAAa,CAAC;AACpB,eAAW,OAAO,OAAO;AACrB,YAAM,QAAQ,MAAM,GAAG,EAAE;AACzB,UAAI,MAAM,QAAQ;AACd,mBAAW,GAAG,MAAM,WAAW,GAAG,IAAI,oBAAI,IAAI;AAC9C,mBAAW,KAAK,MAAM;AAClB,qBAAW,GAAG,EAAE,IAAI,CAAC;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACD,QAAM,mBAAmB,CAAC,UAAU;AAChC,UAAM,MAAM,IAAI,IAAI,CAAC,SAAS,WAAW,KAAK,CAAC;AAC/C,UAAM,EAAE,MAAM,cAAAC,cAAa,IAAI,YAAY;AAC3C,UAAM,WAAW,CAAC,QAAQ;AACtB,YAAM,IAAS,IAAI,GAAG;AACtB,aAAO,SAAS,CAAC,6BAA6B,CAAC;AAAA,IACnD;AACA,QAAI,MAAM,8BAA8B;AACxC,UAAM,MAAM,uBAAO,OAAO,IAAI;AAC9B,eAAW,OAAO,MAAM;AACpB,UAAI,GAAG,IAAS,aAAa,EAAE;AAAA,IACnC;AAEA,QAAI,MAAM,sBAAsB;AAChC,eAAW,OAAO,MAAM;AACpB,UAAIA,cAAa,IAAI,GAAG,GAAG;AACvB,cAAM,KAAK,IAAI,GAAG;AAClB,YAAI,MAAM,SAAS,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG;AAC3C,cAAM,IAAS,IAAI,GAAG;AACtB,YAAI,MAAM;AAAA,cACZ,EAAE;AAAA,sBACM,CAAC;AAAA,kBACL,CAAC;AAAA,0BACO,CAAC;AAAA;AAAA;AAAA;AAAA,gBAIX,EAAE;AAAA;AAAA,oCAEkB,CAAC,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA,qBAItC,EAAE;AAAA,gBACP,CAAC,wBAAwB,CAAC;AAAA;AAAA,sBAEpB,CAAC,OAAO,EAAE;AAAA;AAAA,SAEvB;AAAA,MACG,OACK;AACD,cAAM,KAAK,IAAI,GAAG;AAElB,YAAI,MAAM,SAAS,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG;AAC3C,YAAI,MAAM;AAAA,gBACV,EAAE,0DAA0D,EAAE;AAAA;AAAA,gCAEzC,IAAI,GAAG,CAAC,qBAA0B,IAAI,GAAG,CAAC;AAAA,gBAC/D;AACA,YAAI,MAAM,aAAkB,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ;AAAA,MACzD;AAAA,IACJ;AACA,QAAI,MAAM,4BAA4B;AACtC,QAAI,MAAM,iBAAiB;AAC3B,UAAM,KAAK,IAAI,QAAQ;AACvB,WAAO,CAAC,SAAS,QAAQ,GAAG,OAAO,SAAS,GAAG;AAAA,EACnD;AACA,MAAI;AACJ,QAAMC,YAAgB;AACtB,QAAM,MAAM,CAAM,aAAa;AAC/B,QAAMC,cAAkB;AACxB,QAAM,cAAc,OAAOA,YAAW;AACtC,QAAM,EAAE,SAAS,IAAI;AACrB,MAAI;AACJ,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,cAAU,QAAQ,YAAY;AAC9B,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAACD,UAAS,KAAK,GAAG;AAClB,cAAQ,OAAO,KAAK;AAAA,QAChB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,CAAC;AACf,QAAI,OAAO,eAAe,KAAK,UAAU,SAAS,IAAI,YAAY,MAAM;AAEpE,UAAI,CAAC;AACD,mBAAW,iBAAiB,IAAI,KAAK;AACzC,gBAAU,SAAS,SAAS,GAAG;AAAA,IACnC,OACK;AACD,cAAQ,QAAQ,CAAC;AACjB,YAAM,QAAQ,MAAM;AACpB,iBAAW,OAAO,MAAM,MAAM;AAC1B,cAAM,KAAK,MAAM,GAAG;AAapB,cAAMN,KAAI,GAAG,KAAK,IAAI,EAAE,OAAO,MAAM,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,GAAG;AAC5D,cAAM,aAAa,GAAG,KAAK,UAAU,cAAc,GAAG,KAAK,WAAW;AACtE,YAAIA,cAAa,SAAS;AACtB,gBAAM,KAAKA,GAAE,KAAK,CAACA,OAAM,aAAa,2BAA2BA,IAAG,SAAS,KAAK,KAAK,IAAI,mBAAmBA,IAAG,SAAS,GAAG,CAAC,CAAC;AAAA,QACnI,WACS,YAAY;AACjB,qCAA2BA,IAAG,SAAS,KAAK,KAAK;AAAA,QACrD,OACK;AACD,6BAAmBA,IAAG,SAAS,GAAG;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,UAAU;AAEX,aAAO,MAAM,SAAS,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM,OAAO,IAAI;AAAA,IACnE;AACA,UAAM,eAAe,CAAC;AAEtB,UAAM,SAAS,MAAM;AACrB,UAAM,YAAY,SAAS;AAC3B,UAAMQ,KAAI,UAAU,IAAI;AACxB,eAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AAClC,UAAI,OAAO,IAAI,GAAG;AACd;AACJ,UAAIA,OAAM,SAAS;AACf,qBAAa,KAAK,GAAG;AACrB;AAAA,MACJ;AACA,YAAMR,KAAI,UAAU,IAAI,EAAE,OAAO,MAAM,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,GAAG;AAC9D,UAAIA,cAAa,SAAS;AACtB,cAAM,KAAKA,GAAE,KAAK,CAACA,OAAM,mBAAmBA,IAAG,SAAS,GAAG,CAAC,CAAC;AAAA,MACjE,OACK;AACD,2BAAmBA,IAAG,SAAS,GAAG;AAAA,MACtC;AAAA,IACJ;AACA,QAAI,aAAa,QAAQ;AACrB,cAAQ,OAAO,KAAK;AAAA,QAChB,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,CAAC,MAAM;AACP,aAAO;AACX,WAAO,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM;AACjC,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ,CAAC;AACD,SAAS,mBAAmB,SAAS,OAAO,MAAM,KAAK;AACnD,aAAW,UAAU,SAAS;AAC1B,QAAI,OAAO,OAAO,WAAW,GAAG;AAC5B,YAAM,QAAQ,OAAO;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,OAAO,KAAK;AAAA,IACd,MAAM;AAAA,IACN,OAAO,MAAM;AAAA,IACb;AAAA,IACA,QAAQ,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC,CAAC;AAAA,EAC3G,CAAC;AACD,SAAO;AACX;AACO,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,WAAS,KAAK,MAAM,GAAG;AACvB,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM;AACvC,QAAI,IAAI,QAAQ,MAAM,CAACS,OAAMA,GAAE,KAAK,MAAM,GAAG;AACzC,aAAO,IAAI,IAAI,IAAI,QAAQ,QAAQ,CAAC,WAAW,MAAM,KAAK,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,IAClF;AACA,WAAO;AAAA,EACX,CAAC;AACD,EAAK,WAAW,KAAK,MAAM,WAAW,MAAM;AACxC,QAAI,IAAI,QAAQ,MAAM,CAACA,OAAMA,GAAE,KAAK,OAAO,GAAG;AAC1C,YAAM,WAAW,IAAI,QAAQ,IAAI,CAACA,OAAMA,GAAE,KAAK,OAAO;AACtD,aAAO,IAAI,OAAO,KAAK,SAAS,IAAI,CAAC,MAAW,WAAW,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI;AAAA,IACvF;AACA,WAAO;AAAA,EACX,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,QAAI,QAAQ;AACZ,UAAM,UAAU,CAAC;AACjB,eAAW,UAAU,IAAI,SAAS;AAC9B,YAAM,SAAS,OAAO,KAAK,IAAI;AAAA,QAC3B,OAAO,QAAQ;AAAA,QACf,QAAQ,CAAC;AAAA,MACb,GAAG,GAAG;AACN,UAAI,kBAAkB,SAAS;AAC3B,gBAAQ,KAAK,MAAM;AACnB,gBAAQ;AAAA,MACZ,OACK;AACD,YAAI,OAAO,OAAO,WAAW;AACzB,iBAAO;AACX,gBAAQ,KAAK,MAAM;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,CAAC;AACD,aAAO,mBAAmB,SAAS,SAAS,MAAM,GAAG;AACzD,WAAO,QAAQ,IAAI,OAAO,EAAE,KAAK,CAACC,aAAY;AAC1C,aAAO,mBAAmBA,UAAS,SAAS,MAAM,GAAG;AAAA,IACzD,CAAC;AAAA,EACL;AACJ,CAAC;AACM,IAAM,yBAER,aAAa,0BAA0B,CAAC,MAAM,QAAQ;AACvD,YAAU,KAAK,MAAM,GAAG;AACxB,QAAM,SAAS,KAAK,KAAK;AACzB,EAAK,WAAW,KAAK,MAAM,cAAc,MAAM;AAC3C,UAAM,aAAa,CAAC;AACpB,eAAW,UAAU,IAAI,SAAS;AAC9B,YAAM,KAAK,OAAO,KAAK;AACvB,UAAI,CAAC,MAAM,OAAO,KAAK,EAAE,EAAE,WAAW;AAClC,cAAM,IAAI,MAAM,gDAAgD,IAAI,QAAQ,QAAQ,MAAM,CAAC,GAAG;AAClG,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,EAAE,GAAG;AACrC,YAAI,CAAC,WAAW,CAAC;AACb,qBAAW,CAAC,IAAI,oBAAI,IAAI;AAC5B,mBAAW,OAAO,GAAG;AACjB,qBAAW,CAAC,EAAE,IAAI,GAAG;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACD,QAAM,OAAY,OAAO,MAAM;AAC3B,UAAM,OAAO,IAAI;AACjB,UAAM,MAAM,oBAAI,IAAI;AACpB,eAAWD,MAAK,MAAM;AAClB,YAAM,SAASA,GAAE,KAAK,WAAW,IAAI,aAAa;AAClD,UAAI,CAAC,UAAU,OAAO,SAAS;AAC3B,cAAM,IAAI,MAAM,gDAAgD,IAAI,QAAQ,QAAQA,EAAC,CAAC,GAAG;AAC7F,iBAAW,KAAK,QAAQ;AACpB,YAAI,IAAI,IAAI,CAAC,GAAG;AACZ,gBAAM,IAAI,MAAM,kCAAkC,OAAO,CAAC,CAAC,GAAG;AAAA,QAClE;AACA,YAAI,IAAI,GAAGA,EAAC;AAAA,MAChB;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAAM,SAAS,KAAK,GAAG;AACvB,cAAQ,OAAO,KAAK;AAAA,QAChB,MAAM;AAAA,QACN,UAAU;AAAA,QACV;AAAA,QACA;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,MAAM,KAAK,MAAM,IAAI,QAAQ,IAAI,aAAa,CAAC;AACrD,QAAI,KAAK;AACL,aAAO,IAAI,KAAK,IAAI,SAAS,GAAG;AAAA,IACpC;AACA,QAAI,IAAI,eAAe;AACnB,aAAO,OAAO,SAAS,GAAG;AAAA,IAC9B;AAEA,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,QAAQ,CAAC;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA,MAAM,CAAC,IAAI,aAAa;AAAA,MACxB;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,mBAAsC,aAAa,oBAAoB,CAAC,MAAM,QAAQ;AAC/F,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,UAAM,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,OAAO,QAAQ,CAAC,EAAE,GAAG,GAAG;AAChE,UAAM,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,OAAO,OAAO,QAAQ,CAAC,EAAE,GAAG,GAAG;AAClE,UAAM,QAAQ,gBAAgB,WAAW,iBAAiB;AAC1D,QAAI,OAAO;AACP,aAAO,QAAQ,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,KAAK,CAAC,CAACE,OAAMC,MAAK,MAAM;AACtD,eAAO,0BAA0B,SAASD,OAAMC,MAAK;AAAA,MACzD,CAAC;AAAA,IACL;AACA,WAAO,0BAA0B,SAAS,MAAM,KAAK;AAAA,EACzD;AACJ,CAAC;AACD,SAAS,YAAYC,IAAG,GAAG;AAGvB,MAAIA,OAAM,GAAG;AACT,WAAO,EAAE,OAAO,MAAM,MAAMA,GAAE;AAAA,EAClC;AACA,MAAIA,cAAa,QAAQ,aAAa,QAAQ,CAACA,OAAM,CAAC,GAAG;AACrD,WAAO,EAAE,OAAO,MAAM,MAAMA,GAAE;AAAA,EAClC;AACA,MAAS,cAAcA,EAAC,KAAU,cAAc,CAAC,GAAG;AAChD,UAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,UAAM,aAAa,OAAO,KAAKA,EAAC,EAAE,OAAO,CAAC,QAAQ,MAAM,QAAQ,GAAG,MAAM,EAAE;AAC3E,UAAM,SAAS,EAAE,GAAGA,IAAG,GAAG,EAAE;AAC5B,eAAW,OAAO,YAAY;AAC1B,YAAM,cAAc,YAAYA,GAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAC9C,UAAI,CAAC,YAAY,OAAO;AACpB,eAAO;AAAA,UACH,OAAO;AAAA,UACP,gBAAgB,CAAC,KAAK,GAAG,YAAY,cAAc;AAAA,QACvD;AAAA,MACJ;AACA,aAAO,GAAG,IAAI,YAAY;AAAA,IAC9B;AACA,WAAO,EAAE,OAAO,MAAM,MAAM,OAAO;AAAA,EACvC;AACA,MAAI,MAAM,QAAQA,EAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACtC,QAAIA,GAAE,WAAW,EAAE,QAAQ;AACvB,aAAO,EAAE,OAAO,OAAO,gBAAgB,CAAC,EAAE;AAAA,IAC9C;AACA,UAAM,WAAW,CAAC;AAClB,aAAS,QAAQ,GAAG,QAAQA,GAAE,QAAQ,SAAS;AAC3C,YAAM,QAAQA,GAAE,KAAK;AACrB,YAAM,QAAQ,EAAE,KAAK;AACrB,YAAM,cAAc,YAAY,OAAO,KAAK;AAC5C,UAAI,CAAC,YAAY,OAAO;AACpB,eAAO;AAAA,UACH,OAAO;AAAA,UACP,gBAAgB,CAAC,OAAO,GAAG,YAAY,cAAc;AAAA,QACzD;AAAA,MACJ;AACA,eAAS,KAAK,YAAY,IAAI;AAAA,IAClC;AACA,WAAO,EAAE,OAAO,MAAM,MAAM,SAAS;AAAA,EACzC;AACA,SAAO,EAAE,OAAO,OAAO,gBAAgB,CAAC,EAAE;AAC9C;AACA,SAAS,0BAA0B,QAAQ,MAAM,OAAO;AACpD,MAAI,KAAK,OAAO,QAAQ;AACpB,WAAO,OAAO,KAAK,GAAG,KAAK,MAAM;AAAA,EACrC;AACA,MAAI,MAAM,OAAO,QAAQ;AACrB,WAAO,OAAO,KAAK,GAAG,MAAM,MAAM;AAAA,EACtC;AACA,MAAS,QAAQ,MAAM;AACnB,WAAO;AACX,QAAM,SAAS,YAAY,KAAK,OAAO,MAAM,KAAK;AAClD,MAAI,CAAC,OAAO,OAAO;AACf,UAAM,IAAI,MAAM,wCAA6C,KAAK,UAAU,OAAO,cAAc,CAAC,EAAE;AAAA,EACxG;AACA,SAAO,QAAQ,OAAO;AACtB,SAAO;AACX;AACO,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,WAAS,KAAK,MAAM,GAAG;AACvB,QAAM,QAAQ,IAAI;AAClB,QAAM,WAAW,MAAM,SAAS,CAAC,GAAG,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,SAAS,KAAK,KAAK,UAAU,UAAU;AACvG,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,cAAQ,OAAO,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,MAAM;AAAA,MACV,CAAC;AACD,aAAO;AAAA,IACX;AACA,YAAQ,QAAQ,CAAC;AACjB,UAAM,QAAQ,CAAC;AACf,QAAI,CAAC,IAAI,MAAM;AACX,YAAM,SAAS,MAAM,SAAS,MAAM;AACpC,YAAM,WAAW,MAAM,SAAS,WAAW;AAC3C,UAAI,UAAU,UAAU;AACpB,gBAAQ,OAAO,KAAK;AAAA,UAChB;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,GAAI,SAAS,EAAE,MAAM,WAAW,SAAS,MAAM,OAAO,IAAI,EAAE,MAAM,aAAa,SAAS,MAAM,OAAO;AAAA,QACzG,CAAC;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAIT,KAAI;AACR,eAAW,QAAQ,OAAO;AACtB,MAAAA;AACA,UAAIA,MAAK,MAAM;AACX,YAAIA,MAAK;AACL;AAAA;AACR,YAAM,SAAS,KAAK,KAAK,IAAI;AAAA,QACzB,OAAO,MAAMA,EAAC;AAAA,QACd,QAAQ,CAAC;AAAA,MACb,GAAG,GAAG;AACN,UAAI,kBAAkB,SAAS;AAC3B,cAAM,KAAK,OAAO,KAAK,CAACL,YAAW,kBAAkBA,SAAQ,SAASK,EAAC,CAAC,CAAC;AAAA,MAC7E,OACK;AACD,0BAAkB,QAAQ,SAASA,EAAC;AAAA,MACxC;AAAA,IACJ;AACA,QAAI,IAAI,MAAM;AACV,YAAM,OAAO,MAAM,MAAM,MAAM,MAAM;AACrC,iBAAW,MAAM,MAAM;AACnB,QAAAA;AACA,cAAM,SAAS,IAAI,KAAK,KAAK,IAAI;AAAA,UAC7B,OAAO;AAAA,UACP,QAAQ,CAAC;AAAA,QACb,GAAG,GAAG;AACN,YAAI,kBAAkB,SAAS;AAC3B,gBAAM,KAAK,OAAO,KAAK,CAACL,YAAW,kBAAkBA,SAAQ,SAASK,EAAC,CAAC,CAAC;AAAA,QAC7E,OACK;AACD,4BAAkB,QAAQ,SAASA,EAAC;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM;AACN,aAAO,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM,OAAO;AAChD,WAAO;AAAA,EACX;AACJ,CAAC;AACD,SAAS,kBAAkB,QAAQ,OAAO,OAAO;AAC7C,MAAI,OAAO,OAAO,QAAQ;AACtB,UAAM,OAAO,KAAK,GAAQ,aAAa,OAAO,OAAO,MAAM,CAAC;AAAA,EAChE;AACA,QAAM,MAAM,KAAK,IAAI,OAAO;AAChC;AACO,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAAM,cAAc,KAAK,GAAG;AAC5B,cAAQ,OAAO,KAAK;AAAA,QAChB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,CAAC;AACf,QAAI,IAAI,QAAQ,KAAK,QAAQ;AACzB,YAAM,SAAS,IAAI,QAAQ,KAAK;AAChC,cAAQ,QAAQ,CAAC;AACjB,iBAAW,OAAO,QAAQ;AACtB,YAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AAC/E,gBAAM,SAAS,IAAI,UAAU,KAAK,IAAI,EAAE,OAAO,MAAM,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,GAAG;AAC5E,cAAI,kBAAkB,SAAS;AAC3B,kBAAM,KAAK,OAAO,KAAK,CAACL,YAAW;AAC/B,kBAAIA,QAAO,OAAO,QAAQ;AACtB,wBAAQ,OAAO,KAAK,GAAQ,aAAa,KAAKA,QAAO,MAAM,CAAC;AAAA,cAChE;AACA,sBAAQ,MAAM,GAAG,IAAIA,QAAO;AAAA,YAChC,CAAC,CAAC;AAAA,UACN,OACK;AACD,gBAAI,OAAO,OAAO,QAAQ;AACtB,sBAAQ,OAAO,KAAK,GAAQ,aAAa,KAAK,OAAO,MAAM,CAAC;AAAA,YAChE;AACA,oBAAQ,MAAM,GAAG,IAAI,OAAO;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ;AACA,UAAI;AACJ,iBAAW,OAAO,OAAO;AACrB,YAAI,CAAC,OAAO,IAAI,GAAG,GAAG;AAClB,yBAAe,gBAAgB,CAAC;AAChC,uBAAa,KAAK,GAAG;AAAA,QACzB;AAAA,MACJ;AACA,UAAI,gBAAgB,aAAa,SAAS,GAAG;AACzC,gBAAQ,OAAO,KAAK;AAAA,UAChB,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AAAA,IACJ,OACK;AACD,cAAQ,QAAQ,CAAC;AACjB,iBAAW,OAAO,QAAQ,QAAQ,KAAK,GAAG;AACtC,YAAI,QAAQ;AACR;AACJ,cAAM,YAAY,IAAI,QAAQ,KAAK,IAAI,EAAE,OAAO,KAAK,QAAQ,CAAC,EAAE,GAAG,GAAG;AACtE,YAAI,qBAAqB,SAAS;AAC9B,gBAAM,IAAI,MAAM,sDAAsD;AAAA,QAC1E;AACA,YAAI,UAAU,OAAO,QAAQ;AACzB,kBAAQ,OAAO,KAAK;AAAA,YAChB,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,QAAQ,UAAU,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC;AAAA,YACjF,OAAO;AAAA,YACP,MAAM,CAAC,GAAG;AAAA,YACV;AAAA,UACJ,CAAC;AACD,kBAAQ,MAAM,UAAU,KAAK,IAAI,UAAU;AAC3C;AAAA,QACJ;AACA,cAAM,SAAS,IAAI,UAAU,KAAK,IAAI,EAAE,OAAO,MAAM,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,GAAG;AAC5E,YAAI,kBAAkB,SAAS;AAC3B,gBAAM,KAAK,OAAO,KAAK,CAACA,YAAW;AAC/B,gBAAIA,QAAO,OAAO,QAAQ;AACtB,sBAAQ,OAAO,KAAK,GAAQ,aAAa,KAAKA,QAAO,MAAM,CAAC;AAAA,YAChE;AACA,oBAAQ,MAAM,UAAU,KAAK,IAAIA,QAAO;AAAA,UAC5C,CAAC,CAAC;AAAA,QACN,OACK;AACD,cAAI,OAAO,OAAO,QAAQ;AACtB,oBAAQ,OAAO,KAAK,GAAQ,aAAa,KAAK,OAAO,MAAM,CAAC;AAAA,UAChE;AACA,kBAAQ,MAAM,UAAU,KAAK,IAAI,OAAO;AAAA,QAC5C;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM,QAAQ;AACd,aAAO,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM,OAAO;AAAA,IAChD;AACA,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,UAA6B,aAAa,WAAW,CAAC,MAAM,QAAQ;AAC7E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,QAAQ,QAAQ;AACtB,QAAI,EAAE,iBAAiB,MAAM;AACzB,cAAQ,OAAO,KAAK;AAAA,QAChB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,CAAC;AACf,YAAQ,QAAQ,oBAAI,IAAI;AACxB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO;AAC9B,YAAM,YAAY,IAAI,QAAQ,KAAK,IAAI,EAAE,OAAO,KAAK,QAAQ,CAAC,EAAE,GAAG,GAAG;AACtE,YAAM,cAAc,IAAI,UAAU,KAAK,IAAI,EAAE,OAAc,QAAQ,CAAC,EAAE,GAAG,GAAG;AAC5E,UAAI,qBAAqB,WAAW,uBAAuB,SAAS;AAChE,cAAM,KAAK,QAAQ,IAAI,CAAC,WAAW,WAAW,CAAC,EAAE,KAAK,CAAC,CAACe,YAAWC,YAAW,MAAM;AAChF,0BAAgBD,YAAWC,cAAa,SAAS,KAAK,OAAO,MAAM,GAAG;AAAA,QAC1E,CAAC,CAAC;AAAA,MACN,OACK;AACD,wBAAgB,WAAW,aAAa,SAAS,KAAK,OAAO,MAAM,GAAG;AAAA,MAC1E;AAAA,IACJ;AACA,QAAI,MAAM;AACN,aAAO,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM,OAAO;AAChD,WAAO;AAAA,EACX;AACJ,CAAC;AACD,SAAS,gBAAgB,WAAW,aAAa,OAAO,KAAK,OAAO,MAAM,KAAK;AAC3E,MAAI,UAAU,OAAO,QAAQ;AACzB,QAAS,iBAAiB,IAAI,OAAO,GAAG,GAAG;AACvC,YAAM,OAAO,KAAK,GAAQ,aAAa,KAAK,UAAU,MAAM,CAAC;AAAA,IACjE,OACK;AACD,YAAM,OAAO,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,QAAQ,UAAU,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC;AAAA,MACrF,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,YAAY,OAAO,QAAQ;AAC3B,QAAS,iBAAiB,IAAI,OAAO,GAAG,GAAG;AACvC,YAAM,OAAO,KAAK,GAAQ,aAAa,KAAK,YAAY,MAAM,CAAC;AAAA,IACnE,OACK;AACD,YAAM,OAAO,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,YAAY,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC;AAAA,MACvF,CAAC;AAAA,IACL;AAAA,EACJ;AACA,QAAM,MAAM,IAAI,UAAU,OAAO,YAAY,KAAK;AACtD;AACO,IAAM,UAA6B,aAAa,WAAW,CAAC,MAAM,QAAQ;AAC7E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,QAAQ,QAAQ;AACtB,QAAI,EAAE,iBAAiB,MAAM;AACzB,cAAQ,OAAO,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,MAAM;AAAA,MACV,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,CAAC;AACf,YAAQ,QAAQ,oBAAI,IAAI;AACxB,eAAW,QAAQ,OAAO;AACtB,YAAM,SAAS,IAAI,UAAU,KAAK,IAAI,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,GAAG,GAAG;AACtE,UAAI,kBAAkB,SAAS;AAC3B,cAAM,KAAK,OAAO,KAAK,CAAChB,YAAW,gBAAgBA,SAAQ,OAAO,CAAC,CAAC;AAAA,MACxE;AAEI,wBAAgB,QAAQ,OAAO;AAAA,IACvC;AACA,QAAI,MAAM;AACN,aAAO,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM,OAAO;AAChD,WAAO;AAAA,EACX;AACJ,CAAC;AACD,SAAS,gBAAgB,QAAQ,OAAO;AACpC,MAAI,OAAO,OAAO,QAAQ;AACtB,UAAM,OAAO,KAAK,GAAG,OAAO,MAAM;AAAA,EACtC;AACA,QAAM,MAAM,IAAI,OAAO,KAAK;AAChC;AACO,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,WAAS,KAAK,MAAM,GAAG;AACvB,QAAM,SAAc,cAAc,IAAI,OAAO;AAC7C,OAAK,KAAK,SAAS,IAAI,IAAI,MAAM;AACjC,OAAK,KAAK,UAAU,IAAI,OAAO,KAAK,OAC/B,OAAO,CAAC,MAAW,iBAAiB,IAAI,OAAO,CAAC,CAAC,EACjD,IAAI,CAACU,OAAO,OAAOA,OAAM,WAAgB,YAAYA,EAAC,IAAIA,GAAE,SAAS,CAAE,EACvE,KAAK,GAAG,CAAC,IAAI;AAClB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,QAAQ,QAAQ;AACtB,QAAI,KAAK,KAAK,OAAO,IAAI,KAAK,GAAG;AAC7B,aAAO;AAAA,IACX;AACA,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,SAAS,IAAI,IAAI,IAAI,MAAM;AACrC,OAAK,KAAK,UAAU,IAAI,OAAO,KAAK,IAAI,OACnC,IAAI,CAACA,OAAO,OAAOA,OAAM,WAAgB,YAAYA,EAAC,IAAIA,KAAIA,GAAE,SAAS,IAAI,OAAOA,EAAC,CAAE,EACvF,KAAK,GAAG,CAAC,IAAI;AAClB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,QAAQ,QAAQ;AACtB,QAAI,KAAK,KAAK,OAAO,IAAI,KAAK,GAAG;AAC7B,aAAO;AAAA,IACX;AACA,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,QAAQ,IAAI;AAAA,MACZ;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,QAAQ,QAAQ;AACtB,QAAI,iBAAiB;AACjB,aAAO;AACX,YAAQ,OAAO,KAAK;AAAA,MAChB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,gBAAmC,aAAa,iBAAiB,CAAC,MAAM,QAAQ;AACzF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,UAAM,OAAO,IAAI,UAAU,QAAQ,OAAO,OAAO;AACjD,QAAI,KAAK,OAAO;AACZ,YAAM,SAAS,gBAAgB,UAAU,OAAO,QAAQ,QAAQ,IAAI;AACpE,aAAO,OAAO,KAAK,CAACO,YAAW;AAC3B,gBAAQ,QAAQA;AAChB,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,QAAI,gBAAgB,SAAS;AACzB,YAAM,IAAS,eAAe;AAAA,IAClC;AACA,YAAQ,QAAQ;AAChB,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,eAAkC,aAAa,gBAAgB,CAAC,MAAM,QAAQ;AACvF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ;AAClB,OAAK,KAAK,SAAS;AACnB,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM;AACvC,WAAO,IAAI,UAAU,KAAK,SAAS,oBAAI,IAAI,CAAC,GAAG,IAAI,UAAU,KAAK,QAAQ,MAAS,CAAC,IAAI;AAAA,EAC5F,CAAC;AACD,EAAK,WAAW,KAAK,MAAM,WAAW,MAAM;AACxC,UAAM,UAAU,IAAI,UAAU,KAAK;AACnC,WAAO,UAAU,IAAI,OAAO,KAAU,WAAW,QAAQ,MAAM,CAAC,KAAK,IAAI;AAAA,EAC7E,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,QAAI,QAAQ,UAAU,QAAW;AAC7B,aAAO;AAAA,IACX;AACA,WAAO,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAAA,EAC9C;AACJ,CAAC;AACM,IAAM,eAAkC,aAAa,gBAAgB,CAAC,MAAM,QAAQ;AACvF,WAAS,KAAK,MAAM,GAAG;AACvB,EAAK,WAAW,KAAK,MAAM,SAAS,MAAM,IAAI,UAAU,KAAK,KAAK;AAClE,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,UAAU,KAAK,MAAM;AACpE,EAAK,WAAW,KAAK,MAAM,WAAW,MAAM;AACxC,UAAM,UAAU,IAAI,UAAU,KAAK;AACnC,WAAO,UAAU,IAAI,OAAO,KAAU,WAAW,QAAQ,MAAM,CAAC,SAAS,IAAI;AAAA,EACjF,CAAC;AACD,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM;AACvC,WAAO,IAAI,UAAU,KAAK,SAAS,oBAAI,IAAI,CAAC,GAAG,IAAI,UAAU,KAAK,QAAQ,IAAI,CAAC,IAAI;AAAA,EACvF,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,QAAI,QAAQ,UAAU;AAClB,aAAO;AACX,WAAO,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAAA,EAC9C;AACJ,CAAC;AACM,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,WAAS,KAAK,MAAM,GAAG;AAEvB,OAAK,KAAK,QAAQ;AAClB,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,UAAU,KAAK,MAAM;AACpE,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,QAAI,QAAQ,UAAU,QAAW;AAC7B,cAAQ,QAAQ,IAAI;AAIpB,aAAO;AAAA,IACX;AACA,UAAM,SAAS,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAClD,QAAI,kBAAkB,SAAS;AAC3B,aAAO,OAAO,KAAK,CAACjB,YAAW,oBAAoBA,SAAQ,GAAG,CAAC;AAAA,IACnE;AACA,WAAO,oBAAoB,QAAQ,GAAG;AAAA,EAC1C;AACJ,CAAC;AACD,SAAS,oBAAoB,SAAS,KAAK;AACvC,MAAI,QAAQ,UAAU,QAAW;AAC7B,YAAQ,QAAQ,IAAI;AAAA,EACxB;AACA,SAAO;AACX;AACO,IAAM,eAAkC,aAAa,gBAAgB,CAAC,MAAM,QAAQ;AACvF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ;AAClB,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,UAAU,KAAK,MAAM;AACpE,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,QAAI,QAAQ,UAAU,QAAW;AAC7B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,WAAO,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAAA,EAC9C;AACJ,CAAC;AACM,IAAM,kBAAqC,aAAa,mBAAmB,CAAC,MAAM,QAAQ;AAC7F,WAAS,KAAK,MAAM,GAAG;AACvB,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM;AACvC,UAAM,IAAI,IAAI,UAAU,KAAK;AAC7B,WAAO,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,MAAS,CAAC,IAAI;AAAA,EAChE,CAAC;AACD,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,SAAS,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAClD,QAAI,kBAAkB,SAAS;AAC3B,aAAO,OAAO,KAAK,CAACA,YAAW,wBAAwBA,SAAQ,IAAI,CAAC;AAAA,IACxE;AACA,WAAO,wBAAwB,QAAQ,IAAI;AAAA,EAC/C;AACJ,CAAC;AACD,SAAS,wBAAwB,SAAS,MAAM;AAC5C,MAAI,CAAC,QAAQ,OAAO,UAAU,QAAQ,UAAU,QAAW;AACvD,YAAQ,OAAO,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO,QAAQ;AAAA,MACf;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACO,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,SAAS,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAClD,QAAI,kBAAkB,SAAS;AAC3B,aAAO,OAAO,KAAK,CAACA,YAAW;AAC3B,gBAAQ,QAAQA,QAAO,OAAO,WAAW;AACzC,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,YAAQ,QAAQ,OAAO,OAAO,WAAW;AACzC,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,YAA+B,aAAa,aAAa,CAAC,MAAM,QAAQ;AACjF,WAAS,KAAK,MAAM,GAAG;AACvB,EAAK,WAAW,KAAK,MAAM,SAAS,MAAM,IAAI,UAAU,KAAK,KAAK;AAClE,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,UAAU,KAAK,MAAM;AACpE,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,UAAU,KAAK,MAAM;AACpE,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,SAAS,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAClD,QAAI,kBAAkB,SAAS;AAC3B,aAAO,OAAO,KAAK,CAACA,YAAW;AAC3B,gBAAQ,QAAQA,QAAO;AACvB,YAAIA,QAAO,OAAO,QAAQ;AACtB,kBAAQ,QAAQ,IAAI,WAAW;AAAA,YAC3B,GAAG;AAAA,YACH,OAAO;AAAA,cACH,QAAQA,QAAO,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC;AAAA,YAClF;AAAA,YACA,OAAO,QAAQ;AAAA,UACnB,CAAC;AACD,kBAAQ,SAAS,CAAC;AAAA,QACtB;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,YAAQ,QAAQ,OAAO;AACvB,QAAI,OAAO,OAAO,QAAQ;AACtB,cAAQ,QAAQ,IAAI,WAAW;AAAA,QAC3B,GAAG;AAAA,QACH,OAAO;AAAA,UACH,QAAQ,OAAO,OAAO,IAAI,CAAC,QAAa,cAAc,KAAK,KAAU,OAAO,CAAC,CAAC;AAAA,QAClF;AAAA,QACA,OAAO,QAAQ;AAAA,MACnB,CAAC;AACD,cAAQ,SAAS,CAAC;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,UAA6B,aAAa,WAAW,CAAC,MAAM,QAAQ;AAC7E,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,QAAI,OAAO,QAAQ,UAAU,YAAY,CAAC,OAAO,MAAM,QAAQ,KAAK,GAAG;AACnE,cAAQ,OAAO,KAAK;AAAA,QAChB,OAAO,QAAQ;AAAA,QACf;AAAA,QACA,UAAU;AAAA,QACV,MAAM;AAAA,MACV,CAAC;AACD,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,WAAS,KAAK,MAAM,GAAG;AACvB,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,GAAG,KAAK,MAAM;AAC7D,EAAK,WAAW,KAAK,MAAM,SAAS,MAAM,IAAI,GAAG,KAAK,KAAK;AAC3D,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,IAAI,KAAK,MAAM;AAC9D,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,OAAO,IAAI,GAAG,KAAK,IAAI,SAAS,GAAG;AACzC,QAAI,gBAAgB,SAAS;AACzB,aAAO,KAAK,KAAK,CAACY,UAAS,iBAAiBA,OAAM,KAAK,GAAG,CAAC;AAAA,IAC/D;AACA,WAAO,iBAAiB,MAAM,KAAK,GAAG;AAAA,EAC1C;AACJ,CAAC;AACD,SAAS,iBAAiB,MAAM,KAAK,KAAK;AACtC,MAAS,QAAQ,IAAI,GAAG;AACpB,WAAO;AAAA,EACX;AACA,SAAO,IAAI,IAAI,KAAK,IAAI,EAAE,OAAO,KAAK,OAAO,QAAQ,KAAK,OAAO,GAAG,GAAG;AAC3E;AACO,IAAM,eAAkC,aAAa,gBAAgB,CAAC,MAAM,QAAQ;AACvF,WAAS,KAAK,MAAM,GAAG;AACvB,EAAK,WAAW,KAAK,MAAM,cAAc,MAAM,IAAI,UAAU,KAAK,UAAU;AAC5E,EAAK,WAAW,KAAK,MAAM,SAAS,MAAM,IAAI,UAAU,KAAK,KAAK;AAClE,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,IAAI,UAAU,KAAK,MAAM;AACpE,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,SAAS,IAAI,UAAU,KAAK,IAAI,SAAS,GAAG;AAClD,QAAI,kBAAkB,SAAS;AAC3B,aAAO,OAAO,KAAK,oBAAoB;AAAA,IAC3C;AACA,WAAO,qBAAqB,MAAM;AAAA,EACtC;AACJ,CAAC;AACD,SAAS,qBAAqB,SAAS;AACnC,UAAQ,QAAQ,OAAO,OAAO,QAAQ,KAAK;AAC3C,SAAO;AACX;AACO,IAAM,sBAAyC,aAAa,uBAAuB,CAAC,MAAM,QAAQ;AACrG,WAAS,KAAK,MAAM,GAAG;AACvB,QAAM,aAAa,CAAC;AACpB,aAAW,QAAQ,IAAI,OAAO;AAC1B,QAAI,gBAAgB,UAAU;AAC1B,UAAI,CAAC,KAAK,KAAK,SAAS;AAEpB,cAAM,IAAI,MAAM,oDAAoD,CAAC,GAAG,KAAK,KAAK,MAAM,EAAE,MAAM,CAAC,EAAE;AAAA,MACvG;AACA,YAAM,SAAS,KAAK,KAAK,mBAAmB,SAAS,KAAK,KAAK,QAAQ,SAAS,KAAK,KAAK;AAC1F,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,kCAAkC,KAAK,KAAK,MAAM,EAAE;AACxE,YAAM,QAAQ,OAAO,WAAW,GAAG,IAAI,IAAI;AAC3C,YAAM,MAAM,OAAO,SAAS,GAAG,IAAI,OAAO,SAAS,IAAI,OAAO;AAC9D,iBAAW,KAAK,OAAO,MAAM,OAAO,GAAG,CAAC;AAAA,IAC5C,WACS,SAAS,QAAa,eAAe,IAAI,OAAO,IAAI,GAAG;AAC5D,iBAAW,KAAU,YAAY,GAAG,IAAI,EAAE,CAAC;AAAA,IAC/C,OACK;AACD,YAAM,IAAI,MAAM,kCAAkC,IAAI,EAAE;AAAA,IAC5D;AAAA,EACJ;AACA,OAAK,KAAK,UAAU,IAAI,OAAO,IAAI,WAAW,KAAK,EAAE,CAAC,GAAG;AACzD,OAAK,KAAK,QAAQ,CAAC,SAAS,SAAS;AACjC,QAAI,OAAO,QAAQ,UAAU,UAAU;AACnC,cAAQ,OAAO,KAAK;AAAA,QAChB,OAAO,QAAQ;AAAA,QACf;AAAA,QACA,UAAU;AAAA,QACV,MAAM;AAAA,MACV,CAAC;AACD,aAAO;AAAA,IACX;AACA,SAAK,KAAK,QAAQ,YAAY;AAC9B,QAAI,CAAC,KAAK,KAAK,QAAQ,KAAK,QAAQ,KAAK,GAAG;AACxC,cAAQ,OAAO,KAAK;AAAA,QAChB,OAAO,QAAQ;AAAA,QACf;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS,KAAK,KAAK,QAAQ;AAAA,MAC/B,CAAC;AACD,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAM,cAAiC,aAAa,eAAe,CAAC,MAAM,QAAQ;AACrF,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,WAAO,QAAQ,QAAQ,QAAQ,KAAK,EAAE,KAAK,CAAC,UAAU,IAAI,UAAU,KAAK,IAAI,EAAE,OAAO,OAAO,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC;AAAA,EACnH;AACJ,CAAC;AACM,IAAM,WAA8B,aAAa,YAAY,CAAC,MAAM,QAAQ;AAC/E,WAAS,KAAK,MAAM,GAAG;AACvB,EAAK,WAAW,KAAK,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC;AAC1D,EAAK,WAAW,KAAK,MAAM,WAAW,MAAM,KAAK,KAAK,UAAU,KAAK,OAAO;AAC5E,EAAK,WAAW,KAAK,MAAM,cAAc,MAAM,KAAK,KAAK,UAAU,KAAK,UAAU;AAClF,EAAK,WAAW,KAAK,MAAM,SAAS,MAAM,KAAK,KAAK,UAAU,KAAK,KAAK;AACxE,EAAK,WAAW,KAAK,MAAM,UAAU,MAAM,KAAK,KAAK,UAAU,KAAK,MAAM;AAC1E,OAAK,KAAK,QAAQ,CAAC,SAAS,QAAQ;AAChC,UAAM,QAAQ,KAAK,KAAK;AACxB,WAAO,MAAM,KAAK,IAAI,SAAS,GAAG;AAAA,EACtC;AACJ,CAAC;AACM,IAAM,aAAgC,aAAa,cAAc,CAAC,MAAM,QAAQ;AACnF,EAAO,UAAU,KAAK,MAAM,GAAG;AAC/B,WAAS,KAAK,MAAM,GAAG;AACvB,OAAK,KAAK,QAAQ,CAAC,SAAS,MAAM;AAC9B,WAAO;AAAA,EACX;AACA,OAAK,KAAK,QAAQ,CAAC,YAAY;AAC3B,UAAM,QAAQ,QAAQ;AACtB,UAAMX,KAAI,IAAI,GAAG,KAAK;AACtB,QAAIA,cAAa,SAAS;AACtB,aAAOA,GAAE,KAAK,CAACA,OAAM,mBAAmBA,IAAG,SAAS,OAAO,IAAI,CAAC;AAAA,IACpE;AACA,uBAAmBA,IAAG,SAAS,OAAO,IAAI;AAC1C;AAAA,EACJ;AACJ,CAAC;AACD,SAAS,mBAAmB,QAAQ,SAAS,OAAO,MAAM;AACtD,MAAI,CAAC,QAAQ;AACT,UAAM,OAAO;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA;AAAA;AAAA,MACA,MAAM,CAAC,GAAI,KAAK,KAAK,IAAI,QAAQ,CAAC,CAAE;AAAA;AAAA,MACpC,UAAU,CAAC,KAAK,KAAK,IAAI;AAAA;AAAA,IAE7B;AACA,QAAI,KAAK,KAAK,IAAI;AACd,WAAK,SAAS,KAAK,KAAK,IAAI;AAChC,YAAQ,OAAO,KAAU,MAAM,IAAI,CAAC;AAAA,EACxC;AACJ;;;ACrqDO,IAAM,UAAU,OAAO,WAAW;AAClC,IAAM,SAAS,OAAO,UAAU;AAChC,IAAM,eAAN,MAAmB;AAAA,EACtB,cAAc;AACV,SAAK,OAAO,oBAAI,QAAQ;AACxB,SAAK,SAAS,oBAAI,IAAI;AAAA,EAC1B;AAAA,EACA,IAAI,WAAW,OAAO;AAClB,UAAM,OAAO,MAAM,CAAC;AACpB,SAAK,KAAK,IAAI,QAAQ,IAAI;AAC1B,QAAI,QAAQ,OAAO,SAAS,YAAY,QAAQ,MAAM;AAClD,UAAI,KAAK,OAAO,IAAI,KAAK,EAAE,GAAG;AAC1B,cAAM,IAAI,MAAM,MAAM,KAAK,EAAE,iCAAiC;AAAA,MAClE;AACA,WAAK,OAAO,IAAI,KAAK,IAAI,MAAM;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,QAAQ;AACX,SAAK,KAAK,OAAO,MAAM;AACvB,WAAO;AAAA,EACX;AAAA,EACA,IAAI,QAAQ;AAGR,UAAM,IAAI,OAAO,KAAK;AACtB,QAAI,GAAG;AACH,YAAM,KAAK,EAAE,GAAI,KAAK,IAAI,CAAC,KAAK,CAAC,EAAG;AACpC,aAAO,GAAG;AACV,aAAO,EAAE,GAAG,IAAI,GAAG,KAAK,KAAK,IAAI,MAAM,EAAE;AAAA,IAC7C;AACA,WAAO,KAAK,KAAK,IAAI,MAAM;AAAA,EAC/B;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,KAAK,IAAI,MAAM;AAAA,EAC/B;AACJ;AAEO,SAAS,WAAW;AACvB,SAAO,IAAI,aAAa;AAC5B;AACO,IAAM,iBAA+B,SAAS;;;;;;;;;;;ACVrD,SAASiB,GACPC,IACAC,GAAAA;AAGA,WADMC,KAAqC,CAAE,GACtCF,GAAUG,UAAU;AACzB,QAAMC,KAAQJ,GAAU,CAAA,GAChBK,KAAwBD,GAAxBC,MAAMC,KAAkBF,GAAlBE,SACRC,KAD0BH,GAATI,KACJC,KAAK,GAAA;AAExB,QAAA,CAAKP,GAAOK,EAAAA,EACV,KAAI,iBAAiBH,IAAO;AAC1B,UAAMM,IAAaN,GAAMO,YAAY,CAAA,EAAGT,OAAO,CAAA;AAE/CA,MAAAA,GAAOK,EAAAA,IAAS,EACdD,SAASI,EAAWJ,SACpBM,MAAMF,EAAWL,KAAAA;IAErB,MACEH,CAAAA,GAAOK,EAAAA,IAAS,EAAED,SAAAA,IAASM,MAAMP,GAAAA;AAUrC,QANI,iBAAiBD,MACnBA,GAAMO,YAAYE,QAAQ,SAACH,IAAAA;AAAU,aACnCA,GAAWR,OAAOW,QAAQ,SAACC,IAAAA;AAAC,eAAKd,GAAUe,KAAKD,EAAAA;MAAE,CAAA;IAAC,CAAA,GAInDb,GAA0B;AAC5B,UAAMe,IAAQd,GAAOK,EAAAA,EAAOS,OACtBC,IAAWD,KAASA,EAAMZ,GAAMC,IAAAA;AAEtCH,MAAAA,GAAOK,EAAAA,IAASW,aACdX,IACAN,GACAC,IACAG,IACAY,IACK,CAAA,EAAgBE,OAAOF,GAAsBb,GAAME,OAAAA,IACpDF,GAAME,OAAAA;IAEd;AAEAN,IAAAA,GAAUoB,MAAAA;EACZ;AAEA,SAAOlB;AACT;AAEA,SAASmB,GACPrB,IACAC,GAAAA;AAIA,WAFMC,KAAqC,CAAA,GAEpCF,GAAUG,UAAU;AACzB,QAAMC,KAAQJ,GAAU,CAAA,GAChBK,KAAwBD,GAAxBC,MAAMC,KAAkBF,GAAlBE,SACRC,KAD0BH,GAATI,KACJC,KAAK,GAAA;AAExB,QAAA,CAAKP,GAAOK,EAAAA,EACV,KAAmB,oBAAfH,GAAMC,MAA0B;AAClC,UAAMK,IAAaN,GAAMF,OAAO,CAAA,EAAG,CAAA;AAEnCA,MAAAA,GAAOK,EAAAA,IAAS,EACdD,SAASI,EAAWJ,SACpBM,MAAMF,EAAWL,KAAAA;IAErB,MACEH,CAAAA,GAAOK,EAAAA,IAAS,EAAED,SAAAA,IAASM,MAAMP,GAAAA;AAUrC,QANmB,oBAAfD,GAAMC,QACRD,GAAMF,OAAOW,QAAQ,SAACH,IAAAA;AACpB,aAAAA,GAAWG,QAAQ,SAACC,IAAAA;AAAC,eAAKd,GAAUe,KAAKD,EAAAA;MAAE,CAAA;IAAC,CAAA,GAI5Cb,GAA0B;AAC5B,UAAMe,IAAQd,GAAOK,EAAAA,EAAOS,OACtBC,IAAWD,KAASA,EAAMZ,GAAMC,IAAAA;AAEtCH,MAAAA,GAAOK,EAAAA,IAASW,aACdX,IACAN,GACAC,IACAG,IACAY,IACK,CAAA,EAAgBE,OAAOF,GAAsBb,GAAME,OAAAA,IACpDF,GAAME,OAAAA;IAEd;AAEAN,IAAAA,GAAUoB,MAAAA;EACZ;AAEA,SAAOlB;AACT;AA2GgB,SAAAoB,EACdC,IACAC,IACAC,GAAAA;AAKA,MAAA,WALAA,MAAAA,IAGI,CAAA,IAnOe,SAACF,IAAAA;AACpB,WACE,UAAUA,MACa,YAAA,OAAhBA,GAAOG,QACd,cAAcH,GAAOG;EAEzB,EA+NmBH,EAAAA,EACf,QAAcI,SAAAA,IAAeC,IAAGC,GAAAA;AAAW,QAAA;AAAA,aAAAC,QAAAC,QAAAC,EAAA,WAAA;AACrCF,eAAAA,QAAAC,QACiBR,GACQ,WAAzBE,EAAgBQ,OAAkB,UAAU,YAAA,EAC5CN,IAAQH,EAAAA,CAAAA,EAAcU,KAAA,SAFlBC,GAAAA;AAON,iBAHAN,EAAQO,6BACNC,EAAuB,CAAA,GAAIR,CAAAA,GAEtB,EACL3B,QAAQ,CAAiB,GACzByB,QAAQF,EAAgBa,MAAMC,OAAOC,OAAO,CAAA,GAAIb,EAAAA,IAAUQ,EAAAA;QAChB,CAAA;MAC9C,GAAC,SAAQ/B,IAAAA;AACP,YAvPY,SAACA,IAAAA;AACnB,iBAAOqC,MAAMC,QAAQtC,QAAAA,KAAAA,SAAAA,GAAOuC,MAAAA;QAC9B,EAqPwBvC,EAAAA,EACd,QAAO,EACLuB,QAAQ,CAAE,GACVzB,QAAQ0C,EACN7C,GACEK,GAAMF,QAAAA,CACL2B,EAAQO,6BACkB,UAAzBP,EAAQgB,YAAAA,GAEZhB,CAAAA,EAAAA;AAKN,cAAMzB;MACR,CAAA,CAAA;IACF,SAACU,IAAAA;AAAA,aAAAgB,QAAAgB,OAAAhC,EAAAA;IAAA;EAAA;AAGH,MA5PmB,SAACS,IAAAA;AACpB,WAAO,UAAUA,MAAiC,YAAA,OAAhBA,GAAOwB;EAC3C,EA0PmBxB,EAAAA,EACf,QAAcI,SAAAA,IAAeC,GAAGC,GAAAA;AAAO,QAAA;AAAIC,aAAAA,QAAAC,QAAAC,EACrC,WAAA;AAE2D,eAAAF,QAAAC,SAAlC,WAAzBN,EAAgBQ,OAAqBe,QAAWC,YAClB1B,IAAQI,IAAQH,EAAAA,CAAAA,EAAcU,KAAxDC,SAAAA,GAAAA;AAKN,iBAHAN,EAAQO,6BACNC,EAAuB,CAAE,GAAER,CAAAA,GAEtB,EACL3B,QAAQ,CAAA,GACRyB,QAAQF,EAAgBa,MAAMC,OAAOC,OAAO,CAAE,GAAEb,EAAAA,IAAUQ,EAAAA;QAChB,CAAA;MAC9C,GAAS/B,SAAAA,IAAAA;AACP,YA/QY,SAACA,IAAAA;AAEnB,iBAAOA,cAAoB8C;QAC7B,EA4QwB9C,EAAAA,EACd,QAAO,EACLuB,QAAQ,CAAE,GACVzB,QAAQ0C,EACNvB,GACEjB,GAAMuC,QAAAA,CACLd,EAAQO,6BACkB,UAAzBP,EAAQgB,YAAAA,GAEZhB,CAAAA,EAAAA;AAKN,cAAMzB;MACR,CAAA,CAAA;IACF,SAACU,IAAAA;AAAAgB,aAAAA,QAAAgB,OAAAhC,EAAAA;IACH;EAAA;AAEA,QAAM,IAAIqC,MAAM,iCAAA;AAClB;", "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "fields", "field", "refs", "for<PERSON>ach", "toNestErrors", "shouldUseNativeValidation", "fieldErrors", "path", "Object", "assign", "isNameInFieldArray", "names", "keys", "fieldArrayErrors", "set", "name", "escapeBrackets", "some", "n", "match", "input", "replace", "initializer", "set", "i", "o", "i", "config", "version", "time", "inst", "result", "checks", "result", "r", "_", "inst", "base64", "i", "optionalKeys", "isObject", "allowsEval", "t", "o", "results", "left", "right", "a", "key<PERSON><PERSON><PERSON>", "valueResult", "output", "parseZod3Issues", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "parseZod4Issues", "zodResolver", "schema", "schemaOptions", "resolverOptions", "_def", "values", "_", "options", "Promise", "resolve", "_catch", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "Object", "assign", "Array", "isArray", "issues", "toNestErrors", "criteriaMode", "reject", "_zod", "parse", "parseAsync", "$ZodError", "Error"]}