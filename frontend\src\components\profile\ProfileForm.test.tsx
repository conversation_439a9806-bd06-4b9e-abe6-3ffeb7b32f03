import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, createTestUser } from '../../test/utils'
import { ProfileForm } from './ProfileForm'

// Mock du contexte d'authentification
const mockUpdateProfile = vi.fn()
const mockUser = createTestUser()
const mockAuthContext = {
  user: mockUser,
  token: 'mock-token',
  isAuthenticated: true,
  isLoading: false,
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  refreshToken: vi.fn(),
  updateProfile: mockUpdateProfile,
}

vi.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}))

describe('ProfileForm', () => {
  const user = userEvent.setup()
  const mockOnSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockAuthContext.isLoading = false
  })

  it('renders profile form with user data', () => {
    render(<ProfileForm />)

    expect(screen.getByText('Mon Profil')).toBeInTheDocument()
    expect(screen.getByText('Gérez vos informations personnelles')).toBeInTheDocument()
    
    expect(screen.getByDisplayValue(mockUser.firstName)).toBeInTheDocument()
    expect(screen.getByDisplayValue(mockUser.lastName)).toBeInTheDocument()
    expect(screen.getByDisplayValue(mockUser.email)).toBeInTheDocument()
  })

  it('shows user account information', () => {
    render(<ProfileForm />)

    expect(screen.getByText('Informations du compte')).toBeInTheDocument()
    expect(screen.getByText(mockUser.role)).toBeInTheDocument()
    expect(screen.getByText('Oui')).toBeInTheDocument() // Email confirmé
  })

  it('validates required fields', async () => {
    render(<ProfileForm />)

    const firstNameInput = screen.getByLabelText('Prénom')
    const submitButton = screen.getByRole('button', { name: /sauvegarder/i })

    // Vider le champ prénom
    await user.clear(firstNameInput)
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Le prénom est requis')).toBeInTheDocument()
    })
  })

  it('validates field lengths', async () => {
    render(<ProfileForm />)

    const firstNameInput = screen.getByLabelText('Prénom')
    const submitButton = screen.getByRole('button', { name: /sauvegarder/i })

    // Entrer un prénom trop court
    await user.clear(firstNameInput)
    await user.type(firstNameInput, 'A')
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Le prénom doit contenir au moins 2 caractères')).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    render(<ProfileForm />)

    const emailInput = screen.getByLabelText('Email')
    const submitButton = screen.getByRole('button', { name: /sauvegarder/i })

    await user.clear(emailInput)
    await user.type(emailInput, 'invalid-email')
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Format d\'email invalide')).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    mockUpdateProfile.mockResolvedValueOnce(undefined)
    render(<ProfileForm onSuccess={mockOnSuccess} />)

    const firstNameInput = screen.getByLabelText('Prénom')
    const lastNameInput = screen.getByLabelText('Nom')
    const emailInput = screen.getByLabelText('Email')
    const submitButton = screen.getByRole('button', { name: /sauvegarder/i })

    await user.clear(firstNameInput)
    await user.type(firstNameInput, 'John Updated')
    await user.clear(lastNameInput)
    await user.type(lastNameInput, 'Doe Updated')
    await user.clear(emailInput)
    await user.type(emailInput, '<EMAIL>')

    await user.click(submitButton)

    await waitFor(() => {
      expect(mockUpdateProfile).toHaveBeenCalledWith({
        firstName: 'John Updated',
        lastName: 'Doe Updated',
        email: '<EMAIL>',
      })
      expect(mockOnSuccess).toHaveBeenCalled()
    })
  })

  it('shows loading state during submission', async () => {
    mockAuthContext.isLoading = true
    render(<ProfileForm />)

    const submitButton = screen.getByRole('button', { name: /mise à jour/i })
    expect(submitButton).toBeDisabled()
  })

  it('handles update error', async () => {
    const errorMessage = 'Cette adresse email est déjà utilisée'
    mockUpdateProfile.mockRejectedValueOnce({
      statusCode: 409,
      message: errorMessage,
    })

    render(<ProfileForm />)

    const emailInput = screen.getByLabelText('Email')
    const submitButton = screen.getByRole('button', { name: /sauvegarder/i })

    await user.clear(emailInput)
    await user.type(emailInput, '<EMAIL>')
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Cette adresse email est déjà utilisée')).toBeInTheDocument()
    })
  })

  it('resets form when cancel is clicked', async () => {
    render(<ProfileForm />)

    const firstNameInput = screen.getByLabelText('Prénom')
    const cancelButton = screen.getByRole('button', { name: /annuler/i })

    // Modifier le champ
    await user.clear(firstNameInput)
    await user.type(firstNameInput, 'Modified Name')

    // Cliquer sur annuler
    await user.click(cancelButton)

    // Vérifier que la valeur originale est restaurée
    expect(firstNameInput).toHaveValue(mockUser.firstName)
  })

  it('disables save button when form is not dirty', () => {
    render(<ProfileForm />)

    const submitButton = screen.getByRole('button', { name: /sauvegarder/i })
    expect(submitButton).toBeDisabled()
  })

  it('enables save button when form is dirty', async () => {
    render(<ProfileForm />)

    const firstNameInput = screen.getByLabelText('Prénom')
    const submitButton = screen.getByRole('button', { name: /sauvegarder/i })

    // Modifier un champ
    await user.type(firstNameInput, ' Modified')

    expect(submitButton).not.toBeDisabled()
  })
})
