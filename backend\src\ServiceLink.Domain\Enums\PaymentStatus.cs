namespace ServiceLink.Domain.Enums;

/// <summary>
/// Énumération des statuts de paiement dans ServiceLink
/// Définit le cycle de vie d'un paiement
/// </summary>
public enum PaymentStatus
{
    /// <summary>
    /// En attente - Paiement initié mais pas encore traité
    /// </summary>
    Pending = 1,

    /// <summary>
    /// En cours de traitement - Paiement en cours de validation
    /// </summary>
    Processing = 2,

    /// <summary>
    /// Terminé - Paiement réussi et confirmé
    /// </summary>
    Completed = 3,

    /// <summary>
    /// Échoué - Paiement refusé ou erreur
    /// </summary>
    Failed = 4,

    /// <summary>
    /// Remboursé - Paiement remboursé au client
    /// </summary>
    Refunded = 5,

    /// <summary>
    /// Remboursement partiel - Une partie du paiement a été remboursée
    /// </summary>
    PartiallyRefunded = 6,

    /// <summary>
    /// En litige - Paiement contesté
    /// </summary>
    Disputed = 7,

    /// <summary>
    /// Annulé - Paiement annulé avant traitement
    /// </summary>
    Cancelled = 8,

    /// <summary>
    /// Expiré - Paiement expiré sans traitement
    /// </summary>
    Expired = 9,

    /// <summary>
    /// En attente de capture - Paiement autorisé mais pas encore capturé
    /// </summary>
    Authorized = 10,

    /// <summary>
    /// Bloqué - Paiement bloqué pour vérification
    /// </summary>
    OnHold = 11
}

/// <summary>
/// Énumération des méthodes de paiement supportées
/// </summary>
public enum PaymentMethod
{
    /// <summary>
    /// Stripe - Cartes bancaires internationales
    /// </summary>
    Stripe = 1,

    /// <summary>
    /// PayPal - Portefeuille électronique
    /// </summary>
    PayPal = 2,

    /// <summary>
    /// MTN Mobile Money - Paiement mobile Afrique
    /// </summary>
    MTNMoMo = 3,

    /// <summary>
    /// Orange Money - Paiement mobile Afrique
    /// </summary>
    OrangeMoney = 4,

    /// <summary>
    /// Flutterwave - Passerelle de paiement Afrique
    /// </summary>
    Flutterwave = 5,

    /// <summary>
    /// Espèces - Paiement en liquide
    /// </summary>
    Cash = 6,

    /// <summary>
    /// Virement bancaire
    /// </summary>
    BankTransfer = 7,

    /// <summary>
    /// Chèque
    /// </summary>
    Check = 8
}

/// <summary>
/// Extensions pour l'énumération PaymentStatus
/// </summary>
public static class PaymentStatusExtensions
{
    /// <summary>
    /// Vérifie si le paiement est en cours
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si le paiement est en cours</returns>
    public static bool IsInProgress(this PaymentStatus status)
    {
        return status is PaymentStatus.Pending or PaymentStatus.Processing or PaymentStatus.Authorized;
    }

    /// <summary>
    /// Vérifie si le paiement est terminé avec succès
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si le paiement est réussi</returns>
    public static bool IsSuccessful(this PaymentStatus status)
    {
        return status == PaymentStatus.Completed;
    }

    /// <summary>
    /// Vérifie si le paiement a échoué
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si le paiement a échoué</returns>
    public static bool IsFailed(this PaymentStatus status)
    {
        return status is PaymentStatus.Failed or PaymentStatus.Cancelled or PaymentStatus.Expired;
    }

    /// <summary>
    /// Vérifie si le paiement peut être remboursé
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si le paiement peut être remboursé</returns>
    public static bool CanBeRefunded(this PaymentStatus status)
    {
        return status is PaymentStatus.Completed or PaymentStatus.PartiallyRefunded;
    }

    /// <summary>
    /// Vérifie si le statut est final
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si le statut est final</returns>
    public static bool IsFinal(this PaymentStatus status)
    {
        return status is PaymentStatus.Completed or PaymentStatus.Failed 
                      or PaymentStatus.Refunded or PaymentStatus.Cancelled 
                      or PaymentStatus.Expired;
    }

    /// <summary>
    /// Obtient la description du statut
    /// </summary>
    /// <param name="status">Le statut</param>
    /// <returns>Description du statut</returns>
    public static string GetDescription(this PaymentStatus status)
    {
        return status switch
        {
            PaymentStatus.Pending => "En attente de traitement",
            PaymentStatus.Processing => "Traitement en cours",
            PaymentStatus.Completed => "Paiement réussi",
            PaymentStatus.Failed => "Paiement échoué",
            PaymentStatus.Refunded => "Remboursé",
            PaymentStatus.PartiallyRefunded => "Partiellement remboursé",
            PaymentStatus.Disputed => "En litige",
            PaymentStatus.Cancelled => "Annulé",
            PaymentStatus.Expired => "Expiré",
            PaymentStatus.Authorized => "Autorisé",
            PaymentStatus.OnHold => "En attente de vérification",
            _ => "Statut inconnu"
        };
    }

    /// <summary>
    /// Obtient la couleur associée au statut
    /// </summary>
    /// <param name="status">Le statut</param>
    /// <returns>Code couleur hexadécimal</returns>
    public static string GetColor(this PaymentStatus status)
    {
        return status switch
        {
            PaymentStatus.Pending => "#FFA500",         // Orange
            PaymentStatus.Processing => "#0066CC",      // Bleu
            PaymentStatus.Completed => "#22C55E",       // Vert
            PaymentStatus.Failed => "#EF4444",          // Rouge
            PaymentStatus.Refunded => "#059669",        // Vert foncé
            PaymentStatus.PartiallyRefunded => "#0891B2", // Cyan
            PaymentStatus.Disputed => "#DC2626",        // Rouge foncé
            PaymentStatus.Cancelled => "#6B7280",       // Gris
            PaymentStatus.Expired => "#78716C",         // Gris foncé
            PaymentStatus.Authorized => "#8B5CF6",      // Violet
            PaymentStatus.OnHold => "#F59E0B",          // Amber
            _ => "#6B7280"                              // Gris par défaut
        };
    }
}

/// <summary>
/// Extensions pour l'énumération PaymentMethod
/// </summary>
public static class PaymentMethodExtensions
{
    /// <summary>
    /// Vérifie si la méthode de paiement est électronique
    /// </summary>
    /// <param name="method">La méthode de paiement</param>
    /// <returns>True si la méthode est électronique</returns>
    public static bool IsElectronic(this PaymentMethod method)
    {
        return method is not (PaymentMethod.Cash or PaymentMethod.Check);
    }

    /// <summary>
    /// Vérifie si la méthode de paiement est mobile
    /// </summary>
    /// <param name="method">La méthode de paiement</param>
    /// <returns>True si la méthode est mobile</returns>
    public static bool IsMobile(this PaymentMethod method)
    {
        return method is PaymentMethod.MTNMoMo or PaymentMethod.OrangeMoney;
    }

    /// <summary>
    /// Obtient la description de la méthode de paiement
    /// </summary>
    /// <param name="method">La méthode de paiement</param>
    /// <returns>Description de la méthode</returns>
    public static string GetDescription(this PaymentMethod method)
    {
        return method switch
        {
            PaymentMethod.Stripe => "Carte bancaire (Stripe)",
            PaymentMethod.PayPal => "PayPal",
            PaymentMethod.MTNMoMo => "MTN Mobile Money",
            PaymentMethod.OrangeMoney => "Orange Money",
            PaymentMethod.Flutterwave => "Flutterwave",
            PaymentMethod.Cash => "Espèces",
            PaymentMethod.BankTransfer => "Virement bancaire",
            PaymentMethod.Check => "Chèque",
            _ => "Méthode inconnue"
        };
    }

    /// <summary>
    /// Obtient l'icône associée à la méthode de paiement
    /// </summary>
    /// <param name="method">La méthode de paiement</param>
    /// <returns>Nom de l'icône</returns>
    public static string GetIcon(this PaymentMethod method)
    {
        return method switch
        {
            PaymentMethod.Stripe => "credit-card",
            PaymentMethod.PayPal => "paypal",
            PaymentMethod.MTNMoMo => "smartphone",
            PaymentMethod.OrangeMoney => "smartphone",
            PaymentMethod.Flutterwave => "zap",
            PaymentMethod.Cash => "banknote",
            PaymentMethod.BankTransfer => "building-bank",
            PaymentMethod.Check => "file-text",
            _ => "help-circle"
        };
    }

    /// <summary>
    /// Obtient les frais de transaction typiques (en pourcentage)
    /// </summary>
    /// <param name="method">La méthode de paiement</param>
    /// <returns>Pourcentage de frais</returns>
    public static decimal GetTypicalFees(this PaymentMethod method)
    {
        return method switch
        {
            PaymentMethod.Stripe => 2.9m,
            PaymentMethod.PayPal => 3.4m,
            PaymentMethod.MTNMoMo => 1.5m,
            PaymentMethod.OrangeMoney => 1.5m,
            PaymentMethod.Flutterwave => 1.4m,
            PaymentMethod.Cash => 0m,
            PaymentMethod.BankTransfer => 0.5m,
            PaymentMethod.Check => 0m,
            _ => 0m
        };
    }
}
