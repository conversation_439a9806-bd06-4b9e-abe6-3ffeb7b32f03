namespace ServiceLink.Domain.Enums;

/// <summary>
/// Énumération des niveaux de force d'un mot de passe
/// </summary>
public enum PasswordStrength
{
    /// <summary>
    /// Très faible - Mot de passe très vulnérable
    /// </summary>
    VeryWeak = 1,

    /// <summary>
    /// Faible - Mot de passe vulnérable
    /// </summary>
    Weak = 2,

    /// <summary>
    /// Moyen - Mot de passe acceptable
    /// </summary>
    Medium = 3,

    /// <summary>
    /// Fort - Bon mot de passe
    /// </summary>
    Strong = 4,

    /// <summary>
    /// Très fort - Excellent mot de passe
    /// </summary>
    VeryStrong = 5
}

/// <summary>
/// Extensions pour l'énumération PasswordStrength
/// </summary>
public static class PasswordStrengthExtensions
{
    /// <summary>
    /// Obtient la description du niveau de force
    /// </summary>
    /// <param name="strength">Le niveau de force</param>
    /// <returns>Description du niveau</returns>
    public static string GetDescription(this PasswordStrength strength)
    {
        return strength switch
        {
            PasswordStrength.VeryWeak => "Très faible - Mot de passe très vulnérable",
            PasswordStrength.Weak => "Faible - Mot de passe vulnérable",
            PasswordStrength.Medium => "Moyen - Mot de passe acceptable",
            PasswordStrength.Strong => "Fort - Bon mot de passe",
            PasswordStrength.VeryStrong => "Très fort - Excellent mot de passe",
            _ => "Niveau inconnu"
        };
    }

    /// <summary>
    /// Obtient la couleur associée au niveau de force (pour l'UI)
    /// </summary>
    /// <param name="strength">Le niveau de force</param>
    /// <returns>Code couleur hexadécimal</returns>
    public static string GetColor(this PasswordStrength strength)
    {
        return strength switch
        {
            PasswordStrength.VeryWeak => "#FF0000", // Rouge
            PasswordStrength.Weak => "#FF8000", // Orange
            PasswordStrength.Medium => "#FFFF00", // Jaune
            PasswordStrength.Strong => "#80FF00", // Vert clair
            PasswordStrength.VeryStrong => "#00FF00", // Vert
            _ => "#808080" // Gris
        };
    }

    /// <summary>
    /// Vérifie si le niveau de force est acceptable (Medium ou plus)
    /// </summary>
    /// <param name="strength">Le niveau de force</param>
    /// <returns>True si acceptable</returns>
    public static bool IsAcceptable(this PasswordStrength strength)
    {
        return strength >= PasswordStrength.Medium;
    }

    /// <summary>
    /// Vérifie si le niveau de force est fort (Strong ou VeryStrong)
    /// </summary>
    /// <param name="strength">Le niveau de force</param>
    /// <returns>True si fort</returns>
    public static bool IsStrong(this PasswordStrength strength)
    {
        return strength >= PasswordStrength.Strong;
    }

    /// <summary>
    /// Obtient le score numérique du niveau de force (0-100)
    /// </summary>
    /// <param name="strength">Le niveau de force</param>
    /// <returns>Score de 0 à 100</returns>
    public static int GetScore(this PasswordStrength strength)
    {
        return strength switch
        {
            PasswordStrength.VeryWeak => 20,
            PasswordStrength.Weak => 40,
            PasswordStrength.Medium => 60,
            PasswordStrength.Strong => 80,
            PasswordStrength.VeryStrong => 100,
            _ => 0
        };
    }
}
