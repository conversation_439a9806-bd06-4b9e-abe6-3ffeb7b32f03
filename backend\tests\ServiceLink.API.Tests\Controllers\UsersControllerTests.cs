using Bogus;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using ServiceLink.API.Tests.Common;
using ServiceLink.Application.DTOs;
using ServiceLink.Domain.Entities;
using ServiceLink.Domain.Enums;
using ServiceLink.Domain.ValueObjects;
using System.Net;

namespace ServiceLink.API.Tests.Controllers;

/// <summary>
/// Tests d'intégration pour UsersController
/// </summary>
public class UsersControllerTests : IntegrationTestBase
{
    private readonly Faker _faker;

    public UsersControllerTests(WebApplicationFactory<Program> factory) : base(factory)
    {
        _faker = new Faker("fr");
    }

    [Fact]
    public async Task GetUsers_WithoutAuthentication_ShouldReturnUnauthorized()
    {
        // Act
        var response = await Client.GetAsync("/api/users");

        // Assert
        response.ShouldBeUnauthorized();
    }

    [Fact]
    public async Task GetUsers_WithValidAuthentication_ShouldReturnUsers()
    {
        // Arrange
        await SeedTestUsers();
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));

        // Act
        var response = await Client.GetAsync("/api/users");

        // Assert
        AssertSuccessResponse(response);
        var users = await DeserializeResponse<PagedResponse<UserResponseDto>>(response);
        users.Should().NotBeNull();
        users!.Items.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetUsers_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        await SeedTestUsers(10);
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));

        // Act
        var response = await Client.GetAsync("/api/users?pageNumber=2&pageSize=3");

        // Assert
        AssertSuccessResponse(response);
        var users = await DeserializeResponse<PagedResponse<UserResponseDto>>(response);
        users.Should().NotBeNull();
        users!.PageNumber.Should().Be(2);
        users.PageSize.Should().Be(3);
        users.Items.Should().HaveCountLessOrEqualTo(3);
    }

    [Fact]
    public async Task GetUserById_WithExistingUser_ShouldReturnUser()
    {
        // Arrange
        var user = await CreateTestUser();
        SetAuthorizationHeader(GenerateTestJwtToken(user.Id, user.Email.Value, user.Role.ToString()));

        // Act
        var response = await Client.GetAsync($"/api/users/{user.Id}");

        // Assert
        AssertSuccessResponse(response);
        var userResponse = await DeserializeResponse<UserResponseDto>(response);
        userResponse.Should().NotBeNull();
        userResponse!.Id.Should().Be(user.Id);
        userResponse.Email.Should().Be(user.Email.Value);
        userResponse.FirstName.Should().Be(user.FirstName);
        userResponse.LastName.Should().Be(user.LastName);
    }

    [Fact]
    public async Task GetUserById_WithNonExistingUser_ShouldReturnNotFound()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));

        // Act
        var response = await Client.GetAsync($"/api/users/{nonExistingId}");

        // Assert
        response.ShouldBeNotFound();
    }

    [Fact]
    public async Task CreateUser_WithValidData_ShouldCreateUser()
    {
        // Arrange
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));
        
        var createRequest = new CreateUserRequestDto
        {
            Email = _faker.Internet.Email(),
            FirstName = _faker.Name.FirstName(),
            LastName = _faker.Name.LastName(),
            Role = UserRole.Client,
            PhoneNumber = "+33123456789",
            Password = "SecurePassword123!"
        };

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createRequest));

        // Assert
        AssertSuccessResponse(response);
        var userResponse = await DeserializeResponse<UserResponseDto>(response);
        userResponse.Should().NotBeNull();
        userResponse!.Email.Should().Be(createRequest.Email);
        userResponse.FirstName.Should().Be(createRequest.FirstName);
        userResponse.LastName.Should().Be(createRequest.LastName);
        userResponse.Role.Should().Be(createRequest.Role);
        userResponse.IsActive.Should().BeTrue();
        userResponse.IsEmailConfirmed.Should().BeFalse();
    }

    [Fact]
    public async Task CreateUser_WithInvalidEmail_ShouldReturnBadRequest()
    {
        // Arrange
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));
        
        var createRequest = new CreateUserRequestDto
        {
            Email = "invalid-email",
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createRequest));

        // Assert
        await response.ShouldHaveValidationError("Email");
    }

    [Fact]
    public async Task CreateUser_WithExistingEmail_ShouldReturnConflict()
    {
        // Arrange
        var existingUser = await CreateTestUser();
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));
        
        var createRequest = new CreateUserRequestDto
        {
            Email = existingUser.Email.Value,
            FirstName = "John",
            LastName = "Doe",
            Role = UserRole.Client
        };

        // Act
        var response = await Client.PostAsync("/api/users", CreateJsonContent(createRequest));

        // Assert
        response.ShouldBeConflict();
    }

    [Fact]
    public async Task UpdateUser_WithValidData_ShouldUpdateUser()
    {
        // Arrange
        var user = await CreateTestUser();
        SetAuthorizationHeader(GenerateTestJwtToken(user.Id, user.Email.Value, user.Role.ToString()));
        
        var updateRequest = new UpdateUserRequestDto
        {
            FirstName = "UpdatedFirstName",
            LastName = "UpdatedLastName",
            PhoneNumber = "+33987654321"
        };

        // Act
        var response = await Client.PutAsync($"/api/users/{user.Id}", CreateJsonContent(updateRequest));

        // Assert
        AssertSuccessResponse(response);
        var userResponse = await DeserializeResponse<UserResponseDto>(response);
        userResponse.Should().NotBeNull();
        userResponse!.FirstName.Should().Be(updateRequest.FirstName);
        userResponse.LastName.Should().Be(updateRequest.LastName);
        userResponse.PhoneNumber.Should().Be(updateRequest.PhoneNumber);
    }

    [Fact]
    public async Task UpdateUser_WithoutPermission_ShouldReturnForbidden()
    {
        // Arrange
        var user = await CreateTestUser();
        var otherUserId = Guid.NewGuid();
        SetAuthorizationHeader(GenerateTestJwtToken(otherUserId, "<EMAIL>", "Client"));
        
        var updateRequest = new UpdateUserRequestDto
        {
            FirstName = "UpdatedFirstName",
            LastName = "UpdatedLastName"
        };

        // Act
        var response = await Client.PutAsync($"/api/users/{user.Id}", CreateJsonContent(updateRequest));

        // Assert
        response.ShouldBeForbidden();
    }

    [Fact]
    public async Task ChangePassword_WithValidData_ShouldChangePassword()
    {
        // Arrange
        var user = await CreateTestUser();
        SetAuthorizationHeader(GenerateTestJwtToken(user.Id, user.Email.Value, user.Role.ToString()));
        
        var changePasswordRequest = new ChangePasswordRequestDto
        {
            CurrentPassword = "CurrentPassword123!",
            NewPassword = "NewSecurePassword123!"
        };

        // Act
        var response = await Client.PostAsync($"/api/users/{user.Id}/change-password", CreateJsonContent(changePasswordRequest));

        // Assert
        AssertSuccessResponse(response);
    }

    [Fact]
    public async Task ChangePassword_WithWeakPassword_ShouldReturnBadRequest()
    {
        // Arrange
        var user = await CreateTestUser();
        SetAuthorizationHeader(GenerateTestJwtToken(user.Id, user.Email.Value, user.Role.ToString()));
        
        var changePasswordRequest = new ChangePasswordRequestDto
        {
            CurrentPassword = "CurrentPassword123!",
            NewPassword = "weak"
        };

        // Act
        var response = await Client.PostAsync($"/api/users/{user.Id}/change-password", CreateJsonContent(changePasswordRequest));

        // Assert
        await response.ShouldHaveValidationError("NewPassword");
    }

    [Fact]
    public async Task ActivateUser_AsAdmin_ShouldActivateUser()
    {
        // Arrange
        var user = await CreateTestUser();
        user.Deactivate();
        await DbContext.SaveChangesAsync();
        
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));

        // Act
        var response = await Client.PostAsync($"/api/users/{user.Id}/activate", null);

        // Assert
        AssertSuccessResponse(response);
        var userResponse = await DeserializeResponse<UserResponseDto>(response);
        userResponse.Should().NotBeNull();
        userResponse!.IsActive.Should().BeTrue();
    }

    [Fact]
    public async Task DeactivateUser_AsAdmin_ShouldDeactivateUser()
    {
        // Arrange
        var user = await CreateTestUser();
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));

        // Act
        var response = await Client.PostAsync($"/api/users/{user.Id}/deactivate", null);

        // Assert
        AssertSuccessResponse(response);
        var userResponse = await DeserializeResponse<UserResponseDto>(response);
        userResponse.Should().NotBeNull();
        userResponse!.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task GetUserStatistics_AsAdmin_ShouldReturnStatistics()
    {
        // Arrange
        await SeedTestUsers(5);
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Admin"));

        // Act
        var response = await Client.GetAsync("/api/users/statistics");

        // Assert
        AssertSuccessResponse(response);
        var statistics = await DeserializeResponse<UserStatisticsDto>(response);
        statistics.Should().NotBeNull();
        statistics!.TotalUsers.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetUserStatistics_AsClient_ShouldReturnForbidden()
    {
        // Arrange
        SetAuthorizationHeader(GenerateTestJwtToken(Guid.NewGuid(), "<EMAIL>", "Client"));

        // Act
        var response = await Client.GetAsync("/api/users/statistics");

        // Assert
        response.ShouldBeForbidden();
    }

    private async Task<User> CreateTestUser(UserRole role = UserRole.Client)
    {
        var email = Email.Create(_faker.Internet.Email());
        var firstName = _faker.Name.FirstName();
        var lastName = _faker.Name.LastName();
        var phoneNumber = PhoneNumber.Create("+33123456789");
        
        var user = new User(email, firstName, lastName, role, phoneNumber);
        
        DbContext.Users.Add(user);
        await DbContext.SaveChangesAsync();
        
        return user;
    }

    private async Task SeedTestUsers(int count = 3)
    {
        var users = new List<User>();
        
        for (int i = 0; i < count; i++)
        {
            var email = Email.Create($"user{i}@test.com");
            var user = new User(email, $"User{i}", $"Test{i}", UserRole.Client);
            users.Add(user);
        }
        
        DbContext.Users.AddRange(users);
        await DbContext.SaveChangesAsync();
    }
}

// DTOs pour les tests (normalement ces DTOs seraient dans le projet Application)
public class CreateUserRequestDto
{
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Password { get; set; }
}

public class UpdateUserRequestDto
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
}

public class ChangePasswordRequestDto
{
    public string CurrentPassword { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}

public class UserStatisticsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int InactiveUsers { get; set; }
    public Dictionary<string, int> UsersByRole { get; set; } = new();
}
