import React from 'react'
import { <PERSON>, <PERSON>ting<PERSON>, BarChart3, Shield, ArrowLeft } from 'lucide-react'
import { UsersList } from '../components/admin/UsersList'
import { Button } from '../components/ui/Button'
import { useAuth } from '../contexts/AuthContext'
import { type User } from '../types/auth'

type TabType = 'users' | 'settings' | 'analytics'

interface AdminPageProps {
  onBack?: () => void
}

export const AdminPage: React.FC<AdminPageProps> = ({ onBack }) => {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = React.useState<TabType>('users')
  const [selectedUser, setSelectedUser] = React.useState<User | null>(null)

  // Vérifier que l'utilisateur est admin
  if (user?.role !== 'Admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white shadow rounded-lg p-8 max-w-md w-full text-center">
          <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600 mb-6">
            Vous n'avez pas les permissions nécessaires pour accéder à cette page.
          </p>
          {onBack && (
            <Button onClick={onBack} leftIcon={<ArrowLeft className="h-4 w-4" />}>
              Retour
            </Button>
          )}
        </div>
      </div>
    )
  }

  const tabs = [
    {
      id: 'users' as TabType,
      label: 'Utilisateurs',
      icon: Users,
      description: 'Gérer les comptes utilisateurs',
    },
    {
      id: 'settings' as TabType,
      label: 'Paramètres',
      icon: Settings,
      description: 'Configuration du système',
    },
    {
      id: 'analytics' as TabType,
      label: 'Statistiques',
      icon: BarChart3,
      description: 'Analyses et rapports',
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Administration
                </h1>
                <p className="text-sm text-gray-500">
                  Panneau de contrôle ServiceLink
                </p>
              </div>
            </div>
            {onBack && (
              <Button
                variant="outline"
                onClick={onBack}
                leftIcon={<ArrowLeft className="h-4 w-4" />}
              >
                Retour au dashboard
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors
                      ${isActive
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-50'
                      }
                    `}
                  >
                    <Icon
                      className={`
                        h-5 w-5 mr-3
                        ${isActive ? 'text-blue-600' : 'text-gray-400'}
                      `}
                    />
                    <div>
                      <div className="font-medium">{tab.label}</div>
                      <div className="text-xs text-gray-500 mt-0.5">
                        {tab.description}
                      </div>
                    </div>
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {activeTab === 'users' && (
              <UsersList onUserSelect={setSelectedUser} />
            )}
            
            {activeTab === 'settings' && (
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Paramètres du système
                </h2>
                <p className="text-gray-600">
                  Cette section sera développée dans une prochaine version.
                </p>
              </div>
            )}
            
            {activeTab === 'analytics' && (
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Statistiques et analyses
                </h2>
                <p className="text-gray-600">
                  Cette section sera développée dans une prochaine version.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal pour les détails utilisateur */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Détails de l'utilisateur
            </h3>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Nom :</span>
                <span className="ml-2 text-sm text-gray-900">
                  {selectedUser.firstName} {selectedUser.lastName}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Email :</span>
                <span className="ml-2 text-sm text-gray-900">
                  {selectedUser.email}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Rôle :</span>
                <span className="ml-2 text-sm text-gray-900">
                  {selectedUser.role}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Statut :</span>
                <span className="ml-2 text-sm text-gray-900">
                  {selectedUser.isEmailConfirmed ? 'Email confirmé' : 'Email non confirmé'}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Inscription :</span>
                <span className="ml-2 text-sm text-gray-900">
                  {new Date(selectedUser.createdAt).toLocaleDateString('fr-FR')}
                </span>
              </div>
            </div>
            <div className="mt-6 flex justify-end">
              <Button
                variant="outline"
                onClick={() => setSelectedUser(null)}
              >
                Fermer
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
