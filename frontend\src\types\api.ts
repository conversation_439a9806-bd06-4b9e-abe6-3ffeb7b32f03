export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasPreviousPage: boolean
  hasNextPage: boolean
}

export interface ApiError {
  message: string
  errors?: Record<string, string[]>
  statusCode: number
  traceId?: string
}

export interface PaginationParams {
  pageNumber?: number
  pageSize?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
  search?: string
}

export interface HealthCheckResponse {
  status: 'Healthy' | 'Unhealthy' | 'Degraded'
  timestamp: string
  version?: string
  environment?: string
  dependencies?: {
    name: string
    status: 'Healthy' | 'Unhealthy'
    responseTime?: number
  }[]
}
