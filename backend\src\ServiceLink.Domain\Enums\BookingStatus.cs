namespace ServiceLink.Domain.Enums;

/// <summary>
/// Énumération des statuts de réservation dans ServiceLink
/// Définit le cycle de vie d'une réservation de service
/// </summary>
public enum BookingStatus
{
    /// <summary>
    /// En attente - Réservation créée, en attente de confirmation du prestataire
    /// </summary>
    Pending = 1,

    /// <summary>
    /// Confirmée - Prestataire a accepté la réservation
    /// </summary>
    Confirmed = 2,

    /// <summary>
    /// En cours - Service en cours de réalisation
    /// </summary>
    InProgress = 3,

    /// <summary>
    /// Terminée - Service réalisé avec succès
    /// </summary>
    Completed = 4,

    /// <summary>
    /// Annulée - Réservation annulée par le client ou le prestataire
    /// </summary>
    Cancelled = 5,

    /// <summary>
    /// En litige - Problème signalé, nécessite intervention
    /// </summary>
    Disputed = 6,

    /// <summary>
    /// Refusée - Prestataire a refusé la réservation
    /// </summary>
    Rejected = 7,

    /// <summary>
    /// Expirée - Réservation expirée sans confirmation
    /// </summary>
    Expired = 8,

    /// <summary>
    /// Remboursée - Réservation annulée avec remboursement
    /// </summary>
    Refunded = 9
}

/// <summary>
/// Extensions pour l'énumération BookingStatus
/// </summary>
public static class BookingStatusExtensions
{
    /// <summary>
    /// Vérifie si le statut permet la modification de la réservation
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si la réservation peut être modifiée</returns>
    public static bool CanBeModified(this BookingStatus status)
    {
        return status is BookingStatus.Pending or BookingStatus.Confirmed;
    }

    /// <summary>
    /// Vérifie si le statut permet l'annulation
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si la réservation peut être annulée</returns>
    public static bool CanBeCancelled(this BookingStatus status)
    {
        return status is BookingStatus.Pending or BookingStatus.Confirmed;
    }

    /// <summary>
    /// Vérifie si le statut est final (ne peut plus changer)
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si le statut est final</returns>
    public static bool IsFinal(this BookingStatus status)
    {
        return status is BookingStatus.Completed or BookingStatus.Cancelled 
                      or BookingStatus.Rejected or BookingStatus.Expired 
                      or BookingStatus.Refunded;
    }

    /// <summary>
    /// Vérifie si le statut indique une réservation active
    /// </summary>
    /// <param name="status">Le statut à vérifier</param>
    /// <returns>True si la réservation est active</returns>
    public static bool IsActive(this BookingStatus status)
    {
        return status is BookingStatus.Pending or BookingStatus.Confirmed or BookingStatus.InProgress;
    }

    /// <summary>
    /// Obtient la description du statut
    /// </summary>
    /// <param name="status">Le statut</param>
    /// <returns>Description du statut</returns>
    public static string GetDescription(this BookingStatus status)
    {
        return status switch
        {
            BookingStatus.Pending => "En attente de confirmation",
            BookingStatus.Confirmed => "Confirmée par le prestataire",
            BookingStatus.InProgress => "Service en cours",
            BookingStatus.Completed => "Service terminé",
            BookingStatus.Cancelled => "Annulée",
            BookingStatus.Disputed => "En litige",
            BookingStatus.Rejected => "Refusée par le prestataire",
            BookingStatus.Expired => "Expirée",
            BookingStatus.Refunded => "Remboursée",
            _ => "Statut inconnu"
        };
    }

    /// <summary>
    /// Obtient les transitions possibles depuis ce statut
    /// </summary>
    /// <param name="status">Le statut actuel</param>
    /// <returns>Liste des statuts possibles</returns>
    public static IEnumerable<BookingStatus> GetPossibleTransitions(this BookingStatus status)
    {
        return status switch
        {
            BookingStatus.Pending => new[]
            {
                BookingStatus.Confirmed,
                BookingStatus.Rejected,
                BookingStatus.Cancelled,
                BookingStatus.Expired
            },
            BookingStatus.Confirmed => new[]
            {
                BookingStatus.InProgress,
                BookingStatus.Cancelled,
                BookingStatus.Disputed
            },
            BookingStatus.InProgress => new[]
            {
                BookingStatus.Completed,
                BookingStatus.Disputed,
                BookingStatus.Cancelled
            },
            BookingStatus.Disputed => new[]
            {
                BookingStatus.Completed,
                BookingStatus.Cancelled,
                BookingStatus.Refunded
            },
            _ => Array.Empty<BookingStatus>()
        };
    }

    /// <summary>
    /// Vérifie si une transition est valide
    /// </summary>
    /// <param name="from">Statut de départ</param>
    /// <param name="to">Statut d'arrivée</param>
    /// <returns>True si la transition est valide</returns>
    public static bool IsValidTransition(this BookingStatus from, BookingStatus to)
    {
        return from.GetPossibleTransitions().Contains(to);
    }

    /// <summary>
    /// Obtient la couleur associée au statut (pour l'UI)
    /// </summary>
    /// <param name="status">Le statut</param>
    /// <returns>Code couleur hexadécimal</returns>
    public static string GetColor(this BookingStatus status)
    {
        return status switch
        {
            BookingStatus.Pending => "#FFA500",      // Orange
            BookingStatus.Confirmed => "#0066CC",    // Bleu
            BookingStatus.InProgress => "#9966CC",   // Violet
            BookingStatus.Completed => "#22C55E",    // Vert
            BookingStatus.Cancelled => "#6B7280",    // Gris
            BookingStatus.Disputed => "#EF4444",     // Rouge
            BookingStatus.Rejected => "#DC2626",     // Rouge foncé
            BookingStatus.Expired => "#78716C",      // Gris foncé
            BookingStatus.Refunded => "#059669",     // Vert foncé
            _ => "#6B7280"                           // Gris par défaut
        };
    }

    /// <summary>
    /// Obtient l'icône associée au statut
    /// </summary>
    /// <param name="status">Le statut</param>
    /// <returns>Nom de l'icône</returns>
    public static string GetIcon(this BookingStatus status)
    {
        return status switch
        {
            BookingStatus.Pending => "clock",
            BookingStatus.Confirmed => "check-circle",
            BookingStatus.InProgress => "play-circle",
            BookingStatus.Completed => "check-circle-2",
            BookingStatus.Cancelled => "x-circle",
            BookingStatus.Disputed => "alert-triangle",
            BookingStatus.Rejected => "x-octagon",
            BookingStatus.Expired => "clock-x",
            BookingStatus.Refunded => "arrow-left-circle",
            _ => "help-circle"
        };
    }
}
