[{"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "ConfirmEmail", "RelativePath": "api/Auth/confirm-email", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.ConfirmEmailRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/Auth/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.RefreshTokenResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "ResendConfirmationEmail", "RelativePath": "api/Auth/resend-confirmation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "GetUsers", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "role", "Type": "System.String", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "isEmailConfirmed", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortOrder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserListResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "GetUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "UpdateUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "ServiceLink.Application.DTOs.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "ActivateUser", "RelativePath": "api/Users/<USER>/activate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "ChangePassword", "RelativePath": "api/Users/<USER>/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "ServiceLink.Application.DTOs.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "DeactivateUser", "RelativePath": "api/Users/<USER>/deactivate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "ServiceLink.API.Controllers.DeactivateUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "DisableTwoFactor", "RelativePath": "api/Users/<USER>/disable-2fa", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "ServiceLink.API.Controllers.DisableTwoFactorRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "EnableTwoFactor", "RelativePath": "api/Users/<USER>/enable-2fa", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "GetCurrentUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "CreateUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ServiceLink.Application.DTOs.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.UsersController", "Method": "GetUserStatistics", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceLink.Application.DTOs.UserStatisticsResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceLink.API.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ServiceLink.API.WeatherForecast, ServiceLink.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]