{"Version": 1, "WorkspaceRootPath": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}|src\\ServiceLink.API\\ServiceLink.API.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}|src\\ServiceLink.API\\ServiceLink.API.csproj|solutionrelative:src\\servicelink.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.application\\dtos\\authdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|solutionrelative:src\\servicelink.application\\dtos\\authdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.application\\handlers\\createusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|solutionrelative:src\\servicelink.application\\handlers\\createusercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.application\\commands\\usercommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|solutionrelative:src\\servicelink.application\\commands\\usercommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}|src\\ServiceLink.API\\ServiceLink.API.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.api\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}|src\\ServiceLink.API\\ServiceLink.API.csproj|solutionrelative:src\\servicelink.api\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}|src\\ServiceLink.Infrastructure\\ServiceLink.Infrastructure.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.infrastructure\\configuration\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A8C67AA4-7F5F-4C10-8AA7-72976269E44A}|src\\ServiceLink.Infrastructure\\ServiceLink.Infrastructure.csproj|solutionrelative:src\\servicelink.infrastructure\\configuration\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.application\\commands\\authcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|solutionrelative:src\\servicelink.application\\commands\\authcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.application\\dtos\\userdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|solutionrelative:src\\servicelink.application\\dtos\\userdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}|src\\ServiceLink.API\\ServiceLink.API.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DE8F3481-3FE0-42E7-8C9D-C8B545FE01C0}|src\\ServiceLink.API\\ServiceLink.API.csproj|solutionrelative:src\\servicelink.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|d:\\myprojects\\servicelink\\version2\\backend\\src\\servicelink.application\\validators\\usercommandvalidators.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EF41BA6F-68DC-4F69-A28C-818F9BC12D64}|src\\ServiceLink.Application\\ServiceLink.Application.csproj|solutionrelative:src\\servicelink.application\\validators\\usercommandvalidators.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Document", "DocumentIndex": 7, "Title": "UserDtos.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\DTOs\\UserDtos.cs", "RelativeDocumentMoniker": "src\\ServiceLink.Application\\DTOs\\UserDtos.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\DTOs\\UserDtos.cs", "RelativeToolTip": "src\\ServiceLink.Application\\DTOs\\UserDtos.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAuwD0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T18:31:09.24Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AuthDtos.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\DTOs\\AuthDtos.cs", "RelativeDocumentMoniker": "src\\ServiceLink.Application\\DTOs\\AuthDtos.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\DTOs\\AuthDtos.cs", "RelativeToolTip": "src\\ServiceLink.Application\\DTOs\\AuthDtos.cs", "ViewState": "AgIAAF0AAAAAAAAAAAAEwKQAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T18:30:44.618Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AuthCommands.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Commands\\AuthCommands.cs", "RelativeDocumentMoniker": "src\\ServiceLink.Application\\Commands\\AuthCommands.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Commands\\AuthCommands.cs", "RelativeToolTip": "src\\ServiceLink.Application\\Commands\\AuthCommands.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAALgAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T18:28:54.215Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UserCommands.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Commands\\UserCommands.cs", "RelativeDocumentMoniker": "src\\ServiceLink.Application\\Commands\\UserCommands.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Commands\\UserCommands.cs", "RelativeToolTip": "src\\ServiceLink.Application\\Commands\\UserCommands.cs", "ViewState": "AgIAAEYAAAAAAAAAAAAcwAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T18:28:33.23Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "AuthController.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.API\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "src\\ServiceLink.API\\Controllers\\AuthController.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.API\\Controllers\\AuthController.cs", "RelativeToolTip": "src\\ServiceLink.API\\Controllers\\AuthController.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAAwEAAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T18:27:12.074Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\ServiceLink.API\\appsettings.Development.json", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.API\\appsettings.Development.json", "RelativeToolTip": "src\\ServiceLink.API\\appsettings.Development.json", "ViewState": "AgIAAAkAAAAAAAAAAAAAABsAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T22:43:52.641Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "CreateUserCommandHandler.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Handlers\\CreateUserCommandHandler.cs", "RelativeDocumentMoniker": "src\\ServiceLink.Application\\Handlers\\CreateUserCommandHandler.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Handlers\\CreateUserCommandHandler.cs", "RelativeToolTip": "src\\ServiceLink.Application\\Handlers\\CreateUserCommandHandler.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAEwA8AAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T22:41:27.919Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "UserCommandValidators.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Validators\\UserCommandValidators.cs", "RelativeDocumentMoniker": "src\\ServiceLink.Application\\Validators\\UserCommandValidators.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Application\\Validators\\UserCommandValidators.cs", "RelativeToolTip": "src\\ServiceLink.Application\\Validators\\UserCommandValidators.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAqwAwAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T22:40:02.628Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "UsersController.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.API\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "src\\ServiceLink.API\\Controllers\\UsersController.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.API\\Controllers\\UsersController.cs", "RelativeToolTip": "src\\ServiceLink.API\\Controllers\\UsersController.cs", "ViewState": "AgIAAH8AAAAAAAAAAAAAAIYAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T22:36:47.295Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UserConfiguration.cs", "DocumentMoniker": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\Configuration\\UserConfiguration.cs", "RelativeDocumentMoniker": "src\\ServiceLink.Infrastructure\\Configuration\\UserConfiguration.cs", "ToolTip": "D:\\MyProjects\\ServiceLink\\Version2\\backend\\src\\ServiceLink.Infrastructure\\Configuration\\UserConfiguration.cs", "RelativeToolTip": "src\\ServiceLink.Infrastructure\\Configuration\\UserConfiguration.cs", "ViewState": "AgIAAPoAAAAAAAAAAAAAAAIBAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T21:50:17.698Z", "EditorCaption": ""}]}]}]}